package top.continew.admin;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import top.continew.admin.biz.model.entity.SalesPersonnelMonthlyDataDO;
import top.continew.admin.biz.service.SalesPersonnelMonthlyDataService;
import top.continew.admin.system.enums.JobRankEnum;

import java.time.Month;
import java.time.YearMonth;
import java.util.List;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/17 17:26
 */
@SpringBootTest
public class BusinessManageJobTest {
    @Resource
    private SalesPersonnelMonthlyDataService salesPersonnelMonthlyDataService;

    @Test
    public void test() {
        YearMonth month  = YearMonth.of(2025, Month.AUGUST);
        List<SalesPersonnelMonthlyDataDO> list = salesPersonnelMonthlyDataService.listByUserIds(List.of(667322803787383450L), JobRankEnum.TRIAL, month);
        System.out.println(list);
    }
}
