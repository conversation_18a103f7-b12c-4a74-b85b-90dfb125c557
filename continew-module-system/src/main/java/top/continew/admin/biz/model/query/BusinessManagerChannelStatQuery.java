package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * BM渠道分析查询条件
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
@Data
@Schema(description = "BM渠道分析查询条件")
public class BusinessManagerChannelStatQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] statisticsDate;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * BM类型
     */
    @NotNull(message = "BM类型不能为空")
    private Long bmType;


    private @Min(
            value = 1L,
            message = "页码最小值为 {value}"
    ) Integer page = 1;
    @Schema(
            description = "每页条数",
            example = "10"
    )
    private @Range(
            min = 1L,
            max = 1000L,
            message = "每页条数（取值范围 {min}-{max}）"
    ) Integer size = 10;
}