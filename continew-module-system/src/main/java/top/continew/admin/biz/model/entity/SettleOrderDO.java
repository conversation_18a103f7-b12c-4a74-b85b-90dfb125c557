package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 结算订单实体
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Data
@TableName("biz_settle_order")
public class SettleOrderDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    private Long customerId;

    /**
     * 总消耗
     */
    private BigDecimal totalSpent;

    /**
     * 结算消耗
     */
    private BigDecimal settleSpent;

    /**
     * 结算时间
     */
    private LocalDateTime settleTime;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT)
    private Long createUser;
}