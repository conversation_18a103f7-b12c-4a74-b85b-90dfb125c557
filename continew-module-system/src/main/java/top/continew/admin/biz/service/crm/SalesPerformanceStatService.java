package top.continew.admin.biz.service.crm;

import top.continew.admin.biz.model.query.PerformanceStatisticsQuery;
import top.continew.admin.biz.model.query.crm.SalesPerformanceStatQuery;
import top.continew.admin.biz.model.resp.BusinessPerformanceStatisticsResp;
import top.continew.admin.biz.model.resp.SalesDataSummaryResp;
import top.continew.admin.biz.model.resp.SalesPersonnelMonthlyDataResp;
import top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商务业绩统计 Service
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface SalesPerformanceStatService {

    /**
     * 查询商务业绩统计
     *
     * @param query 查询条件
     * @return 商务业绩统计列表
     */
    List<SalesPerformanceStatResp> listSalesPerformanceStat(SalesPerformanceStatQuery query);

    List<SalesPersonnelMonthlyDataResp> listBusinessPerformanceStat(PerformanceStatisticsQuery query);

    SalesDataSummaryResp getSalesDataSummary(LocalDateTime startTime, LocalDateTime endTime);

}