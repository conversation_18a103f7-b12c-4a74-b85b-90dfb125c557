package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CustomerSpentStatisticsResp {

    /**
     * 总下户数
     * 该客户下属的所有子账户总数。
     */
    @Schema(description = "总下户数")
    private Integer totalSubAccounts;

    /**
     * 广告使用数
     * 正在使用广告服务的子账户数量。
     */
    @Schema(description = "广告使用数")
    private Integer adUsageCount;

    /**
     * 不消耗数
     * 在统计周期内，没有任何广告消耗的子账户数量。
     */
    @Schema(description = "不消耗数")
    private Integer nonSpendingCount;

    /**
     * 低消耗数
     * 在统计周期内，广告消耗低于预设阈值的子账户数量。
     */
    @Schema(description = "低消耗数")
    private Integer lowSpendingCount;

    /**
     * 正常消耗数
     * 在统计周期内，广告消耗正常的子账户数量。
     */
    @Schema(description = "正常消耗数")
    private Integer normalSpendingCount;

}
