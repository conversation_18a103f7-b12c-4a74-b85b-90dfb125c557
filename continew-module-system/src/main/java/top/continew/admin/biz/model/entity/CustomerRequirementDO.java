package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.CustomerRequirementStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 客户需求实体
 *
 * <AUTHOR>
 * @since 2025/02/20 14:03
 */
@Data
@TableName("biz_customer_requirement")
public class CustomerRequirementDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 需求时间
     */
    private LocalDateTime requirementTime;

    /**
     * 需求数量
     */
    private Integer quantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 接单时间
     */
    private LocalDateTime acceptTime;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 状态:待接单，处理中，已邀请，已完成，取消
     */
    private CustomerRequirementStatusEnum status;

    /**
     * 完成数量
     */
    private Integer finishQuantity;



    /**
     * bm类型
     */
    private Long bmType;

    /**
     * 客户BM ID
     */
    private String customerBmId;

    /**
     * 开户费
     */
    private BigDecimal payAmount;

    /**
     * 广告户名称
     */
    private String adAccountName;


    private String customerEmail;
}