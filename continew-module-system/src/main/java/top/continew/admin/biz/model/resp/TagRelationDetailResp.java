package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 标签关联详情信息
 *
 * <AUTHOR>
 * @since 2025/05/08 16:37
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "标签关联详情信息")
public class TagRelationDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @Schema(description = "标签ID")
    @ExcelProperty(value = "标签ID")
    private Long tagId;

    /**
     * 类型(1=广告户 2=下户订单)
     */
    @Schema(description = "类型(1=广告户 2=下户订单)")
    @ExcelProperty(value = "类型(1=广告户 2=下户订单)")
    private Integer type;

    /**
     * 关联ID
     */
    @Schema(description = "关联ID")
    @ExcelProperty(value = "关联ID")
    private Long relationId;
}