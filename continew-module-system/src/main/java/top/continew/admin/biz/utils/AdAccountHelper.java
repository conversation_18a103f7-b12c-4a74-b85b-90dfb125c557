package top.continew.admin.biz.utils;

import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class AdAccountHelper {

    private static final ConcurrentHashMap<String, BigDecimal> adAccountPrepayLastBalanceMap = new ConcurrentHashMap<>();

    public void setAdAccountLastPrepayBalance(String platformAdAId, BigDecimal adAccountPrepayLastBalance) {
        adAccountPrepayLastBalanceMap.put(platformAdAId, adAccountPrepayLastBalance);
    }

    public BigDecimal getAdAccountLastPrepayBalance(String platformAdAId) {
        return adAccountPrepayLastBalanceMap.get(platformAdAId);
    }
}
