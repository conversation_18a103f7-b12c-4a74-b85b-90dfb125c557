/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.*;
import java.util.List;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.*;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 广告账号查询条件
 *
 * <AUTHOR>
 * @since 2025/01/02 13:54
 */
@Data
@Schema(description = "广告账号查询条件")
public class AdAccountQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联BM5 ID
     */
    @Schema(description = "关联BM5 ID")
    @Query(type = QueryType.IN, columns = {"bm_id"})
    private List<String> bmIds;
    /**
     * 平台广告ID
     */
    @Schema(description = "平台广告ID")
    @Query(type = QueryType.EQ)
    private List<String> platformAdIds;

    /**
     * 完整卡号
     */
    @Schema(description = "完整卡号")
    @Query(type = QueryType.EQ)
    private String fullCardNumber;

    /**
     * 时区
     */
    @Schema(description = "时区")
    @Query(type = QueryType.EQ)
    private String timezone;

    /**
     * 账号状态
     */
    @Schema(description = "账号状态")
    @Query(type = QueryType.EQ)
    private List<AdAccountStatusEnum> accountStatus;

    /**
     * 养号状态
     */
    @Schema(description = "养号状态")
    @Query(type = QueryType.EQ)
    private List<AdAccountKeepStatusEnum> keepStatus;

    /**
     * 出售状态
     */
    @Schema(description = "出售状态")
    @Query(type = QueryType.EQ)
    private Integer saleStatus;

    private AdAccountAppealStatusEnum appealStatus;

    /**
     * 个号地区
     */
    @Schema(description = "个号地区")
    @Query(type = QueryType.EQ)
    private String personalArea;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    @Query(type = QueryType.LIKE)
    private String browserNo;

    /**
     * 清零状态
     */
    @Schema(description = "清零状态")
    @Query(type = QueryType.EQ)
    private Integer clearStatus;

    private BigDecimal firstSpend;

    private BigDecimal lastSpend;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    private List<Long> createUser;

    private List<Long> ids;

    private String tag;

    private Long deptId;

    private Long fbChannelId;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] bmAuthTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] banTime;

    private String remark;

    private String bm1Browser;

    private Long bm1Id;

    private Boolean isRemoveAdmin;

    private Boolean isLowLimit;

    private List<Long> bmItemType;

    private Integer voStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] saleTime;

    private List<Long> tags;

    private String channelName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] updateTime;


    /**
     * 是否可用
     */
    private Boolean usable;


    /**
     * 不可用原因
     */
    @Schema(description = "不可用原因")
    @Query(type = QueryType.EQ)
    private List<AdAccountUnusableReasonEnum> unusableReason;
}