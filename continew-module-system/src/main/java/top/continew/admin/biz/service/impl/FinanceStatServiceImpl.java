/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.mapper.FinanceStatMapper;
import top.continew.admin.biz.model.entity.CustomerBalanceRecordDO;
import top.continew.admin.biz.model.query.AdAccountInsightQuery;
import top.continew.admin.biz.model.query.AdAccountRetentionQuery;
import top.continew.admin.biz.model.query.CustomerStatReportQuery;
import top.continew.admin.biz.model.req.CustomerDailyReportImportRowReq;
import top.continew.admin.biz.model.resp.AdAccountDailyStatReportResp;
import top.continew.admin.biz.model.resp.AdAccountRetentionResp;
import top.continew.admin.biz.model.resp.AdAccountStatResp;
import top.continew.admin.biz.model.resp.DailyStatReportResp;
import top.continew.admin.biz.service.CustomerBalanceRecordService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.FinanceStatService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceStatServiceImpl implements FinanceStatService {

    private final CustomerService customerService;

    private final CustomerBalanceRecordService customerBalanceRecordService;

    private final FinanceStatMapper financeStatMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importDailyReport(Long customerId, MultipartFile file) {
        boolean exist = customerBalanceRecordService.exists(Wrappers.<CustomerBalanceRecordDO>lambdaQuery()
            .eq(CustomerBalanceRecordDO::getCustomerId, customerId));
        CheckUtils.throwIf(exist, "该客户已存在流水记录，无法导入日报");
        List<CustomerDailyReportImportRowReq> importRowList;
        // 读取表格数据
        try {
            importRowList = EasyExcel.read(file.getInputStream())
                .head(CustomerDailyReportImportRowReq.class)
                .sheet("日报")
                .headRowNumber(3)
                .doReadSync();
        } catch (Exception e) {
            log.error("客户日报数据文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("数据文件解析异常");
        }
        for (CustomerDailyReportImportRowReq dailyReportImportRowReq : importRowList) {
            // 广告户ID为空的代表是客户余额流水
            if (StringUtils.isBlank(dailyReportImportRowReq.getDate())) {
                continue;
            }
            LocalDateTime transTime;
            try {
                transTime = LocalDateTimeUtil.parse(dailyReportImportRowReq.getDate() + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
            } catch (DateTimeParseException e) {
                transTime = LocalDateTimeUtil.parse(dailyReportImportRowReq.getDate() + " 00:00:00", "yyyy-M-d HH:mm:ss");
            }
            String remark = "【旧数据导入】" + (dailyReportImportRowReq.getRemark() == null
                ? ""
                : dailyReportImportRowReq.getRemark());
            if (dailyReportImportRowReq.getTransferAmount() != null) {
                customerService.changeAmount(customerId, dailyReportImportRowReq.getTransferAmount(), CustomerBalanceTypeEnum.SYSTEM, transTime, remark);
                if (dailyReportImportRowReq.getFee() != null) {
                    customerService.changeAmount(customerId, dailyReportImportRowReq.getFee()
                        .negate(), CustomerBalanceTypeEnum.RECHARGE_FEE, transTime, remark);
                }
            }
            if (dailyReportImportRowReq.getRefundAmount() != null) {
                customerService.changeAmount(customerId, dailyReportImportRowReq.getRefundAmount()
                    .abs()
                    .negate(), CustomerBalanceTypeEnum.DEDUCE, transTime, remark);
            }
            if (StringUtils.isNotBlank(dailyReportImportRowReq.getPlatformAdId())) {

                String adPlatformId = dailyReportImportRowReq.getPlatformAdId().trim();
                // 充值金额大于0代表充值，小于0代表减款
                if (dailyReportImportRowReq.getAdAccountRechargeAmount() != null) {
                    boolean isRecharge = dailyReportImportRowReq.getAdAccountRechargeAmount()
                        .compareTo(BigDecimal.ZERO) > 0;
                    customerService.changeAmount(customerId, dailyReportImportRowReq.getPlatformAdId(), isRecharge
                        ? dailyReportImportRowReq.getAdAccountRechargeAmount().negate()
                        : dailyReportImportRowReq.getAdAccountRechargeAmount().abs(), isRecharge
                        ? CustomerBalanceTypeEnum.AD_ACCOUNT_RECHARGE
                        : CustomerBalanceTypeEnum.AD_ACCOUNT_REDUCE, transTime, remark);
                }

                // 开户费
                if (dailyReportImportRowReq.getBuyAdAccountAmount() != null) {
                    customerService.changeAmount(customerId, dailyReportImportRowReq.getPlatformAdId(), dailyReportImportRowReq.getBuyAdAccountAmount()
                        .negate(), CustomerBalanceTypeEnum.AD_ACCOUNT_BUY, transTime, remark);
                }

                // 取款金额
                if (dailyReportImportRowReq.getWithdrawAmount() != null) {
                    boolean isClear = false;
                    if (StringUtils.isNotBlank(dailyReportImportRowReq.getAdStatus())) {
                        if (dailyReportImportRowReq.getAdStatus()
                            .contains("停用") || dailyReportImportRowReq.getAdStatus().contains("封")) {
                            isClear = true;
                        }
                    }
                    customerService.changeAmount(customerId, dailyReportImportRowReq.getPlatformAdId(), dailyReportImportRowReq.getWithdrawAmount()
                        .abs(), isClear
                        ? CustomerBalanceTypeEnum.AD_ACCOUNT_CLEAR
                        : CustomerBalanceTypeEnum.AD_ACCOUNT_REDUCE, transTime, remark);
                }

            }
        }
    }

    @Override
    public BasePageResp<AdAccountStatResp> pageAdAccountStatReport(AdAccountInsightQuery query, PageQuery pageQuery) {
        IPage<AdAccountStatResp> page = financeStatMapper.selectAdAccountStatPage(new Page<>((long)pageQuery.getPage(), (long)pageQuery.getSize()), query);
        page.getRecords().forEach(row -> {
            if (null == row.getDiffAmount()) {
                row.setDiffAmount(row.getCardAmount().subtract(row.getAdAmountDeductions()));
            }
            LocalDateTime adAccountFinishTime;
            if (StringUtils.isNotBlank(row.getTimezone()) && row.getFinishTime() != null) {
                adAccountFinishTime = CommonUtils.convertTimezone("GMT+08:00", row.getTimezone(), row.getFinishTime());
            } else {
                adAccountFinishTime = row.getFinishTime();
            }
            LocalDateTime adAccountRecycleTime;
            if (StringUtils.isNotBlank(row.getTimezone()) && row.getRecycleTime() != null) {
                adAccountRecycleTime = CommonUtils.convertTimezone("GMT+08:00", row.getTimezone(), row.getRecycleTime());
            } else {
                adAccountRecycleTime = row.getRecycleTime();
            }
            row.setAdAccountFinishTime(adAccountFinishTime);
            row.setAdAccountRecycleTime(adAccountRecycleTime);

        });

        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public BasePageResp<AdAccountDailyStatReportResp> pageAdAccountDailyStatReport(AdAccountInsightQuery query,
                                                                                   PageQuery pageQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        IPage<AdAccountDailyStatReportResp> page = financeStatMapper.selectAdAccountDailyStatReport(new Page<>((long)pageQuery.getPage(), (long)pageQuery.getSize()), query.getAdAccountIds()
            .get(0), start, end);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public PageResp<DailyStatReportResp> selectDailyStatReportPage(CustomerStatReportQuery query, PageQuery pageQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        IPage<DailyStatReportResp> page = financeStatMapper.selectDailyStatReportPage(new Page<>((long)pageQuery.getPage(), (long)pageQuery.getSize()), start, end, query.getCustomerId());
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<DailyStatReportResp> selectDailyStatReportList(CustomerStatReportQuery query) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        return financeStatMapper.selectDailyStatReportList(start, end, query.getCustomerId());
    }

    @Override
    public BasePageResp<AdAccountRetentionResp> pageAdAccountRetention(AdAccountRetentionQuery query,
                                                                       PageQuery pageQuery) {
        IPage<AdAccountRetentionResp> page = financeStatMapper.selectAdAccountRetentionPage(new Page<>((long)pageQuery.getPage(), (long)pageQuery.getSize()), query);

        LocalDateTime now = LocalDateTime.now();
        // 计算实际留存数
        page.getRecords().forEach(item -> {
            LocalDateTime itemDate = LocalDateTime.of(item.getDate(), LocalDateTime.MIN.toLocalTime());

            // 如果是今天的数据，所有留存数据都置空
            if (itemDate.toLocalDate().equals(now.toLocalDate())) {
                item.setRetention1d(null);
                item.setRetention7d(null);
                item.setRetention15d(null);
                item.setRetention30d(null);
                return;
            }

            // 计算日期差
            long daysBetween = ChronoUnit.DAYS.between(itemDate.toLocalDate(), now.toLocalDate());

            // 设置各时间段的留存数据
            if (daysBetween >= 1) {
                item.setRetention1d(item.getCount() - item.getBanCount1d());
            } else {
                item.setRetention1d(null);
            }

            if (daysBetween >= 7) {
                item.setRetention7d(item.getCount() - item.getBanCount7d());
            } else {
                item.setRetention7d(null);
            }

            if (daysBetween >= 15) {
                item.setRetention15d(item.getCount() - item.getBanCount15d());
            } else {
                item.setRetention15d(null);
            }

            if (daysBetween >= 30) {
                item.setRetention30d(item.getCount() - item.getBanCount30d());
            } else {
                item.setRetention30d(null);
            }
        });

        return new PageResp<>(page.getRecords(), page.getTotal());
    }

}
