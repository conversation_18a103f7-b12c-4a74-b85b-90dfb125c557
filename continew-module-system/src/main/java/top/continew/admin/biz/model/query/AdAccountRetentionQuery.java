package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@Schema(description = "广告账号查询条件")
public class AdAccountRetentionQuery {
    private Long customerId;
    private Long bmChannel;
    private String timezone;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] finishTime;
}