package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 中介返点实体
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Data
@TableName("biz_agent_rebate")
public class AgentRebateDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联中介
     */
    private Long agentId;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 结算月份
     */
    private String settleMonth;

    /**
     * 结算金额
     */
    private BigDecimal settleAmount;

    /**
     * 开户费
     */
    private BigDecimal accountOpenAmount;

    /**
     * 返点日期
     */
    private LocalDateTime rebateTime;

    /**
     * 返点金额
     */
    private BigDecimal rebateAmount;

    private BigDecimal actualRebateAmount;

    /**
     * 返点规则
     */
    private String rebateRule;

    /**
     * 备注
     */
    private String remark;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;
}