package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.admin.common.base.BaseDetailResp;

/**
 * 交易对象详情信息
 *
 * <AUTHOR>
 * @since 2025/08/17 14:22
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "交易对象详情信息")
public class TransactionUserDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @ExcelProperty(value = "交易类型")
    private TransactionUserTypeEnum transactionType;

    /**
     * 关联ID
     */
    @Schema(description = "关联ID")
    @ExcelProperty(value = "关联ID")
    private Long referId;

    /**
     * 名字
     */
    @Schema(description = "名字")
    @ExcelProperty(value = "名字")
    private String name;
}