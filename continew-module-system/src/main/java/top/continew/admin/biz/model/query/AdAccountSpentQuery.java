package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Data
@Schema(description = "广告账户消耗查询参数对象")
public class AdAccountSpentQuery {
    @Schema(description = "客户ID列表。支持传入一个或多个客户ID进行筛选。若不传，则查询所有客户。",
            example = "[1001, 1002]",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<Long> customerIds;
    @Schema(description = "查询日期范围的开始时间。格式：yyyy-MM-dd HH:mm:ss",
            example = "2024-01-01 00:00:00",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @Schema(description = "查询日期范围的结束时间。格式：yyyy-MM-dd HH:mm:ss",
            example = "2024-12-31 23:59:59",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    @Schema(description = "【低消耗定义】广告开启的天数。此条件必须与 lowConsumptionAmount 同时提供才生效。",
            example = "1",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer lowConsumptionDay;
    @Schema(description = "【低消耗定义】消耗金额上限（美元）。此条件必须与 lowConsumptionHours 同时提供才生效。",
            example = "10.00",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal lowConsumptionAmount;

    @Schema(description = "1-普通筛选 2-广告户筛选",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    @Schema(description = "广告户ID（支持空格分隔多个ID）")
    private String adAccountIds;


    public String[] getAdAccountIdsAsArray() {
        if (this.adAccountIds == null || this.adAccountIds.trim().isEmpty()) {
            return new String[0];
        }
        String[] rawIds = this.adAccountIds.split("[\\s,，]+");
        return Arrays.stream(rawIds)
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .toArray(String[]::new);
    }
}