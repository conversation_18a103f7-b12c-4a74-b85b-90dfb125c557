package top.continew.admin.biz.model.query.crm;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 线索查询条件
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "线索查询条件")
public class LeadQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    @Query(type = QueryType.EQ)
    private Long sourceId;

    /**
     * 状态：1-待跟进、2-跟进中、3-长期跟进、4-创建商机、5-无效
     */
    @Schema(description = "状态")
    @Query(type = QueryType.EQ)
    private Integer status;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    @Query(type = QueryType.LIKE)
    private String customerName;

    /**
     * 客户行业
     */
    @Schema(description = "客户行业")
    @Query(type = QueryType.EQ)
    private Integer customerIndustry;

    /**
     * 无效原因
     */
    @Schema(description = "无效原因")
    @Query(type = QueryType.EQ)
    private Integer invalidReason;

    /**
     * 关联商机ID
     */
    @Schema(description = "关联商机ID")
    @Query(type = QueryType.EQ)
    private Long opportunityId;

    /**
     * 对接人
     */
    @Schema(description = "对接人")
    @Query(type = QueryType.EQ)
    private Long handlerUserId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;

    /**
     * 跟进日期
     */
    @Schema(description = "跟进时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] lastFollowTime;

}