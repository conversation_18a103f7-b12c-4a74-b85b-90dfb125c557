package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 结算订单详情详情信息
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "结算订单详情详情信息")
public class SettleOrderItemDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @ExcelProperty(value = "广告户ID")
    private String platformAdId;

    /**
     * 消耗
     */
    @Schema(description = "消耗")
    @ExcelProperty(value = "总消耗")
    private BigDecimal spent;
}