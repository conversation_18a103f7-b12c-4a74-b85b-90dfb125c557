/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import org.apache.commons.lang3.StringUtils;
import top.continew.admin.biz.enums.FbAccountStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * fb账号实体
 *
 * <AUTHOR>
 * @since 2025/01/03 16:32
 */
@Data
@TableName("biz_fb_account")
public class FbAccountDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联账号ID
     */
    private String platformAccountId;

    /**
     * 浏览器编号
     */
    private String browserSerialNo;

    /**
     * 状态（1=正常，2=封禁）
     */
    private FbAccountStatusEnum status;
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime updateTime;

    /**
     *
     */
    private String name;

    /**
     *
     */
    private String tab;

    /**
     *
     */
    private String platform;

    /**
     *
     */
    private String username;

    /**
     *
     */
    private String password;

    /**
     *
     */
    private String fakey;

    /**
     *
     */
    private String cookie;

    /**
     *
     */
    private String proxyType;

    /**
     *
     */
    private String ipChecker;

    /**
     *
     */
    private String proxy;

    /**
     *
     */
    private String proxyUrl;

    /**
     *
     */
    private Integer proxyId;

    /**
     *
     */
    private String ip;

    /**
     *
     */
    private String countryCode;

    /**
     *
     */
    private String regionCode;

    /**
     *
     */
    private String cityCode;

    /**
     *
     */
    private String ua;

    /**
     *
     */
    private String resolution;

    private Long channelId;

    private String tag;

    private String remark;

    private Long ipId;

    private String customRemark;

    private BigDecimal price;

    private String browserId;

    public String getFormatProxy() {
        String proxy = replaceSecondColon(this.getProxy());
        if (StringUtils.isBlank(proxy)) {
            return "";
        }
        return "%s://%s".formatted(this.getProxyType().equals("http") ? "http" : "socks", proxy);
    }

    private static String replaceSecondColon(String str) {
        String[] strs = str.split(":");
        if (strs.length != 4) {
            return "";
        }
        return "%s:%s@%s:%s".formatted(strs[2], strs[3], strs[0], strs[1]);
    }
}