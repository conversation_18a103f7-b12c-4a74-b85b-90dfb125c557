package top.continew.admin.biz.model.query.crm;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 商机查询条件
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "商机查询条件")
public class OpportunityQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户ID
     */
    @Schema(description = "关联客户ID")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    @Query(type = QueryType.EQ)
    private Long sourceId;

    /**
     * 状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失
     */
    @Schema(description = "状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失")
    @Query(type = QueryType.EQ)
    private Integer status;

    /**
     * 需求内容
     */
    @Schema(description = "需求内容")
    @Query(type = QueryType.EQ)
    private String requirement;

    /**
     * 流失原因
     */
    @Schema(description = "流失原因")
    @Query(type = QueryType.EQ)
    private Integer lostReason;

    /**
     * 对接人
     */
    @Schema(description = "对接人")
    @Query(type = QueryType.EQ)
    private Long handlerUserId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Query(type = QueryType.EQ)
    private Long createUser;

    @Schema(description = "跟进时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] lastFollowTime;
}