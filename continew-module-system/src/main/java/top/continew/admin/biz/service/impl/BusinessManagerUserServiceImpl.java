package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.beanutils.WrapDynaBean;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.mapper.BusinessManagerUserMapper;
import top.continew.admin.biz.model.entity.BusinessManagerUserDO;
import top.continew.admin.biz.model.entity.WhiteEmailDO;
import top.continew.admin.biz.model.query.BusinessManagerUserQuery;
import top.continew.admin.biz.model.req.BusinessManagerUserReq;
import top.continew.admin.biz.model.resp.BusinessManagerUserDetailResp;
import top.continew.admin.biz.model.resp.BusinessManagerUserResp;
import top.continew.admin.biz.service.BusinessManagerUserService;
import top.continew.admin.biz.service.WhiteEmailService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

/**
 * bm管理员业务实现
 *
 * <AUTHOR>
 * @since 2025/04/22 16:45
 */
@Service
@RequiredArgsConstructor
public class BusinessManagerUserServiceImpl extends BaseServiceImpl<BusinessManagerUserMapper, BusinessManagerUserDO, BusinessManagerUserResp, BusinessManagerUserDetailResp, BusinessManagerUserQuery, BusinessManagerUserReq> implements BusinessManagerUserService {


    private final WhiteEmailService whiteEmailService;

    @Override
    public PageResp<BusinessManagerUserResp> page(BusinessManagerUserQuery query, PageQuery pageQuery) {
        QueryWrapper<BusinessManagerUserDO> queryWrapper = this.buildQueryWrapper(query);
        if (query.getIsSelfUser() != null) {
            if (query.getIsSelfUser()) {
                queryWrapper.exists("select 1 from biz_white_email pa where pa.email = u.user_email");
            } else {
                queryWrapper.notExists("select 1 from biz_white_email pa where pa.email = u.user_email");
            }
        }
        queryWrapper.eq("bm.status", BusinessManagerStatusEnum.NORMAL);
        this.sort(queryWrapper, pageQuery);
        IPage<BusinessManagerUserResp> page = this.baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public void addWhiteList(String email) {
        boolean exist = whiteEmailService.exists(Wrappers.<WhiteEmailDO>lambdaQuery().eq(WhiteEmailDO::getEmail, email));
        if (!exist) {
            WhiteEmailDO item = new WhiteEmailDO();
            item.setEmail(email);
            whiteEmailService.save(item);
        }
    }
}