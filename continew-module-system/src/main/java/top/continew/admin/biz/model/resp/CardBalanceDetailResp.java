/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.CardBalanceTypeEnum;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

/**
 * 卡片余额流水详情信息
 *
 * <AUTHOR>
 * @since 2024/12/29 13:45
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "卡片余额流水详情信息")
public class CardBalanceDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡台平台
     */
    @Schema(description = "卡台平台")
    @ExcelProperty(value = "卡台平台", converter = ExcelBaseEnumConverter.class)
    private CardPlatformEnum platform;

    /**
     * 卡号
     */
    @Schema(description = "卡号")
    @ExcelProperty(value = "卡号")
    private String cardNumber;

    /**
     * 类型（1=充值，2=提现）
     */
    @Schema(description = "类型（1=充值，2=提现）")
    @ExcelProperty(value = "类型", converter = ExcelBaseEnumConverter.class)
    private CardBalanceTypeEnum type;

    /**
     * 变更金额
     */
    @Schema(description = "变更金额")
    @ExcelProperty(value = "变更金额")
    private BigDecimal amount;

    /**
     * 交易后余额
     */
    @Schema(description = "交易后余额")
    @ExcelProperty(value = "交易后余额")
    private BigDecimal afterAmount;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @ExcelProperty(value = "交易时间")
    private LocalDateTime transTime;

    /**
     * 交易编号
     */
    @Schema(description = "交易编号")
    @ExcelProperty(value = "交易编号")
    private String transactionId;
}