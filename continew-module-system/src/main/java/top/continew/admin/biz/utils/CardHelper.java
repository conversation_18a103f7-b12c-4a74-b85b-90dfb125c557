package top.continew.admin.biz.utils;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.service.CardService;

import java.math.BigDecimal;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/14 09:58
 */
@Component
public class CardHelper implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        CardHelper.applicationContext = context;
    }

    private static CardService getCardService() {
        return applicationContext.getBean(CardService.class);
    }

    public static String getCardNumber(CardPlatformEnum platformEnum, String platformCardId) {
        return getCardService().getCardNumber(platformEnum, platformCardId);
    }

    public static void updateCardBalance(CardPlatformEnum platformEnum, String platformCardId, BigDecimal balance) {
        // 根据卡号更新余额字段
        LambdaUpdateWrapper<CardDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CardDO::getPlatformCardId, platformCardId).eq(CardDO::getPlatform, platformEnum)
                .set(CardDO::getBalance, balance);
        getCardService().update(updateWrapper);

    }
}
