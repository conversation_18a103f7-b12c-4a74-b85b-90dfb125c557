package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.annotation.DictField;
import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 社交账号实体
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@TableName("biz_social_account")
@DictField(labelKey = "account")
public class SocialAccountDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账号类型：telegram、wechat、line、phone
     */
    private Integer accountType;

    /**
     * 账号
     */
    private String account;

    /**
     * 分配人
     */
    private Long assigneeId;

    /**
     * 账号状态：1-启用、2-禁用
     */
    private Integer status;
}