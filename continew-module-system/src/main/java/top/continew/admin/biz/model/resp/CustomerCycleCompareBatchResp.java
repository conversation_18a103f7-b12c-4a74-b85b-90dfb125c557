/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户周期对比批量响应
 *
 * <AUTHOR>
 * @since 2025/1/19 15:30
 */
@Data
@Schema(description = "客户周期对比批量响应")
public class CustomerCycleCompareBatchResp {
    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long customerId;

    /**
     * 周期标识
     */
    @Schema(description = "周期标识")
    private String period;

    /**
     * 总下户数
     */
    @Schema(description = "总下户数")
    private Integer totalAccounts;

    /**
     * 总正常户数
     */
    @Schema(description = "总正常户数")
    private Integer totalNormalAccounts;

    /**
     * 总死户数
     */
    @Schema(description = "总死户数")
    private Integer totalDeadAccounts;

    /**
     * 总回收户数
     */
    @Schema(description = "总回收户数")
    private Integer totalRecycledAccounts;

    /**
     * 总消耗
     */
    @Schema(description = "总消耗")
    private BigDecimal totalSpent;
    
    /**
     * 客户创建时间
     */
    @Schema(description = "客户创建时间")
    private LocalDateTime createTime;
}