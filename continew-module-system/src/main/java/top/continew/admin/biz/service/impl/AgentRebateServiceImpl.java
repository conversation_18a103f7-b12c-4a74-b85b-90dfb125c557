package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.AgentRebateMapper;
import top.continew.admin.biz.model.entity.AgentRebateDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.AgentRebateQuery;
import top.continew.admin.biz.model.req.AgentRebateConfirmReq;
import top.continew.admin.biz.model.req.AgentRebateReq;
import top.continew.admin.biz.model.resp.AgentRebateDetailResp;
import top.continew.admin.biz.model.resp.AgentRebateResp;
import top.continew.admin.biz.service.AgentRebateService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 中介返点业务实现
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Service
@RequiredArgsConstructor
public class AgentRebateServiceImpl extends BaseServiceImpl<AgentRebateMapper, AgentRebateDO, AgentRebateResp, AgentRebateDetailResp, AgentRebateQuery, AgentRebateReq> implements AgentRebateService {

    private final CustomerService customerService;

    @Override
    protected void beforeAdd(AgentRebateReq req) {
        CustomerDO customer = customerService.getById(req.getCustomerId());
        req.setRebateRule(customer.getRebateRule());
    }

    @Override
    public PageResp<AgentRebateResp> page(AgentRebateQuery query, PageQuery pageQuery) {
        QueryWrapper<AgentRebateDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, pageQuery);
        IPage<AgentRebateResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        page.getRecords().forEach(this::fill);
        return PageResp.build(page);
    }

    @Override
    protected <E> List<E> list(AgentRebateQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<AgentRebateDO> queryWrapper = this.buildQueryWrapper(query);
        this.sort(queryWrapper, sortQuery);
        List<AgentRebateDetailResp> entityList = baseMapper.selectCustomList(queryWrapper);
        entityList.forEach(this::fill);
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    public Map<String, BigDecimal> getTotalRebateAmount(AgentRebateQuery query) {
        QueryWrapper<AgentRebateDO> queryWrapper = this.buildQueryWrapper(query);
        List<AgentRebateDetailResp> entityList = baseMapper.selectCustomList(queryWrapper);
        Map<String, BigDecimal> result = new HashMap<>();
        result.put("actualRebateAmount", entityList.stream()
            .map(AgentRebateDetailResp::getActualRebateAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
        result.put("rebateAmount", entityList.stream()
            .map(AgentRebateDetailResp::getRebateAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
        return result;
    }

    @Override
    public void confirmRebate(AgentRebateConfirmReq req) {
        AgentRebateDO update = new AgentRebateDO();
        update.setId(req.getId());
        update.setActualRebateAmount(req.getActualRebateAmount());
        update.setRebateTime(req.getRebateTime());
        this.updateById(update);
    }
}