package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import cn.crane4j.annotation.condition.ConditionOnPropertyNotNull;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;
import top.continew.starter.extension.crud.constant.ContainerPool;

/**
 * 商务人员月度绩效数据信息
 *
 * <AUTHOR>
 * @since 2025/08/20 14:26
 */
@Data
@Schema(description = "商务人员月度绩效数据信息")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SalesPersonnelMonthlyDataResp implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @ConditionOnPropertyNotNull
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "businessName"))
    private Long businessUserId;


    @Schema(description = "商务人员姓名")
    private String businessName;

    /**
     * 入职时间
     */
    @Schema(description = "入职时间")
    private LocalDate entryTime;

    /**
     * 岗位类型
     */
    @Schema(description = "岗位类型")
    private Integer jobRank;

    /**
     * 统计月份
     */
    @Schema(description = "统计月份")
    private LocalDate salesDate;

    /**
     * 微信好友数量
     */
    @Schema(description = "微信好友数量")
    private Integer wechatFriendsCount;

    /**
     * Telegram好友数量
     */
    @Schema(description = "Telegram好友数量")
    private Integer telegramFriendsCount;

    /**
     * 成交客户数量
     */
    @Schema(description = "成交客户数量")
    private Integer closedCustomersCount;

    /**
     * 交友数量
     */
    @Schema(description = "交友数量")
    private Integer friendsCount;

    /**
     * 意向客户数量
     */
    @Schema(description = "意向客户数量")
    private Integer intendedCustomersCount;

    /**
     * 客户总消耗
     */
    @Schema(description = "客户总消耗")
    private BigDecimal totalCustomerSpend;

    /**
     * 广告户使用率
     */
    @Schema(description = "广告户使用率")
    private Double adAccountUsageRate;

    /**
     * 客户单户平均消耗
     */
    @Schema(description = "客户单户平均消耗")
    private BigDecimal avgSingleAccountSpend;

    /**
     * 客户留存率
     */
    @Schema(description = "客户留存率")
    private Double customerRetentionRate;

    /**
     * 日报漏填写数量
     */
    @Schema(description = "日报漏填写数量")
    private Integer dailyReportMissingCount;

    /**
     * 客咨漏填写数量
     */
    @Schema(description = "客咨漏填写数量")
    private Integer customerConsultingMissingCount;

    /**
     * 综合评价
     */
    @Schema(description = "综合评价")
    private Integer comprehensiveEvaluation;

    /**
     * 工作态度
     */
    @Schema(description = "工作态度")
    private Integer workAttitude;

    /**
     * 朋友圈日更
     */
    @Schema(description = "朋友圈日更")
    private Integer momentsDailyUpdateCount;

    /**
     * 进入TG群数量
     */
    @Schema(description = "进入TG群数量")
    private Integer enteredTgGroupsCount;

    /**
     * TG群推广
     */
    @Schema(description = "TG群推广")
    private Integer tgGroupPromotion;


    @Schema(description = "分享交流")
    private Integer shareExchange;
}