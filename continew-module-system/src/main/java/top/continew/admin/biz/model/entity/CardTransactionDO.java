/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.AdPlatformEnum;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardTransactionStatusEnum;
import top.continew.admin.biz.enums.CardTransactionTypeEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡片交易流水实体
 *
 * <AUTHOR>
 * @since 2024/12/31 21:57
 */
@Data
@TableName("biz_card_transaction")
public class CardTransactionDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 平台
     */
    private CardPlatformEnum platform;

    /**
     * 第三方平台卡片ID
     */
    private String platformCardId;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 交易编号
     */
    private String transactionId;

    /**
     * 交易类型
     */
    private CardTransactionTypeEnum transType;

    /**
     * 原始交易类型
     */
    private String originTransType;

    /**
     * 交易状态
     */
    private CardTransactionStatusEnum transStatus;

    /**
     * 交易金额
     */
    private BigDecimal transAmount;

    /**
     * 清算金额
     */
    private BigDecimal clearAmount;

    /**
     * 交易币种
     */
    private String transCurrency;

    /**
     * 交易时间
     */
    private LocalDateTime transTime;

    private LocalDateTime chinaTime;

    private LocalDateTime statTime;

    private String adAccountId;

    private Long customerId;

    /**
     * 交易详情
     */
    private String transDetail;

    private String remark;

    private String originTransactionId;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    private AdPlatformEnum adPlatform;

    /**
     * 关联商务用户ID
     */
    private Long businessUserId;
}