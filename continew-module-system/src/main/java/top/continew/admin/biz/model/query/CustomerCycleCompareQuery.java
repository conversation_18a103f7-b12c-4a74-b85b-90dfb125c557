/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.util.List;

/**
 * 客户周期对比查询条件
 *
 * <AUTHOR>
 * @since 2024/12/31 10:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "客户周期对比查询条件")
public class CustomerCycleCompareQuery extends PageQuery {

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private List<Long> customerIds;

    /**
     * 客户标签ID列表
     */
    @Schema(description = "客户标签ID列表")
    private List<Long> tagIds;


    /**
     * 是否退款
     */
    @Schema(description = "是否退款")
    private Boolean hasRefund;

}