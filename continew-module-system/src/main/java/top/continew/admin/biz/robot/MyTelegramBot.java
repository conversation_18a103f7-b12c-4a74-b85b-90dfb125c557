/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.PostConstruct;
import jakarta.validation.ValidationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.api.methods.GetFile;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategyFactory;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.CustomerBalanceRecordService;
import top.continew.admin.system.service.FileService;
import top.continew.starter.core.exception.BusinessException;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class MyTelegramBot extends TelegramLongPollingBot {

    @Value("${telegram.token}")
    private String token;

    @Value("${telegram.username}")
    private String username;

    private final RobotCommandStrategyFactory robotCommandStrategyFactory;

    private final CustomerBalanceRecordService customerBalanceRecordService;

    private final FileService fileService;

    private List<String> financialUser = new ArrayList<>();

    @Override
    public String getBotUsername() {
        return username;
    }

    @PostConstruct
    public void init() {
        financialUser.add("hans0102");
        financialUser.add("Reallyee");
        financialUser.add("agony9445");
        TelegramBotsApi telegramBotsApi = null;
        try {
            telegramBotsApi = new TelegramBotsApi(DefaultBotSession.class);
            telegramBotsApi.registerBot(this);
            log.info("TG机器人启动成功");
        } catch (TelegramApiException e) {
            log.error("TG机器人启动失败：{}", e.getMessage());
        }
    }

    @Override
    public String getBotToken() {
        return token;
    }

    @Override
    public void onUpdateReceived(Update update) {
        if (update.hasMessage()) {
            if (update.getMessage().hasText()) {
                log.info("【TELEGRAM】[{}]-[{}]发送消息：{}", update.getMessage().getChat().getTitle(), update.getMessage()
                    .getFrom()
                    .getFirstName(), update.getMessage().getText());
                if (financialUser.contains(update.getMessage().getFrom().getUserName()) && update.getMessage()
                    .getText()
                    .startsWith("上传水单")) {
                    String replyMessage = "";
                    String id = update.getMessage().getText().replace("上传水单", "").trim();
                    String fileId = "";
                    if (update.getMessage().getReplyToMessage().hasPhoto()) {
                        fileId = update.getMessage().getReplyToMessage().getPhoto().get(update.getMessage().getReplyToMessage().getPhoto().size() - 1).getFileId();
                    } else if (update.getMessage().getReplyToMessage().hasDocument()) {
                        fileId = update.getMessage().getReplyToMessage().getDocument().getFileId();
                    }
                    String filePath = null;
                    try {
                        filePath = execute(new GetFile(fileId)).getFilePath();
                        String fileUrl = "https://api.telegram.org/file/bot" + getBotToken() + "/" + filePath;
                        FileInfo fileInfo = fileService.uploadImage(HttpUtil.downloadBytes(fileUrl), "");
                        customerBalanceRecordService.uploadCertificate(id, fileInfo.getUrl());
                        replyMessage = "上传成功";
                    } catch (Exception e) {
                        log.error("【TELEGRAM】水单提交失败：{}", ExceptionUtils.getStackTrace(e));
                        replyMessage = "上传失败";
                    }
                    SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                        .chatId(update.getMessage().getChatId())
                        .replyToMessageId(update.getMessage().getMessageId())
                        .text(replyMessage)
                        .build()));
                } else {
                    RobotCommandEnum command = getCommand(update.getMessage().getText());
                    RobotCommandStrategy commandStrategy = robotCommandStrategyFactory.findStrategy(command);
                    if (commandStrategy != null) {
                        String replyMsg = "";
                        try {
                            replyMsg = commandStrategy.execute(update);
                        } catch (ValidationException | BusinessException e) {
                            replyMsg = e.getMessage();
                        } catch (Exception e) {
                            log.error("【TELEGRAM】任务提交失败：{}", ExceptionUtils.getStackTrace(e));
                            replyMsg = "订单提交失败，请联系客服人员";
                        } finally {
                            if (StringUtils.isNotBlank(replyMsg)) {
                                Integer replyMessageId = update.getMessage().getMessageId();
                                if (command.equals(RobotCommandEnum.CUSTOMER_RECHARGE_CONFIRM)) {
                                    replyMessageId = update.getMessage().getReplyToMessage().getMessageId();
                                }
                                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                                    .chatId(update.getMessage().getChatId())
                                    .replyToMessageId(replyMessageId)
                                    .text(replyMsg)
                                    .build()));
                            }
                        }
                    }
                }
            }
        }
    }

    private RobotCommandEnum getCommand(String text) {
        String firstLine = StringUtils.substringBefore(text, "\n");
        for (RobotCommandEnum value : RobotCommandEnum.values()) {
            if (value.getDescription().equals(firstLine)) {
                return value;
            }
        }
        return RobotCommandEnum.UNKNOWN;
    }
}
