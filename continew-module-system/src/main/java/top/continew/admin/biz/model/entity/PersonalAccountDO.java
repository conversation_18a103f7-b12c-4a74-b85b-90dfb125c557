package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.PersonalAccountStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAppealStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutTypeEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 个号实体
 *
 * <AUTHOR>
 * @since 2025/03/11 17:02
 */
@Data
@TableName("biz_personal_account")
public class PersonalAccountDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道
     */
    private Long channelId;

    /**
     * 账号信息
     */
    private String content;

    /**
     * 接入人
     */
    private String accessUser;

    /**
     * 是否接入
     */
    private Boolean isAccess;

    /**
     * 浏览器编号
     */
    private String browserNo;

    /**
     * 账号状态
     */
    private PersonalAccountStatusEnum accountStatus;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 类型
     */
    private PersonalAccoutTypeEnum type;

    /**
     * 是否售后号
     */
    private Boolean isAfterSale;

    /**
     * 售后状态
     */
    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    /**
     * 售后原因
     */
    private String afterSaleReason;

    /**
     * 申诉状态
     */
    private PersonalAccoutAppealStatusEnum appealStatus;

    /**
     * 是否改密码
     */
    private Boolean isChangePwd;

    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private LocalDateTime purchaseTime;

    private String platformAccountId;

    private String uniqueKey;

    private String email;

    private String headers;

    private String proxy;

    private Boolean isSync;

    private String browserId;
}