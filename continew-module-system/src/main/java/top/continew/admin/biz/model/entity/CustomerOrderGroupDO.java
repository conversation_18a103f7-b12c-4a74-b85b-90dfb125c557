package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 客户下户订单分组实体
 *
 * <AUTHOR>
 * @since 2025/07/17 17:14
 */
@Data
@TableName("biz_customer_order_group")
public class CustomerOrderGroupDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    private String name;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private Long customerId;
}