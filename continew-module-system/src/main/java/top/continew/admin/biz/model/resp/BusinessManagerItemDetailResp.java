package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.excel.converter.BooleanConverter;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

/**
 * bm坑位详情信息
 *
 * <AUTHOR>
 * @since 2025/03/11 11:52
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "bm坑位详情信息")
public class BusinessManagerItemDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "BM渠道")
    private String businessManagerChannel;
    /**
     * bm 主键ID
     */
    @Schema(description = "bm 主键ID")
    private Long businessManagerId;

    @ExcelProperty(value = "BM ID")
    private String businessManager;

    private Long type;

    @ExcelProperty(value = "BM类型")
    private String typeName;

    /**
     * 名称（坑位1）
     */
    @Schema(description = "名称（坑位1）")
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 单价
     */
    @Schema(description = "单价")
    @ExcelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 状态（1=正常，2=封禁）
     */
    @Schema(description = "状态（1=正常，2=封禁）")
    @ExcelProperty(value = "状态",converter = ExcelBaseEnumConverter.class)
    private BusinessManagerStatusEnum status;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @ExcelProperty(value = "广告户ID")
    private String platformAdId;

    /**
     * 是否使用
     */
    @Schema(description = "是否使用")
    @ExcelProperty(value = "是否使用")
    private Boolean isUse;

    /**
     * 使用时间
     */
    @Schema(description = "使用时间")
    @ExcelProperty(value = "使用时间")
    private LocalDateTime useTime;

    /**
     * 封禁时间
     */
    @Schema(description = "封禁时间")
    @ExcelProperty(value = "封禁时间")
    private LocalDateTime banTime;


    private Long channelId;

    @ExcelProperty(value = "权限")
    private Integer ownMethod;

    @ExcelProperty(value = "是否补号" ,converter = BooleanConverter.class)
    private Boolean isBu;
}