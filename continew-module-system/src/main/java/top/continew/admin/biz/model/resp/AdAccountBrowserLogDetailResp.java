/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 账号浏览器操作记录详情信息
 *
 * <AUTHOR>
 * @since 2024/12/31 14:27
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "账号浏览器操作记录详情信息")
public class AdAccountBrowserLogDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 标签
     */
    @Schema(description = "标签")
    @ExcelProperty(value = "标签")
    private String label;

    /**
     * 操作内容
     */
    @Schema(description = "操作内容")
    @ExcelProperty(value = "操作内容")
    private String data;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    @ExcelProperty(value = "操作时间")
    private Long opsTime;

    /**
     * 平台账号ID
     */
    @Schema(description = "平台账号ID")
    @ExcelProperty(value = "平台账号ID")
    private String platformAccountId;

    /**
     * 激活码
     */
    @Schema(description = "激活码")
    @ExcelProperty(value = "激活码")
    private String activeCode;

    /**
     * 操作环境
     */
    @Schema(description = "操作环境")
    @ExcelProperty(value = "操作环境")
    private String env;
}