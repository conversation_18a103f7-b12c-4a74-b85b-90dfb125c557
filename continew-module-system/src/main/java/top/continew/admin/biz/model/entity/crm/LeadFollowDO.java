package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 线索跟进记录实体
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@TableName("biz_lead_follow")
public class LeadFollowDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 跟进内容
     */
    private String content;

    /**
     * 附件地址
     */
    private String attachment;

    /**
     * 跟进时间
     */
    private LocalDateTime followTime;

    /**
     * 跟进人
     */
    private Long followUserId;
}