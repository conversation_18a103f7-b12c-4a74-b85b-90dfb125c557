/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 广告组成效数据实体
 *
 * <AUTHOR>
 * @since 2025/01/03 15:08
 */
@Data
@TableName("biz_campaign_insight")
public class CampaignInsightDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 平台广告系列ID
     */
    private String platformCampaignId;

    /**
     * 广告组ID
     */
    private String platformAdsetId;

    /**
     * 广告户ID
     */
    private String adAccountId;

    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 展示次数
     */
    private Long impressions;

    /**
     * 点击量
     */
    private Long clicks;

    /**
     * 已花费金额
     */
    private BigDecimal spend;

    /**
     * 行为数据（JSON格式）
     */
    private String actionsData;
    
    /**
     * conversions成效数据（JSON格式）
     */
    private String conversionsData;
    
    /**
     * 转化成效值
     */
    private BigDecimal conversionValue;
    
    /**
     * 成效指标key值
     */
    private String conversionKey;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}