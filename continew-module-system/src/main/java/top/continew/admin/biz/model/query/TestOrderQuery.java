package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 测试任务查询条件
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Data
@Schema(description = "测试任务查询条件")
public class TestOrderQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 测试标题
     */
    @Schema(description = "测试标题")
    @Query(type = QueryType.EQ)
    private String title;

    /**
     * trello链接
     */
    @Schema(description = "trello链接")
    @Query(type = QueryType.EQ)
    private String trelloUrl;

    /**
     * 状态(1=待进行，2=进行中，3=已完成）
     */
    @Schema(description = "状态(1=待进行，2=进行中，3=已完成）")
    @Query(type = QueryType.EQ)
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;

    @QueryIgnore
    private String adAccountId;
}