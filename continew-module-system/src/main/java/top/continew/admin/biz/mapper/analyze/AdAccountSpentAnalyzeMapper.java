package top.continew.admin.biz.mapper.analyze;


import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.query.AdAccountSpentQuery;
import top.continew.admin.biz.model.resp.CustomerSpentResp;
import top.continew.admin.biz.model.resp.CustomerSpentStatisticsResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public interface AdAccountSpentAnalyzeMapper {

    /**
     * 查询广告户消耗的分页数据-根据客户
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 客户名称    总下户数的分页数据
     */
    IPage<CustomerSpentResp> selectPageCustomerBase(@Param("page") IPage<?> page, @Param("query") AdAccountSpentQuery query);


    /**
     * 根据客户查询出广告使用数
     *
     * @param customerId 客户ID
     * @return 广告使用数
     */
    Integer selectAdUsageCount(@Param("customerId") Long customerId, @Param("adAccountIds") List<String> adAccountIds, @Param("startTime")LocalDateTime startTime,  @Param("endTime")LocalDateTime endTime);


    /**
     * 根据广告户id列表查询对应下户订单的不消耗-低消耗-正常消耗的列表
     *
     * @param adAccountIds 广告户ID列表
     * @param day          开启天数
     * @param amount       消耗金额（小于）
     * @return 不消耗-低消耗-正常消耗列表
     */
    CustomerSpentStatisticsResp selectAllSpentCount(@Param("customerId") Long customerId, @Param("adAccountIds") List<String> adAccountIds,
                                                    @Param("day") Integer day, @Param("amount") BigDecimal amount,
                                                    @Param("startTime")LocalDateTime startTime,  @Param("endTime")LocalDateTime endTime);


    /**
     * 根据查询条件来查询统计 总下户数-广告使用数-不消耗数-低消耗数-正常消耗
     *
     * @param query  查询条件
     * @param day    开启天数
     * @param amount 消耗金额（小于）
     * @return 统计数据
     */
    CustomerSpentStatisticsResp getCustomerDashboardStats(@Param("query") AdAccountSpentQuery query,
                                                          @Param("day") Integer day, @Param("amount") BigDecimal amount);








    /**
     * 根据客户查询出一刀流广告户
     *
     * @param customerId 客户ID
     * @return 一刀流广告户
     */
    List<String> selectAdIdByOneDollar(@Param("customerId") Long customerId, @Param("adAccountIds") List<String> adAccountIds,
                                       @Param("startTime")LocalDateTime startTime,  @Param("endTime")LocalDateTime endTime);


    /**
     * 该客户下所有的支付问题的广告户ID列表
     * @param customerId 客户ID
     * @return 广告户ID列表
     */
    List<String> selectAdIdByFailTrans(@Param("customerId") Long customerId, @Param("adAccountIds") List<String> adAccountIds,
                                       @Param("startTime")LocalDateTime startTime,  @Param("endTime")LocalDateTime endTime);


    /**
     * 根据时区和客户ID来查询出对应的广告户ID列表
     *
     * @param customerId 客户ID
     * @param timezone   时区
     * @return 广告户ID列表
     */
    List<String> selectAdIdByTimeZone(@Param("customerId") Long customerId,  @Param("adAccountIds") List<String> adAccountIds, @Param("timezone") String timezone,
                                      @Param("startTime")LocalDateTime startTime,  @Param("endTime")LocalDateTime endTime);


    /**
     * 通过客户ID和卡头来获取客户下单的广告户
     *
     * @param customerId 客户ID
     * @return 广告户ID列表
     */
    List<String> selectAdIdByCardHeader(@Param("customerId") Long customerId, @Param("adAccountIds") List<String> adAccountIds, @Param("cardHeader") String cardHeader,
                                        @Param("startTime")LocalDateTime startTime,  @Param("endTime")LocalDateTime endTime);

    /**
     * 根据广告户ID列表获取每个广告户的最新下户订单
     *
     * @param adAccountIds 广告户ID列表
     * @return 最新下户订单列表
     */
    List<AdAccountOrderDO> selectLatestAdAccountOrdersByAdIds(@Param("adAccountIds") List<String> adAccountIds);
}
