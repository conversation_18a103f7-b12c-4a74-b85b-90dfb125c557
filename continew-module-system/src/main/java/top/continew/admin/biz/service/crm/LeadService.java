package top.continew.admin.biz.service.crm;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.crm.LeadDO;
import top.continew.admin.biz.model.query.crm.LeadQuery;
import top.continew.admin.biz.model.query.crm.LeadFollowQuery;
import top.continew.admin.biz.model.req.crm.LeadReq;
import top.continew.admin.biz.model.req.crm.LeadFollowReq;
import top.continew.admin.biz.model.req.crm.LeadFollowUpdateReq;
import top.continew.admin.biz.model.req.crm.LeadUpdateHandlerReq;
import top.continew.admin.biz.model.resp.crm.LeadDetailResp;
import top.continew.admin.biz.model.resp.crm.LeadResp;
import top.continew.admin.biz.model.resp.crm.LeadFollowResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * 线索业务接口
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
public interface LeadService extends BaseService<LeadResp, LeadDetailResp, LeadQuery, LeadReq>{
    
    /**
     * 添加线索跟进记录
     *
     * @param req 线索跟进记录请求
     */
    void addFollow(LeadFollowReq req);
    
    /**
     * 修改线索跟进记录
     *
     * @param req 线索跟进记录更新请求
     */
    void updateFollow(LeadFollowUpdateReq req);
    
    /**
     * 删除线索跟进记录
     *
     * @param id 跟进记录ID
     */
    void deleteFollow(Long id);

    /**
     * 分页获取线索跟进记录
     *
     * @param query 线索跟进记录查询
     * @return 线索跟进记录分页数据
     */
    PageResp<LeadFollowResp> pageFollow(LeadFollowQuery query);
    
    /**
     * 批量修改线索对接人
     *
     * @param req 批量修改线索对接人请求
     */
    void batchUpdateHandler(LeadUpdateHandlerReq req);

    /**
     * 更新长期跟进状态
     */
    void updateLongTermFollowUp();
}