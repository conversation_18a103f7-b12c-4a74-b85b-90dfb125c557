/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Facebook 广告系列实体
 *
 * <AUTHOR>
 * @since 2025/01/16 10:00
 */
@Data
@TableName("biz_fb_ad_campaigns")
public class FbAdCampaignsDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 平台ID（Facebook 返回的 campaign_id）
     */
    private String platformId;

    /**
     * 系列名称
     */
    private String name;

    /**
     * 广告户ID（Facebook 返回的 act_xxx）
     */
    private String adAccountId;

    /**
     * 状态：ACTIVE 正常投放 / PAUSED 暂停 / DELETED 已删除 / ARCHIVED 已归档
     */
    private String status;

    /**
     * 每日预算，单位为分（cent）
     */
    private Integer dailyBudget;

    /**
     * 总预算（分）；0 表示不限
     */
    private Integer lifetimeBudget;

    /**
     * 定价策略: LOWEST_COST_WITHOUT_CAP、COST_CAP、BID_CAP 等
     */
    private String bidStrategy;

    private LocalDateTime startTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String effectiveStatus;

    private String deliveryStatus;
}