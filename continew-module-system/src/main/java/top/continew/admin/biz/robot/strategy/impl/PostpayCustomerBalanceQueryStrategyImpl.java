/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CustomerSettleTypeEnum;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.mapper.AdAccountMapper;
import top.continew.admin.biz.mapper.AdAccountOrderMapper;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.service.CustomerService;

import java.math.BigDecimal;
import java.util.List;

@Service
@RequiredArgsConstructor
public class PostpayCustomerBalanceQueryStrategyImpl implements RobotCommandStrategy {

    private final AdAccountOrderMapper adAccountOrderMapper;

    private final TelegramChatIdConfig telegramChatIdConfig;

    private final CustomerService customerService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.POSTPAY_CUSTOMER_BALANCE;
    }

    @Override
    public String execute(Update update) {
        long chatId = update.getMessage().getChatId();
        if (chatId != telegramChatIdConfig.getBusinessChatId())
            return null;
        List<CustomerDO> customerList = customerService.list(Wrappers.<CustomerDO>lambdaQuery()
            .eq(CustomerDO::getSettleType, CustomerSettleTypeEnum.TWO));
        StringBuilder result = new StringBuilder();
        for (CustomerDO customerDO : customerList) {
            BigDecimal fbBalance = adAccountOrderMapper.getCustomerFbBalance(customerDO.getId());
            result.append(customerDO.getName()).append("剩余余额：$").append(NumberUtil.toStr(fbBalance.add(customerDO.getBalance()))).append("\n");
        }
        return result.toString();
    }
}
