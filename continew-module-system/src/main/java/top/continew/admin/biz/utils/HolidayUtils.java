package top.continew.admin.biz.utils;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class HolidayUtils {
    private static final Set<LocalDate> HOLIDAYS = new HashSet<>();

    @PostConstruct
    public void init() {
        try {
            Resource resource = new ClassPathResource("data/holidays.txt");
            List<String> lines = Files.readAllLines(Paths.get(resource.getURI()));
            for (String line : lines) {
                String[] dates = line.split(",");
                for (String date : dates) {
                    HOLIDAYS.add(LocalDate.parse(date));
                }
            }
        } catch (Exception e) {
            log.error("加载节假日配置文件失败", e);
        }
    }

    /**
     * 判断指定日期是否为休息日
     *
     * @param date 日期
     * @return true-休息日 false-工作日
     */
    public boolean isHoliday(LocalDate date) {
        return HOLIDAYS.contains(date);
    }

    /**
     * 计算指定日期后n个工作日的日期
     *
     * @param startDate 开始日期
     * @param workdays 工作日天数
     * @return 计算后的日期
     */
    public LocalDateTime calculateWorkingDayDate(LocalDateTime startDate, int workdays) {
        LocalDateTime result = startDate;
        int addedWorkDays = 0;
        
        while (addedWorkDays < workdays) {
            result = result.plusDays(1);
            if (!isHoliday(result.toLocalDate())) {
                addedWorkDays++;
            }
        }
        
        return result;
    }
}