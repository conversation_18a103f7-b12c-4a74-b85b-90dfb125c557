package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 投放日报明细详情信息
 *
 * <AUTHOR>
 * @since 2025/08/13 21:02
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "投放日报明细详情信息")
public class AdProductStatItemDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计数据ID
     */
    @Schema(description = "统计数据ID")
    @ExcelProperty(value = "统计数据ID")
    private Long statId;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    @ExcelProperty(value = "广告户")
    private String platformAdId;

    /**
     * 消耗
     */
    @Schema(description = "消耗")
    @ExcelProperty(value = "消耗")
    private BigDecimal spend;

    /**
     * 回流
     */
    @Schema(description = "回流")
    @ExcelProperty(value = "回流")
    private BigDecimal reflowSpend;

    /**
     * 成效数据
     */
    @Schema(description = "成效数据")
    @ExcelProperty(value = "成效数据")
    private String effectData;
}