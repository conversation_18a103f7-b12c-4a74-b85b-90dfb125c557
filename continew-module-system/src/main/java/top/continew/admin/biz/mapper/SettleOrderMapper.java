package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.resp.SettleOrderResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.SettleOrderDO;

/**
* 结算订单 Mapper
*
* <AUTHOR>
* @since 2025/03/20 14:56
*/
public interface SettleOrderMapper extends BaseMapper<SettleOrderDO> {
    IPage<SettleOrderResp> customPage(@Param("page") Page<Object> objectPage, @Param(Constants.WRAPPER) QueryWrapper<SettleOrderDO> wrapper);

}