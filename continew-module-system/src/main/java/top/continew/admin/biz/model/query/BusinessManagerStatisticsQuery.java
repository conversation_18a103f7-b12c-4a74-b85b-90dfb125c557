package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 成本分析查询条件
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
@Data
@Schema(description = "成本分析查询条件")
public class BusinessManagerStatisticsQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @Query(type = QueryType.BETWEEN)
    private LocalDate[] statisticsDate;
}