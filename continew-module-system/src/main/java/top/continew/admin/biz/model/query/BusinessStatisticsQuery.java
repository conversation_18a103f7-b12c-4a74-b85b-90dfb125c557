package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.query.PageQuery;
import cn.hutool.core.date.DatePattern;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商务统计查询")
public class BusinessStatisticsQuery extends PageQuery {

    @Schema(description = "商务ID")
    private Long businessUserId;

    @Schema(description = "统计时间范围")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] statTimes;

    private String sortField;

    private Boolean ascSortFlag;
}