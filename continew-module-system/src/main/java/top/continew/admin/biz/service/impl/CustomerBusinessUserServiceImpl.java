package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.CustomerBusinessUserMapper;
import top.continew.admin.biz.model.entity.CustomerBusinessUserDO;
import top.continew.admin.biz.model.query.CustomerBusinessUserQuery;
import top.continew.admin.biz.model.req.CustomerBusinessUserReq;
import top.continew.admin.biz.model.resp.CustomerBusinessUserDetailResp;
import top.continew.admin.biz.model.resp.CustomerBusinessUserResp;
import top.continew.admin.biz.service.CustomerBusinessUserService;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.util.List;

/**
 * 客户商务对接业务实现
 *
 * <AUTHOR>
 * @since 2025/08/05 15:35
 */
@Service
@RequiredArgsConstructor
public class CustomerBusinessUserServiceImpl extends BaseServiceImpl<CustomerBusinessUserMapper, CustomerBusinessUserDO, CustomerBusinessUserResp, CustomerBusinessUserDetailResp, CustomerBusinessUserQuery, CustomerBusinessUserReq> implements CustomerBusinessUserService {

    @Override
    public List<CustomerBusinessUserResp> getByCustomerId(Long customerId) {
        LambdaQueryWrapper<CustomerBusinessUserDO> queryWrapper = Wrappers.<CustomerBusinessUserDO>lambdaQuery()
                .eq(CustomerBusinessUserDO::getCustomerId, customerId)
                .orderByDesc(CustomerBusinessUserDO::getId);

        List<CustomerBusinessUserDO> list = this.list(queryWrapper);
        List<CustomerBusinessUserResp> resp = BeanUtil.copyToList(list, CustomerBusinessUserResp.class);
        resp.forEach(this::fill);
        return resp;
    }
}