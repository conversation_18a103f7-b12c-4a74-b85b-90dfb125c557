package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 测试任务详情查询条件
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Data
@Schema(description = "测试任务详情查询条件")
public class TestOrderItemQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联测试任务
     */
    @Schema(description = "关联测试任务")
    @Query(type = QueryType.EQ)
    private Long orderId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @Query(type = QueryType.EQ)
    private String platformAdId;

    /**
     * 状态（1=待进行，2=进行中，3=测试通过，4=测试失败）
     */
    @Schema(description = "状态（1=待进行，2=进行中，3=测试通过，4=测试失败）")
    @Query(type = QueryType.EQ)
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Query(type = QueryType.EQ)
    private String remark;
}