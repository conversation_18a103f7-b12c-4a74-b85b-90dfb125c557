package top.continew.admin.biz.report.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.AdAccountSaleStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.event.AdAccountStatusChangeEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.AdAccountTransactionService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.admin.system.model.entity.UserDO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AdsAccountDataLoaderPreloaderStrategyImpl implements ReportOpsStrategy {

    private final AdAccountService adAccountService;

    private final AdAccountTransactionService adAccountTransactionService;

    @Override
    public ReportType getReport() {
        return ReportType.ADS_ACCOUNT_DATA_LOADER_PRELOADER;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
        JSONArray data = JSONArray.from(browserReq.getData());
        List<AdAccountDO> adAccountList = adAccountService.list(Wrappers.<AdAccountDO>lambdaQuery()
            .select(AdAccountDO::getId, AdAccountDO::getPlatformAdId, AdAccountDO::getAccountStatus));
        for (int i = 0; i < data.size(); i++) {
            JSONObject item = data.getJSONObject(i);
            String platformAdId = item.getString("account_id");
            AdAccountDO exist = adAccountList.stream()
                .filter(v -> v.getPlatformAdId().equals(platformAdId))
                .findFirst()
                .orElse(null);
            int accountStatus = item.getInteger("account_status");
            AdAccountStatusEnum accountStatusEnum = AdAccountStatusEnum.getEnum(accountStatus);
            String timezone = "";
            String timezoneName = item.getString("timezone_name");
            if (StringUtils.isNotBlank(timezoneName)) {
                timezone = FacebookUtils.getTimezone(timezoneName);
            }
            BigDecimal spendCap = Optional.ofNullable(item.getBigDecimal("spend_cap"))
                .map(v -> CommonUtils.divide100(v, null))
                .orElse(null);
            BigDecimal amountSpend = Optional.ofNullable(item.getBigDecimal("amount_spent"))
                .map(v -> CommonUtils.divide100(v, null))
                .orElse(null);
            BigDecimal balance = Optional.ofNullable(item.getBigDecimal("balance"))
                .map(v -> CommonUtils.divide100(v, null))
                .orElse(null);
            AdAccountDO accountDO = new AdAccountDO.builder().platformAdId(platformAdId)
                .name(item.getString("name"))
                .timezone(timezone)
                .spendCap(spendCap)
                .amountSpent(amountSpend)
                .balance(balance)
                .billCountry(item.getString("business_country_code"))
                .billCurrency(item.getString("currency"))
                .build();
            if (exist == null) {
                accountDO.setAccountStatus(accountStatusEnum);
                accountDO.setBrowserNo(browserReq.getBrowserNo());
                accountDO.setCreateUser(userDO.getId());
                accountDO.setSaleStatus(platformAdId.length() <= 9
                    ? AdAccountSaleStatusEnum.LOCK
                    : AdAccountSaleStatusEnum.WAIT);
                accountDO.setPlatformAccountId(browserReq.getFbAccountId());
                adAccountService.save(accountDO);
            } else {
                accountDO.setId(exist.getId());
                adAccountService.updateById(accountDO);
                AdAccountStatusEnum newAccountStatus = AdAccountStatusEnum.getEnum(accountStatus);
                if (!exist.getAccountStatus().equals(newAccountStatus)) {
                    AdAccountDO statusChange = new AdAccountDO();
                    statusChange.setPlatformAdId(platformAdId);
                    statusChange.setAccountStatus(newAccountStatus);
                    AdAccountStatusChangeEvent adAccountStatusChangeEvent = new AdAccountStatusChangeEvent(statusChange);
                    SpringUtil.publishEvent(adAccountStatusChangeEvent);
                }
            }
            // 交易记录保存
            JSONObject transactions = item.getJSONObject("transactions");
            if (transactions != null) {
                adAccountTransactionService.insertAdAccountTransaction(platformAdId, transactions.getJSONArray("data"));
            }
        }
        return "";
    }
}
