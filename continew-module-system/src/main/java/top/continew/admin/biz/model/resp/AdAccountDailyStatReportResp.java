/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AdAccountDailyStatReportResp {

    @ExcelProperty("广告户ID")
    private String adAccountId;

    /**
     * 统计日期
     */
    @ExcelProperty("统计日期")
    private String statDate;

    /**
     * 打款金额
     */
    @ExcelProperty("消耗")
    private BigDecimal spend = BigDecimal.ZERO;

    /**
     * 充值手续费
     */
    @ExcelProperty("充值")
    private BigDecimal recharge = BigDecimal.ZERO;
}
