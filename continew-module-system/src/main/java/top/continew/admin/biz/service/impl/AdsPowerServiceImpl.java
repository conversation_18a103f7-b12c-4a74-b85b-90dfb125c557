package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.AdsPowerMapper;
import top.continew.admin.biz.model.entity.AdsPowerDO;
import top.continew.admin.biz.model.query.AdsPowerQuery;
import top.continew.admin.biz.model.req.AdsPowerReq;
import top.continew.admin.biz.model.resp.AdsPowerDetailResp;
import top.continew.admin.biz.model.resp.AdsPowerResp;
import top.continew.admin.biz.service.AdsPowerService;

/**
 * 指纹浏览器业务实现
 *
 * <AUTHOR>
 * @since 2025/04/25 17:15
 */
@Service
@RequiredArgsConstructor
public class AdsPowerServiceImpl extends BaseServiceImpl<AdsPowerMapper, <PERSON>s<PERSON>owerDO, AdsPowerResp, AdsPowerDetailResp, AdsPowerQuery, AdsPowerReq> implements AdsPowerService {}