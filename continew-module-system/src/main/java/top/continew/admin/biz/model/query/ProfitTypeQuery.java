package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 利润类型查询条件
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Data
@Schema(description = "利润类型查询条件")
public class ProfitTypeQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 媒体平台
     */
    @Schema(description = "媒体平台")
    @Query(type = QueryType.EQ)
    private Integer adPlatform;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @Query(type = QueryType.EQ)
    private String name;

    @Query(type = QueryType.EQ)
    private String cat;
}