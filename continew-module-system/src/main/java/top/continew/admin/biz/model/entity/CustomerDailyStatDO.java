package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 客户每日统计实体
 *
 * <AUTHOR>
 * @since 2025/07/17 11:39
 */
@Data
@TableName("biz_customer_daily_stat")
public class CustomerDailyStatDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 统计时间
     */
    private LocalDate statDate;

    /**
     * 总户数
     */
    private Integer totalAccount;

    /**
     * 当前户数
     */
    private Integer totalFinishAccount;

    /**
     * 正常户数
     */
    private Integer totalNormalAccount;

    /**
     * 使用户数
     */
    private Integer todayUsedAccount;

    /**
     * 封禁数量
     */
    private Integer todayBanAccount;

    /**
     * 下户数
     */
    private Integer todayOpenAccount;

    /**
     * 卡台消耗
     */
    private BigDecimal todayCardSpent;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}