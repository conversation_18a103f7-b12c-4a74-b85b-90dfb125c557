package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 广告户交易记录查询条件
 *
 * <AUTHOR>
 * @since 2025/03/10 14:15
 */
@Data
@Schema(description = "广告户交易记录查询条件")
public class AdAccountTransactionQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @Query(type = QueryType.EQ)
    private String platformAdId;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] transTime;

    /**
     * 交易ID
     */
    @Schema(description = "交易ID")
    @Query(type = QueryType.EQ)
    private String transactionId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @Query(type = QueryType.EQ)
    private String transactionType;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    @Query(type = QueryType.EQ)
    private BigDecimal amount;

    /**
     * 交易状态
     */
    @Schema(description = "交易状态")
    @Query(type = QueryType.EQ)
    private String status;
}