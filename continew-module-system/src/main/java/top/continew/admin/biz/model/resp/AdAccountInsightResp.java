/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 广告户每日消耗信息
 *
 * <AUTHOR>
 * @since 2025/01/14 14:43
 */
@Data
@Schema(description = "广告户每日消耗信息")
public class AdAccountInsightResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    private String adAccountId;

    /**
     * 统计时间
     */
    @Schema(description = "统计时间")
    private LocalDate statDate;

    /**
     * 消耗
     */
    @Schema(description = "消耗")
    private BigDecimal spend;
}