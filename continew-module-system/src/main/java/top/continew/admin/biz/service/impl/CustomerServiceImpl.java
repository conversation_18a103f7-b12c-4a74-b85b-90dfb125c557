/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.nimbusds.jose.shaded.gson.JsonObject;
import org.apache.poi.ss.usermodel.Workbook;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.CustomerBalanceChangeEvent;
import top.continew.admin.biz.event.CustomerBalanceChangeModel;
import top.continew.admin.biz.excel.CustomerDailyExcel;
import top.continew.admin.biz.mapper.CustomerMapper;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.entity.crm.CustomerVisitTaskDO;
import top.continew.admin.biz.model.entity.crm.OpportunityDO;
import top.continew.admin.biz.model.query.CustomerOrderStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerQuery;
import top.continew.admin.biz.model.query.CustomerStatReportQuery;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.service.crm.CustomerVisitTaskService;
import top.continew.admin.biz.service.crm.OpportunityService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.CustomerHelper;
import top.continew.admin.biz.utils.HolidayUtils;
import top.continew.starter.core.exception.BaseException;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户业务实现
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Service
@RequiredArgsConstructor
public class CustomerServiceImpl extends BaseServiceImpl<CustomerMapper, CustomerDO, CustomerResp, CustomerDetailResp, CustomerQuery, CustomerReq> implements CustomerService {

    private final CustomerBalanceRecordService customerBalanceRecordService;

    private final AdAccountBalanceRecordService adAccountBalanceRecordService;

    private final AdAccountService adAccountService;

    private final AdAccountCardService adAccountCardService;

    private final CardService cardService;

    private final CustomerBusinessUserService customerBusinessUserService;

    private final AdProductStatService adProductStatService;

    private final HolidayUtils holidayUtils;

    @Override
    public Long changeAmount(Long customerId,
                             BigDecimal amount,
                             CustomerBalanceTypeEnum balanceType,
                             LocalDateTime transactionTime,
                             String remark) {
        return this.changeAmount(customerId, null, amount, balanceType, transactionTime, remark, null, null);
    }

    @Override
    protected void afterAdd(CustomerReq req, CustomerDO entity) {
        CustomerBusinessUserDO customerBusinessUserDO = new CustomerBusinessUserDO();
        customerBusinessUserDO.setCustomerId(entity.getId());
        customerBusinessUserDO.setUserId(entity.getBusinessUserId());
        customerBusinessUserService.save(customerBusinessUserDO);
    }

    @Override
    protected void beforeAdd(CustomerReq req) {
        // 判断客户名称是否重复
        LambdaQueryWrapper<CustomerDO> queryWrapper = Wrappers.lambdaQuery(CustomerDO.class)
                .eq(CustomerDO::getName, req.getName());
        if (baseMapper.exists(queryWrapper)) {
            throw new BusinessException("已存在相同名称的客户，请不要重复创建");
        }
    }

    @Override
    protected void beforeUpdate(CustomerReq req, Long id) {
        // 判断客户名称是否重复
        if (null != req.getName()) {
            LambdaQueryWrapper<CustomerDO> queryWrapper = Wrappers.lambdaQuery(CustomerDO.class)
                    .eq(CustomerDO::getName, req.getName())
                    .ne(CustomerDO::getId, id);
            if (baseMapper.exists(queryWrapper)) {
                throw new BusinessException("已存在相同名称的客户，请不要重复创建");
            }
        }
    }

    @Override
    public PageResp<CustomerResp> page(CustomerQuery query, PageQuery pageQuery) {
        // 创建分页对象
        Page<CustomerDO> page = new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize());
        IPage<CustomerDO> resultPage = baseMapper.pageCustomers(page, query);
        // 构建返回结果
        PageResp<CustomerResp> pageResp = PageResp.build(resultPage, this.getListClass());
        // 填充额外信息
        pageResp.getList().forEach(this::fill);

        return pageResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long changeAmount(Long customerId,
                             String platformAdId,
                             BigDecimal amount,
                             CustomerBalanceTypeEnum balanceType,
                             LocalDateTime transactionTime,
                             String remark) {
        return this.changeAmount(customerId, platformAdId, amount, balanceType, transactionTime, remark, null, null);
    }

    private Long changeAmount(Long customerId,
                              String platformAdId,
                              BigDecimal amount,
                              CustomerBalanceTypeEnum balanceType,
                              LocalDateTime transactionTime,
                              String remark,
                              String certificate,
                              Long createUser) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        CustomerBalanceRecordDO record = new CustomerBalanceRecordDO();
        record.setCustomerId(customerId);
        boolean isOut = amount.compareTo(BigDecimal.ZERO) < 0;
        if (isOut) {
            record.setAction(TransactionActionEnum.OUT);
        } else {
            record.setAction(TransactionActionEnum.IN);
        }
        record.setType(balanceType);
        record.setAmount(amount.abs());
        record.setTransTime(transactionTime != null ? transactionTime : LocalDateTime.now());
        record.setRemark(remark);
        record.setPlatformAdId(platformAdId);
        record.setCertificate(certificate);
        if (createUser != null) {
            record.setCreateUser(createUser);
        }
        if (isOut) {
            CustomerHelper.reduceBalance(customerId, amount.abs());
        } else {
            CustomerHelper.addBalance(customerId, amount.abs());
        }
        CustomerDO customer = getById(customerId);
        record.setAfterAmount(customer.getBalance());
        record.setBusinessUserId(customer.getBusinessUserId());
        customerBalanceRecordService.save(record);
        return record.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeAmount(Long id, CustomerBalanceChangeReq req) {
        CustomerDO customer = this.getById(id);
        BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(customer.getId());
        CustomerBalanceChangeModel changeModel = null;
        Long recordId;
        if (Objects.equals(req.getAction(), CustomerBalanceTypeEnum.SYSTEM.getValue())) {
            CheckUtils.throwIfNull(req.getFeeHandleMethod(), "请选择手续费扣除方式");
            // 计算充值手续费
            BigDecimal fee;
            if (req.getFeeHandleMethod().equals(RechargeFeeHandleMethodEnum.REDUCE_FROM_RECHARGE_AMOUNT)) {
                fee = CommonUtils.divide100(req.getAmount().multiply(customer.getFeeRatePercent()), null);
            } else if (req.getFeeHandleMethod().equals(RechargeFeeHandleMethodEnum.RECHARGE_INCLUDE_FEE)) {
                BigDecimal onePart = req.getAmount()
                        .divide(customer.getFeeRatePercent().add(BigDecimal.valueOf(100)), 4, RoundingMode.HALF_UP);
                fee = onePart.multiply(customer.getFeeRatePercent()).setScale(2, RoundingMode.HALF_UP);
            } else {
                CheckUtils.throwIfNull(req.getFee(), "请输入手续费");
                fee = req.getFee();
            }
            recordId = this.changeAmount(id, null, req.getAmount(), CustomerBalanceTypeEnum.SYSTEM, req.getTransTime(), req.getRemark(), req.getCertificate(), null);
            this.changeAmount(id, fee.negate(), CustomerBalanceTypeEnum.RECHARGE_FEE, req.getTransTime(), null);
            // 发送额度汇总信息
            changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, req.getAmount(), customer.getBalance(), req.getAmount()
                    .subtract(fee));
        } else if (Objects.equals(req.getAction(), CustomerBalanceTypeEnum.DEDUCE.getValue())) {
            recordId = this.changeAmount(id, req.getAmount()
                    .negate(), CustomerBalanceTypeEnum.DEDUCE, req.getTransTime(), req.getRemark());
            changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, req.getAmount()
                    .negate(), customer.getBalance(), req.getAmount().negate());
        } else if (Objects.equals(req.getAction(), CustomerBalanceTypeEnum.RECHARGE_FEE_REFUND.getValue())) {
            recordId = this.changeAmount(id, req.getAmount()
                    .abs(), CustomerBalanceTypeEnum.RECHARGE_FEE_REFUND, req.getTransTime(), req.getRemark());
            changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, req.getAmount()
                    .negate(), customer.getBalance(), req.getAmount().abs());
        } else {
            // 转移金额
            recordId = this.changeAmount(id, req.getAmount(), CustomerBalanceTypeEnum.MIGRATE, req.getTransTime(), req.getRemark());
            // 发送额度汇总信息
            changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, null, customer.getBalance(), req.getAmount());
        }
        if (req.getTransTime() != null) {
            customerBalanceRecordService.adjustAfterAmount(customer.getId(), recordId);
        }

        changeModel.setAction(req.getAction());
        changeModel.setTransferTime(null != req.getTransTime() ? req.getTransTime() : LocalDateTime.now());
        SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
    }

    @Override
    public PageResp<CustomerStatReportResp> pageCustomerStatReport(CustomerStatReportQuery query, PageQuery pageQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        IPage<CustomerStatReportResp> page = this.baseMapper.selectCustomerStatReport(new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize()), query.getCustomerId(), start, end, query.getIsSelf(), query.getSettleType(), query.getBusinessType(), query.getSortField(), query.getAscSortFlag());
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<CustomerStatReportResp> listCustomerStatReport(CustomerStatReportQuery query, SortQuery sortQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        return this.baseMapper.listCustomerStatReport(query.getCustomerId(), start, end, query.getIsSelf(), query.getSettleType(), query.getBusinessType());
    }

    @Override
    public CustomerStatSummaryResp getCustomerStatReportSummary(CustomerStatReportQuery query) {
        CustomerStatSummaryResp customerStatSummaryResp = this.baseMapper.getCustomerStatReportSummary(query);
        List<CustomerDO> customerList = this.list(Wrappers.<CustomerDO>lambdaQuery()
                .eq(query.getIsSelf() != null, CustomerDO::getIsSelfAccount, query.getIsSelf())
                .eq(query.getCustomerId() != null, CustomerDO::getId, query.getCustomerId())
                .eq(query.getSettleType() != null, CustomerDO::getSettleType, query.getSettleType()));

        customerStatSummaryResp.setTotalBalance(customerList.stream()
                .map(CustomerDO::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        return customerStatSummaryResp;
    }

    @Override
    public PageResp<CustomerDailyStatReportResp> pageCustomerDailyStatReport(CustomerStatReportQuery query,
                                                                             PageQuery pageQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        IPage<CustomerDailyStatReportResp> page = this.baseMapper.selectCustomerDailyStatReport(new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize()), query.getCustomerId(), start, end);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<CustomerDailyStatReportResp> listCustomerDailyStatReport(CustomerStatReportQuery query,
                                                                         SortQuery sortQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        return this.baseMapper.listCustomerDailyStatReport(query.getCustomerId(), start, end);
    }

    @Override
    public CustomerDO getByTelegramChatId(String telegramChatId) {
        // 判断当前群是否只绑定一个客户
        long count = this.count(Wrappers.<CustomerDO>lambdaQuery().eq(CustomerDO::getTelegramChatId, telegramChatId));
        if (count != 1) {
            return null;
        }
        return this.getOne(Wrappers.<CustomerDO>lambdaQuery().eq(CustomerDO::getTelegramChatId, telegramChatId));
    }

    @Override
    public boolean checkRepeatTransfer(Long customerId, BigDecimal amount) {
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusHours(3);
        return customerBalanceRecordService.exists(Wrappers.<CustomerBalanceRecordDO>lambdaQuery()
                .eq(CustomerBalanceRecordDO::getCustomerId, customerId)
                .eq(CustomerBalanceRecordDO::getType, CustomerBalanceTypeEnum.SYSTEM)
                .eq(CustomerBalanceRecordDO::getAmount, amount)
                .between(CustomerBalanceRecordDO::getTransTime, start, end));
    }

    @Override
    public List<CustomerDailyExcel> dailyList(CustomerDO customer) {
        List<CustomerBalanceRecordDO> list = customerBalanceRecordService.list(new LambdaQueryWrapper<CustomerBalanceRecordDO>().eq(CustomerBalanceRecordDO::getCustomerId, customer.getId())
                .orderByAsc(CustomerBalanceRecordDO::getTransTime, CustomerBalanceRecordDO::getId));

        List<CustomerDailyExcel> excelList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            CustomerBalanceRecordDO record = list.get(i);
            if (record.getType() == CustomerBalanceTypeEnum.RECHARGE_FEE) {
                continue;
            }
            LocalDateTime transTime = record.getTransTime();
            CustomerDailyExcel customerDailyExcel = new CustomerDailyExcel();
            customerDailyExcel.setDate(transTime.toLocalDate().toString());
            customerDailyExcel.setAdAccountId(record.getPlatformAdId());
            customerDailyExcel.setCustomer(customer.getName());
            customerDailyExcel.setRemark(record.getRemark().replaceAll("【旧数据导入】", ""));

            if (record.getType() == CustomerBalanceTypeEnum.SYSTEM) {
                BigDecimal payment = record.getAmount();
                customerDailyExcel.setPayment(payment);
                BigDecimal fee = BigDecimal.ZERO;
                if (i + 1 < list.size()) {
                    if (list.get(i + 1).getType() == CustomerBalanceTypeEnum.RECHARGE_FEE) {
                        fee = list.get(i + 1).getAmount();
                    }
                }
                if (fee.compareTo(BigDecimal.ZERO) > 0) {
                    customerDailyExcel.setServiceFee(fee);
                }
                customerDailyExcel.setRechargeAdAccountAmount(payment.subtract(fee));
            }
            syncData(customerDailyExcel, record);
            excelList.add(customerDailyExcel);
        }

        return excelList;
    }

    @Override
    public CustomerInfoResp getInfo(Long id) {
        CustomerDO customerDO = getById(id);
        if (customerDO == null) {
            throw new BusinessException("没找到客户");
        }
        AdAccountOrderService adAccountOrderService = SpringUtil.getBean(AdAccountOrderService.class);
        List<String> list = adAccountOrderService.list(new LambdaQueryWrapper<AdAccountOrderDO>().eq(AdAccountOrderDO::getCustomerId, id)
                        .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED))
                .stream()
                .map(AdAccountOrderDO::getAdAccountId)
                .toList();
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        CustomerInfoResp customerInfoResp = new CustomerInfoResp();
        customerInfoResp.setTotalAdAccount(list.size());

        List<AdAccountBalanceRecordDO> adAccountBalanceRecordDOList = adAccountBalanceRecordService.list(new LambdaQueryWrapper<AdAccountBalanceRecordDO>().in(AdAccountBalanceRecordDO::getPlatformAdId, list)
                .eq(AdAccountBalanceRecordDO::getType, AdAccountBalanceTypeEnum.RECHARGE));
        // 充值金额
        BigDecimal totalRecharge = adAccountBalanceRecordDOList.stream()
                .map(AdAccountBalanceRecordDO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal withdrawalAmount = customerBalanceRecordService.list(new LambdaQueryWrapper<CustomerBalanceRecordDO>().eq(CustomerBalanceRecordDO::getCustomerId, customerDO.getId())
                        .in(CustomerBalanceRecordDO::getType, CustomerBalanceTypeEnum.AD_ACCOUNT_CLEAR, CustomerBalanceTypeEnum.AD_ACCOUNT_REDUCE))
                .stream()
                .map(CustomerBalanceRecordDO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setTotalRecharge(totalRecharge.subtract(withdrawalAmount));

        // 平均充值
        customerInfoResp.setAverageRecharge(customerInfoResp.getTotalRecharge()
                .divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP));

        // 停用广告户
        long count = adAccountService.count(new LambdaQueryWrapper<AdAccountDO>().in(AdAccountDO::getPlatformAdId, list)
                .eq(AdAccountDO::getAccountStatus, AdAccountStatusEnum.BANNED));
        customerInfoResp.setDeadAdAccountNum(Math.toIntExact(count));
        // 死户率
        BigDecimal deadProbabilities = BigDecimal.valueOf(customerInfoResp.getDeadAdAccountNum())
                .divide(BigDecimal.valueOf(customerInfoResp.getTotalAdAccount()), 2, RoundingMode.HALF_UP);
        customerInfoResp.setDeadProbabilities(deadProbabilities);
        // 当日充值
        BigDecimal toDayRecharge = adAccountBalanceRecordDOList.stream()
                .filter(s -> s.getTransTime().toLocalDate().equals(LocalDate.now()))
                .map(AdAccountBalanceRecordDO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setToDayRecharge(toDayRecharge);

        AdAccountInsightService adAccountInsightService = SpringUtil.getBean(AdAccountInsightService.class);

        List<AdAccountInsightDO> accountInsight = adAccountInsightService.list(new LambdaQueryWrapper<AdAccountInsightDO>().in(AdAccountInsightDO::getAdAccountId, list));
        // 昨日花费
        BigDecimal yesterdaySpend = accountInsight.stream()
                .filter(s -> s.getStatDate().equals(LocalDate.now().minusDays(1)))
                .map(AdAccountInsightDO::getSpend)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setYesterdaySpend(yesterdaySpend);
        // 总花费
        BigDecimal totalSpend = accountInsight.stream()
                .map(AdAccountInsightDO::getSpend)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setTotalSpend(totalSpend);
        // 平均花费
        BigDecimal averageSpend = totalSpend.divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
        customerInfoResp.setAverageSpend(averageSpend);

        Set<String> cards = adAccountCardService.list(new LambdaQueryWrapper<AdAccountCardDO>().in(AdAccountCardDO::getPlatformAdId, list)
                        .ne(AdAccountCardDO::getFullCardNumber, ""))
                .stream()
                .map(AdAccountCardDO::getFullCardNumber)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        // 卡台余额
        BigDecimal cardBalance = cardService.list(new LambdaQueryWrapper<CardDO>().in(CardDO::getCardNumber, cards))
                .stream()
                .map(CardDO::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setCardBalance(cardBalance);

        return customerInfoResp;
    }

    @Override
    public CustomerDO getByName(String customerName) {
        return this.getOne(Wrappers.<CustomerDO>lambdaQuery().eq(CustomerDO::getName, customerName));
    }

    @Override
    public String getName(Long id) {
        CustomerDO entity = this.getById(id);

        return null != entity ? entity.getName() : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<Long> ids, Integer status, LocalDateTime terminateTime) {
        // 检查状态是否合法
        CheckUtils.throwIf(!CustomerStatusEnum.NORMAL.getValue()
                .equals(status) && !CustomerStatusEnum.TERMINATED.getValue().equals(status), "状态不合法");

        LambdaUpdateWrapper<CustomerDO> updateWrapper = Wrappers.<CustomerDO>lambdaUpdate()
                .set(CustomerDO::getStatus, status)
                .in(CustomerDO::getId, ids);

        // 根据状态设置或清空终止时间
        if (CustomerStatusEnum.NORMAL.getValue().equals(status)) {
            // 正常状态，清空终止时间
            updateWrapper.set(CustomerDO::getTerminateTime, null);
        } else if (CustomerStatusEnum.TERMINATED.getValue().equals(status)) {
            // 终止状态，设置当前时间为终止时间
            terminateTime = null == terminateTime ? LocalDateTime.now() : terminateTime;
            updateWrapper.set(CustomerDO::getTerminateTime, terminateTime);
        }

        // 批量更新状态和终止时间
        this.update(updateWrapper);
    }

    @Override
    public CustomerOpenResp open(Long id, CustomerOpenReq req) {
        CustomerDO customer = getById(id);
        CheckUtils.throwIfNull(customer, "客户不存在");
        CheckUtils.throwIf(StringUtils.isNotBlank(customer.getPassword()), "客户已经存在密码");
        boolean exists = exists(new LambdaQueryWrapper<CustomerDO>().eq(CustomerDO::getUsername, req.getUsername()));
        CheckUtils.throwIf(exists, "用户名已存在");
        // 生成强度较高的随机密码
        String password = RandomStringUtils.randomAlphanumeric(12);
        customer.setUsername(req.getUsername());
        customer.setPassword(DigestUtils.sha256Hex(password));
        this.updateById(customer);
        return CustomerOpenResp.builder()
                .customerName(customer.getName())
                .username(req.getUsername())
                .password(password)
                .build();
    }

    @Override
    public void resetPassword(Long id, CustomerOpenReq req) {
        CustomerDO customer = getById(id);
        CheckUtils.throwIfNull(customer, "客户不存在");
        CheckUtils.throwIfBlank(req.getPassword(), "请输入密码");

        customer.setPassword(DigestUtils.sha256Hex(req.getPassword()));
        this.updateById(customer);
    }

    @Override
    public void activation(CustomerActivationReq req) {
        lambdaUpdate().in(CustomerDO::getId, req.getIds())
                .eq(CustomerDO::getStatus, CustomerStatusEnum.TERMINATED)
                .set(CustomerDO::getStatus, CustomerStatusEnum.NORMAL)
                .set(CustomerDO::getCooperateTime, req.getCooperateTime())
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long rechargeFromRobot(Long customerId,
                                  BigDecimal amount,
                                  RechargeFeeHandleMethodEnum feeHandleMethodEnum,
                                  Long createUser,
                                  String remark) {
        // 计算充值手续费
        CustomerDO customer = this.getById(customerId);
        BigDecimal fee = BigDecimal.ZERO;
        if (feeHandleMethodEnum.equals(RechargeFeeHandleMethodEnum.REDUCE_FROM_RECHARGE_AMOUNT)) {
            fee = CommonUtils.divide100(amount.multiply(customer.getFeeRatePercent()), null);
        } else if (feeHandleMethodEnum.equals(RechargeFeeHandleMethodEnum.RECHARGE_INCLUDE_FEE)) {
            BigDecimal onePart = amount.divide(customer.getFeeRatePercent()
                    .add(BigDecimal.valueOf(100)), 4, RoundingMode.HALF_UP);
            fee = onePart.multiply(customer.getFeeRatePercent()).setScale(2, RoundingMode.HALF_UP);
        }
        // 发送额度汇总信息
        BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(customer.getId());
        Long id = this.changeAmount(customerId, null, amount, CustomerBalanceTypeEnum.SYSTEM, null, remark, null, createUser);
        this.changeAmount(customerId, null, fee.negate(), CustomerBalanceTypeEnum.RECHARGE_FEE, null, remark, null, createUser);
        CustomerBalanceChangeModel changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, amount, customer.getBalance(), amount.subtract(fee));
        SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
        return id;
    }

    @Override
    public PageResp<CustomerOrderStatisticsResp> selectCustomerOrderStatisticsPage(CustomerOrderStatisticsQuery query,
                                                                                   PageQuery pageQuery) {
        IPage<CustomerOrderStatisticsResp> page = baseMapper.selectCustomerOrderStatisticsPage(new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize()), query);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateBusinessUser(List<Long> customerIds, Long businessUserId) {
        CheckUtils.throwIf(CollUtil.isEmpty(customerIds), "客户ID列表不能为空");
        CheckUtils.throwIfNull(businessUserId, "关联商务用户ID不能为空");

        // 批量更新客户的关联商务
        LambdaUpdateWrapper<CustomerDO> updateWrapper = Wrappers.<CustomerDO>lambdaUpdate()
                .set(CustomerDO::getBusinessUserId, businessUserId)
                .in(CustomerDO::getId, customerIds);

        this.update(updateWrapper);

        // 批量插入商务对接表
        List<CustomerBusinessUserDO> customerBusinessUsers = customerIds.stream().map(customerId -> {
            CustomerBusinessUserDO customerBusinessUserDO = new CustomerBusinessUserDO();
            customerBusinessUserDO.setCustomerId(customerId);
            customerBusinessUserDO.setUserId(businessUserId);
            return customerBusinessUserDO;
        }).collect(Collectors.toList());
        customerBusinessUserService.saveBatch(customerBusinessUsers);

        CustomerVisitTaskService customerVisitTaskService = SpringUtil.getBean(CustomerVisitTaskService.class);
        // 关联客户的待处理、处理中的客户回访任务，变更下商务人员
        customerVisitTaskService.lambdaUpdate()
                .in(CustomerVisitTaskDO::getCustomerId, customerIds)
                .in(CustomerVisitTaskDO::getTaskStatus, VisitTaskStatusEnum.PENDING, VisitTaskStatusEnum.PENDING)
                .set(CustomerVisitTaskDO::getAssigneeId, businessUserId)
                .update();
        OpportunityService opportunityService = SpringUtil.getBean(OpportunityService.class);
        // 关联客户的跟进中、长期跟进中的商机，变更下商务人员
        opportunityService.lambdaUpdate()
                .in(OpportunityDO::getCustomerId, customerIds)
                .in(OpportunityDO::getStatus, OpportunityStatusEnum.FOLLOWING_UP, OpportunityStatusEnum.LONG_TERM_FOLLOW_UP)
                .set(OpportunityDO::getHandlerUserId, businessUserId)
                .update();
    }

    @Override
    public String selectCustomerNameByAdAccountId(String adAccountId) {
        return baseMapper.selectCustomerNameByAdAccountId(adAccountId);
    }

    @Override
    public PageResp<ToufangCustomerResp> selectToufangCustomerPage(CustomerQuery query, PageQuery pageQuery) {
        QueryWrapper<CustomerDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, pageQuery);
        IPage<ToufangCustomerResp> page = baseMapper.selectToufangCustomerPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        return PageResp.build(page);
    }

    @Override
    public List<ToufangCustomerResp> selectToufangCustomerList(CustomerQuery query, SortQuery sortQuery) {
        QueryWrapper<CustomerDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        return this.baseMapper.selectToufangCustomerList(queryWrapper);
    }

    @Override
    public void exportToufangCustomerDailyReport(Long customerId, HttpServletResponse response) {
        List<ToufangCustomerDailyReportResp> statList = adProductStatService.getCustomerDailyReport(customerId);
        List<CustomerBalanceRecordDO> tranferList = customerBalanceRecordService.list(Wrappers.<CustomerBalanceRecordDO>lambdaQuery()
                .eq(CustomerBalanceRecordDO::getCustomerId, customerId)
                .eq(CustomerBalanceRecordDO::getType, CustomerBalanceTypeEnum.SYSTEM));
        // BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(customerId);
        CustomerDO customerDO = this.getById(customerId);
        List<List<String>> headList = this.getToufangDailyReportExcelHead(customerDO.getEffectTemplate(), customerDO.getDataTemplate());
        List<List<Object>> dataList = this.getToufangDailyReportExcelData(customerDO, statList, tranferList);
        // EasyExcel.write(new File("C:\\Users\\<USER>\\Downloads\\1.xlsx")).head(headList).sheet().doWrite(dataList);
        try {
            String exportFileName = URLUtil.encode("%s_%s.xlsx".formatted(customerDO.getName(), DateUtil.format(new Date(), "yyyyMMddHHmmss")));
            response.setHeader("Content-disposition", "attachment;filename=" + exportFileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            EasyExcel.write(response.getOutputStream())
                    .head(headList)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("Sheet1")
                    .doWrite(dataList);
        } catch (Exception var7) {
            throw new BaseException("导出 Excel 出现错误");
        }
    }

    @Override
    public void toufangRefund(ToufangRefundReq req) {

        CustomerWithdrawOrderService customerWithdrawOrderService = SpringUtil.getBean(CustomerWithdrawOrderService.class);
        CustomerWithdrawOrderReq customerWithdrawOrderReq = new CustomerWithdrawOrderReq();
        customerWithdrawOrderReq.setCustomerId(req.getId());
        customerWithdrawOrderReq.setRemark(req.getRemark());
        customerWithdrawOrderService.add(customerWithdrawOrderReq);
    }

    @Override
    public PageResp<Map<String, Object>> getToufangCustomerDailyPage(Long customerId, PageQuery pageQuery) {
        CustomerDO customer = this.getById(customerId);

        CheckUtils.throwIfNull(customer, "客户不存在");

        // 获取统计数据和转账数据
        List<ToufangCustomerDailyReportResp> statList = adProductStatService.getCustomerDailyReport(customerId);
        List<CustomerBalanceRecordDO> tranferList = customerBalanceRecordService.list(Wrappers.<CustomerBalanceRecordDO>lambdaQuery()
                .eq(CustomerBalanceRecordDO::getCustomerId, customerId)
                .eq(CustomerBalanceRecordDO::getType, CustomerBalanceTypeEnum.SYSTEM));

        // 获取表头和数据
        List<List<String>> headList = this.getToufangDailyReportExcelHead(customer.getEffectTemplate(), customer.getDataTemplate());
        List<List<Object>> dataList = this.getToufangDailyReportExcelData(customer, statList, tranferList);

        // 提取列名（过滤掉前两列"结余"和"合计"）
        List<String> columnNames = headList.stream()
                .map(head -> head.get(2)) // 取第3个元素作为列名
                .toList();

        // 组装数据（过滤掉前两列）
        List<Map<String, Object>> resultList = dataList.stream()
                .map(row -> {
                    Map<String, Object> rowMap = new LinkedHashMap<>();

                    for (int i = 0; i < columnNames.size(); i++) {
                        rowMap.put(columnNames.get(i), getCellValue(row, i));
                    }
                    return rowMap;
                })
                .collect(Collectors.toList());

        // 应用分页
        int total = resultList.size();
        int start = (pageQuery.getPage() - 1) * pageQuery.getSize();
        int end = Math.min(start + pageQuery.getSize(), total);

        List<Map<String, Object>> pagedResult = resultList.subList(start, end);

        return new PageResp<>(pagedResult, total);
    }

    @Override
    public void exportToufangCustomerDaily(Long customerId, HttpServletResponse response) {

        CustomerDO customer = this.getById(customerId);

        CheckUtils.throwIfNull(customer, "客户不存在");

        // 获取统计数据和转账数据
        List<ToufangCustomerDailyReportResp> statList = adProductStatService.getCustomerDailyReport(customerId);
        List<CustomerBalanceRecordDO> tranferList = customerBalanceRecordService.list(Wrappers.<CustomerBalanceRecordDO>lambdaQuery()
                .eq(CustomerBalanceRecordDO::getCustomerId, customerId)
                .eq(CustomerBalanceRecordDO::getType, CustomerBalanceTypeEnum.SYSTEM));

        // 获取表头和数据
        List<List<String>> headList = this.getToufangDailyReportExcelHead(customer.getEffectTemplate(), customer.getDataTemplate());
        List<List<Object>> dataList = this.getToufangDailyReportExcelData(customer, statList, tranferList);

        List<List<String>> columnNames = headList.stream()
                .map(head -> List.of(head.get(2))) // 取第3个元素作为列名，并包装成单元素List
                .toList();
        for (List<Object> row : dataList) {
            // 遍历内层List（即每一行）
            for (int i = 0; i < row.size(); i++) {
                Object cell = row.get(i);
                // 检查是否是字符串类型且内容为 "-"
                if ("-".equals(cell)) {
                    row.set(i, ""); // 将 "-" 替换为空字符串
                }
            }
        }
        try {
            String exportFileName = URLUtil.encode("%s_%s.xlsx".formatted(customer.getName(), DateUtil.format(new Date(), "yyyyMMddHHmmss")));
            response.setHeader("Content-disposition", "attachment;filename=" + exportFileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            EasyExcel.write(response.getOutputStream())
                    .head(columnNames)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("Sheet1")
                    .doWrite(dataList);
        } catch (Exception var7) {
            throw new BaseException("导出 Excel 出现错误");
        }
    }

    private Object getCellValue(List<Object> row, int i) {
        try {
            Object o = row.get(i);
            if (o == null) {
                return "";
            }
            if (o.equals("-")) {
                return "";
            }
            return o;
        } catch (IndexOutOfBoundsException e) {
            return "";
        }
    }

    private List<List<String>> getToufangDailyReportExcelHead(String effectTemplate, String dataTemplate) {
        List<List<String>> headList = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("结余");
        head0.add("合计");
        head0.add("日期");
        List<String> head1 = new ArrayList<>();
        head1.add("=D2-H2");
        head1.add("");
        head1.add("代理线");
        List<String> head2 = new ArrayList<>();
        head2.add("");
        head2.add("");
        head2.add("产品");
        List<String> head3 = new ArrayList<>();
        head3.add("");
        head3.add("=SUM(D4,D10000)");
        head3.add("打款");
        List<String> head4 = new ArrayList<>();
        head4.add("");
        head4.add("=SUM(E4,E10000)");
        head4.add("日消耗");
        List<String> head5 = new ArrayList<>();
        head5.add("");
        head5.add("=SUM(F4,F10000)");
        head5.add("回流");
        List<String> head6 = new ArrayList<>();
        head6.add("");
        head6.add("=SUM(G4,G10000)");
        head6.add("服务费");
        List<String> head7 = new ArrayList<>();
        head7.add("");
        head7.add("=SUM(H4,H10000)");
        head7.add("实际花费");
        List<String> head8 = new ArrayList<>();
        head8.add("");
        head8.add("=SUM(I4,I10000)");
        head8.add("承担费用");
        headList.add(head0);
        headList.add(head1);
        headList.add(head2);
        headList.add(head3);
        headList.add(head4);
        headList.add(head5);
        headList.add(head6);
        headList.add(head7);
        headList.add(head8);
        String chatList = RandomUtil.BASE_CHAR.toUpperCase();
        int index = 8;
        if (JSON.isValidArray(effectTemplate)) {
            JSONArray effectTemplateArray = JSON.parseArray(effectTemplate);
            for (int i = 0; i < effectTemplateArray.size(); i++) {
                JSONObject effectTemplateObject = effectTemplateArray.getJSONObject(i);
                List<String> head = new ArrayList<>();
                head.add("");
                head.add("=SUM(%s4,%s10000)".formatted(chatList.charAt(index), chatList.charAt(index)));
                head.add(effectTemplateObject.getString("key"));
                headList.add(head);
                index++;
            }
        }
        if (JSON.isValidArray(dataTemplate)) {
            JSONArray dataTemplateArray = JSON.parseArray(dataTemplate);
            for (int i = 0; i < dataTemplateArray.size(); i++) {
                JSONObject dataTemplateObject = dataTemplateArray.getJSONObject(i);
                List<String> head = new ArrayList<>();
                head.add("");
                head.add("=SUM(%s4,%s10000)".formatted(chatList.charAt(index), chatList.charAt(index)));
                head.add(dataTemplateObject.getString("key"));
                headList.add(head);
                index++;
            }
        }
        return headList;
    }

    private List<List<Object>> getToufangDailyReportExcelData(CustomerDO customer,
                                                              List<ToufangCustomerDailyReportResp> statList,
                                                              List<CustomerBalanceRecordDO> tranferList) {
        List<List<Object>> list = new ArrayList<>();
        for (ToufangCustomerDailyReportResp item : statList) {
            String date = LocalDateTimeUtil.format(item.getStatDate(), "yyyy年MM月dd日");
            // 判断是否有同一天的打款记录
            List<CustomerBalanceRecordDO> transfers = tranferList.stream()
                    .filter(v -> LocalDateTimeUtil.isSameDay(v.getTransTime().toLocalDate(), item.getStatDate()))
                    .toList();
            if (!transfers.isEmpty()) {
                for (CustomerBalanceRecordDO transfer : transfers) {
                    List<Object> column = new ArrayList<>();
                    column.add(date);
                    column.add("-");
                    column.add("-");
                    column.add(transfer.getAmount());
                    List<Object> data = this.fillToufangDailyReportExcelData(column, customer.getEffectTemplate(), customer.getDataTemplate(), "", "");
                    list.add(data);
                }
                tranferList.removeIf(v -> LocalDateTimeUtil.isSameDay(v.getTransTime()
                        .toLocalDate(), item.getStatDate()));
            }
            List<Object> column = new ArrayList<>();
            column.add(date);
            column.add(item.getProductName());
            column.add(AdProductTypeEnum.getDescription(item.getProductType()));
            column.add("");
            column.add(item.getSpend());
            column.add(item.getReflowSpend());
            column.add(item.getFee());
            column.add(item.getTotalSpend());
            column.add(item.getBearCost());
            List<Object> data = this.fillToufangDailyReportExcelData(column, customer.getEffectTemplate(), customer.getDataTemplate(), item.getEffectData(), item.getExtraData());
            list.add(data);
        }
        return list;
    }

    private List<Object> fillToufangDailyReportExcelData(List<Object> data,
                                                         String effectTemplate,
                                                         String dataTemplate,
                                                         String effectData,
                                                         String extraData) {
        if (JSON.isValidArray(effectTemplate)) {
            JSONObject effectJson = new JSONObject();
            if (JSON.isValidArray(effectData)) {
                JSONArray effectDataArray = JSON.parseArray(effectData);
                for (int i = 0; i < effectDataArray.size(); i++) {
                    JSONObject effectDataObject = effectDataArray.getJSONObject(i);
                    effectJson.put(effectDataObject.getString("key"), effectDataObject.getString("value"));
                }
            }
            JSONArray effectTemplateArray = JSON.parseArray(effectTemplate);
            for (int i = 0; i < effectTemplateArray.size(); i++) {
                JSONObject effectTemplateObject = effectTemplateArray.getJSONObject(i);
                data.add(effectJson.getOrDefault(effectTemplateObject.getString("key"), "-"));
            }
        }
        if (JSON.isValidArray(dataTemplate)) {
            JSONObject extraJson = new JSONObject();
            if (JSON.isValidArray(extraData)) {
                JSONArray extraDataArray = JSON.parseArray(extraData);
                for (int i = 0; i < extraDataArray.size(); i++) {
                    JSONObject extraDataObject = extraDataArray.getJSONObject(i);
                    extraJson.put(extraDataObject.getString("key"), extraDataObject.getString("value"));
                }
            }
            JSONArray dataTemplateArray = JSON.parseArray(dataTemplate);
            for (int i = 0; i < dataTemplateArray.size(); i++) {
                JSONObject dataTemplateObject = dataTemplateArray.getJSONObject(i);
                data.add(extraJson.getOrDefault(dataTemplateObject.getString("key"), "-"));
            }
        }
        return data;
    }

    private void syncData(CustomerDailyExcel excel, CustomerBalanceRecordDO record) {
        if (record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_RECHARGE) {
            excel.setRechargeAmount(record.getAmount());
        } else if (record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_CLEAR || record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_REDUCE) {
            excel.setWithdrawalAmount(record.getAmount());
        } else if (record.getType() == CustomerBalanceTypeEnum.DEDUCE) {
            excel.setRefund(record.getAmount());
        } else if (record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_BUY) {
            excel.setOpeningFee(record.getAmount());
            excel.setRechargeAdAccountAmount(record.getAmount().negate());
        } else if (record.getType() == CustomerBalanceTypeEnum.MIGRATE) {
            if (record.getAction().equals(TransactionActionEnum.IN)) {
                excel.setAdjustment(record.getAmount());
            } else {
                excel.setAdjustment(record.getAmount().negate());
            }
        } else if (record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_REFUND) {
            excel.setOpeningFee(record.getAmount().negate());
            excel.setRechargeAdAccountAmount(record.getAmount());
        }
    }
}