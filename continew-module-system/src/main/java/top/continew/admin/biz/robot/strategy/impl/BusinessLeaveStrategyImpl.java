package top.continew.admin.biz.robot.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.ScheduleTypeEnum;
import top.continew.admin.biz.model.entity.SalesScheduleDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotLeaveReq;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.SalesScheduleService;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.admin.system.service.UserService;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;

@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessLeaveStrategyImpl implements RobotCommandStrategy {

    private final UserService userService;

    private final SalesScheduleService salesScheduleService;

    @Value("${businessTelegram.chatId}")
    private Long chatId;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.BUSINESS_LEAVE;
    }

    @Override
    public String execute(Update update) {

        if (!update.getMessage().getChatId().equals(chatId)) {
            return null;
        }

        RobotLeaveReq req = BotUtils.parseBotMsgToObject(update.getMessage().getText(), "\n", ":", RobotLeaveReq.class);
        log.info("{}收到一条请假请求：{}", getCommand().getDescription(), JSONObject.toJSONString(req));
        LocalDate date;
        try {
            date = LocalDate.parse(req.getDate());
        } catch (DateTimeParseException e) {
            return "❌ 日期格式错误，示例2025-03-22";
        }

        String userName = update.getMessage().getFrom().getUserName();

        UserDO user = userService.selectByTelegramId(userName);
        if (user == null) {
            return "❌ 没找到对应的用户，请联系管理员";
        }
        SalesScheduleDO salesSchedule = new SalesScheduleDO();
        salesSchedule.setScheduleType(ScheduleTypeEnum.LEAVE);
        salesSchedule.setScheduleDate(date);
        salesSchedule.setSalesId(user.getId());
        salesScheduleService.telegramSave(salesSchedule);

        return "✅ 请假信息提交成功！";
    }
}
