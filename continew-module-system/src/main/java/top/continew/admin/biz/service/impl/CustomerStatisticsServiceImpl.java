package top.continew.admin.biz.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.CustomerStatisticsMapper;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.BusinessStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerSpentTrendQuery;
import top.continew.admin.biz.model.query.CustomerStatisticsQuery;
import top.continew.admin.biz.model.resp.BusinessStatisticsResp;
import top.continew.admin.biz.model.resp.CustomerSpentTrendResp;
import top.continew.admin.biz.model.resp.CustomerStatisticsOverviewResp;
import top.continew.admin.biz.model.resp.CustomerStatisticsResp;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.CustomerStatisticsService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/1/23 10:45
 */

/**
 * 客户统计服务实现
 */
@Service
@RequiredArgsConstructor
public class CustomerStatisticsServiceImpl implements CustomerStatisticsService {

    private final CustomerStatisticsMapper customerStatisticsMapper;

    private final CustomerService customerService;

    @Override
    public PageResp<CustomerStatisticsResp> page(CustomerStatisticsQuery query) {
        // 1. 分页查询基础数据
        IPage<CustomerStatisticsResp> page = customerStatisticsMapper.selectStatisticsWithCondition(new Page<>(query.getPage(), query.getSize()), query);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public CustomerStatisticsOverviewResp selectCustomerStatSummary(CustomerStatisticsQuery query) {
        CustomerStatisticsOverviewResp res = customerStatisticsMapper.selectCustomerStatisticsSummary(query);
        List<CustomerDO> customerList = customerService.list();
        res.setOccupiedAmount(res.getOccupiedAmount()
            .add(customerList.stream().map(CustomerDO::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add)));
        return customerStatisticsMapper.selectCustomerStatisticsSummary(query);
    }

    @Override
    public List<CustomerStatisticsResp> listCustomerStat(CustomerStatisticsQuery query) {
        return customerStatisticsMapper.selectCustomerStatisticsList(query);
    }

    @Override
    public PageResp<BusinessStatisticsResp> businessPage(BusinessStatisticsQuery query) {
        IPage<BusinessStatisticsResp> page = new Page<>(query.getPage(), query.getSize());
        List<BusinessStatisticsResp> records = customerStatisticsMapper.selectBusinessStatistics(page, query);

        records.forEach(item -> {
            // 计算死户率
            if (item.getTotalAccount() > 0) {
                item.setDeadRate(BigDecimal.valueOf(item.getDeadAccount())
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(item.getTotalAccount()), 2, RoundingMode.HALF_UP));
            } else {
                item.setDeadRate(BigDecimal.ZERO);
            }

            // 计算平均户充值
            if (item.getTotalAccount() > 0 && item.getTotalRecharge() != null) {
                item.setAverageRecharge(item.getTotalRecharge()
                    .divide(BigDecimal.valueOf(item.getTotalAccount()), 2, RoundingMode.HALF_UP));
            } else {
                item.setAverageRecharge(BigDecimal.ZERO);
            }

            // 计算平均户消耗
            if (item.getTotalAccount() > 0 && item.getTotalSpend() != null) {
                item.setAverageSpend(item.getTotalSpend()
                    .divide(BigDecimal.valueOf(item.getTotalAccount()), 2, RoundingMode.HALF_UP));
            } else {
                item.setAverageSpend(BigDecimal.ZERO);
            }
        });

        return new PageResp<>(records, page.getTotal());
    }

    @Override
    public List<BusinessStatisticsResp> listBusinessData(BusinessStatisticsQuery query) {
        return this.customerStatisticsMapper.selectBusinessStatisticsList(query);
    }

    @Override
    public PageResp<CustomerSpentTrendResp> selectCustomerSpentTrendList(CustomerSpentTrendQuery customerSpentTrendQuery,
                                                                         PageQuery pageQuery) {
        IPage<CustomerSpentTrendResp> page = new Page<>(pageQuery.getPage(), pageQuery.getSize());
        QueryWrapper<CustomerSpentTrendResp> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("c.type", 1);
        queryWrapper.eq(customerSpentTrendQuery.getCustomerId() != null, "c.id", customerSpentTrendQuery.getCustomerId());
        queryWrapper.eq(customerSpentTrendQuery.getBusinessUserId() != null, "c.business_user_id", customerSpentTrendQuery.getBusinessUserId());
        Sort sort = pageQuery.getSort();
        for (Sort.Order order : sort) {
            String property = order.getProperty();
            queryWrapper.orderBy(true, order.isAscending(), CharSequenceUtil.toUnderlineCase(property));
        }
        IPage<CustomerSpentTrendResp> list = this.customerStatisticsMapper.selectCustomerSpentTrendList(page, queryWrapper);
        for (CustomerSpentTrendResp customerSpentTrendResp : list.getRecords()) {
            if (customerSpentTrendResp.getDayBeforeYesterdaySpent().compareTo(BigDecimal.ZERO) == 0) {
                customerSpentTrendResp.setYesterdayGrowRate(new BigDecimal(customerSpentTrendResp.getYesterdaySpent()
                    .compareTo(BigDecimal.ZERO) == 0 ? "0" : "1").setScale(4, RoundingMode.HALF_UP));
            } else {
                customerSpentTrendResp.setYesterdayGrowRate(NumberUtil.div(customerSpentTrendResp.getYesterdaySpent()
                    .subtract(customerSpentTrendResp.getDayBeforeYesterdaySpent()), customerSpentTrendResp.getDayBeforeYesterdaySpent(), 4));
            }

            if (customerSpentTrendResp.getYesterdaySpent().compareTo(BigDecimal.ZERO) == 0) {
                customerSpentTrendResp.setTodayGrowRate(new BigDecimal(customerSpentTrendResp.getTodaySpent()
                    .compareTo(BigDecimal.ZERO) == 0 ? "0" : "1").setScale(4, RoundingMode.HALF_UP));
            } else {
                customerSpentTrendResp.setTodayGrowRate(NumberUtil.div(customerSpentTrendResp.getTodaySpent()
                    .subtract(customerSpentTrendResp.getYesterdaySpent()), customerSpentTrendResp.getYesterdaySpent(), 4));
            }
        }
        return new PageResp<>(list.getRecords(), list.getTotal());
    }
}
