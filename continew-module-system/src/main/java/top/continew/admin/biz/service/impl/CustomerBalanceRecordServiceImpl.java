/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.enums.TransactionActionEnum;
import top.continew.admin.biz.mapper.CustomerBalanceRecordMapper;
import top.continew.admin.biz.model.entity.CustomerBalanceRecordDO;
import top.continew.admin.biz.model.entity.WalletTransferDO;
import top.continew.admin.biz.model.query.CustomerBalanceRecordQuery;
import top.continew.admin.biz.model.req.CustomerBalanceRecordReq;
import top.continew.admin.biz.model.resp.CustomerBalanceRecordDetailResp;
import top.continew.admin.biz.model.resp.CustomerBalanceRecordResp;
import top.continew.admin.biz.service.CustomerBalanceRecordService;
import top.continew.admin.biz.service.WalletTransferService;
import top.continew.admin.biz.utils.CustomerHelper;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户余额变更记录业务实现
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
@Service
@RequiredArgsConstructor
@Slf4j  // 添加日志注解
public class CustomerBalanceRecordServiceImpl extends BaseServiceImpl<CustomerBalanceRecordMapper, CustomerBalanceRecordDO, CustomerBalanceRecordResp, CustomerBalanceRecordDetailResp, CustomerBalanceRecordQuery, CustomerBalanceRecordReq> implements CustomerBalanceRecordService {

    private final WalletTransferService walletTransferService;


//    @Override
//    public PageResp<CustomerBalanceRecordResp> page(CustomerBalanceRecordQuery query, PageQuery pageQuery) {
//        QueryWrapper<CustomerBalanceRecordDO> wrapper = this.buildQueryWrapper(query);
//        IPage<CustomerBalanceRecordResp> page = this.baseMapper.customPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), wrapper);
//        return new PageResp<>(page.getRecords(), page.getTotal());
//    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> ids) {
        // 1. 检查是否只删除一条记录
        if (CollUtil.isEmpty(ids) || ids.size() > 1) {
            throw new IllegalArgumentException("一次只能删除一条记录");
        }

        // 2. 获取要删除的记录
        CustomerBalanceRecordDO record = getById(ids.get(0));
        if (record == null) {
            return;
        }
        log.info("删除余额变更-记录ID：{}，客户ID：{}，交易类型：{}，金额：{}，交易后余额：{}", record.getId(), record.getCustomerId(), record.getAction(), record.getAmount(), record.getAfterAmount());

        // 3. 更新客户余额
        BigDecimal amount = record.getAmount();
        if (record.getAction() == TransactionActionEnum.IN) {
            // 如果是收入，则减少余额
            CustomerHelper.reduceBalance(record.getCustomerId(), amount);
            log.info("删除余额变更-减少客户余额，客户ID：{}，减少金额：{}", record.getCustomerId(), amount);
        } else {
            // 如果是支出，则增加余额
            CustomerHelper.addBalance(record.getCustomerId(), amount);
            log.info("删除余额变更-增加客户余额，客户ID：{}，增加金额：{}", record.getCustomerId(), amount);
        }

        // 4. 获取删除记录之后的所有记录
        List<CustomerBalanceRecordDO> laterRecords = this.list(Wrappers.<CustomerBalanceRecordDO>lambdaQuery()
            .eq(CustomerBalanceRecordDO::getCustomerId, record.getCustomerId())
            .gt(CustomerBalanceRecordDO::getId, record.getId())
            .orderByAsc(CustomerBalanceRecordDO::getTransTime, CustomerBalanceRecordDO::getId));

        // 5. 删除记录
        baseMapper.deleteById(record.getId());

        // 6. 如果有后续记录，更新它们的余额
        if (!laterRecords.isEmpty()) {
            List<CustomerBalanceRecordDO> updateList = new ArrayList<>();
            // 计算删除记录后的起始余额
            BigDecimal preRecordAfterAmount;
            if (record.getAction() == TransactionActionEnum.IN) {
                // 如果删除的是收入记录，则起始余额应该是：当前记录的交易后余额 - 收入金额
                preRecordAfterAmount = record.getAfterAmount().subtract(record.getAmount());
            } else {
                // 如果删除的是支出记录，则起始余额应该是：当前记录的交易后余额 + 支出金额
                preRecordAfterAmount = record.getAfterAmount().add(record.getAmount());
            }

            // 从删除记录的下一条开始调整
            for (CustomerBalanceRecordDO laterRecord : laterRecords) {
                if (laterRecord.getAction().equals(TransactionActionEnum.IN)) {
                    preRecordAfterAmount = preRecordAfterAmount.add(laterRecord.getAmount());
                } else {
                    preRecordAfterAmount = preRecordAfterAmount.subtract(laterRecord.getAmount());
                }
                CustomerBalanceRecordDO update = new CustomerBalanceRecordDO();
                update.setId(laterRecord.getId());
                update.setAfterAmount(preRecordAfterAmount);
                updateList.add(update);

                log.info("删除余额变更-更新后续记录，记录ID：{}, 交易金额：{}，交易类型：{}, 交易后金额[前]:{}，交易后金额[后]：{}", laterRecord.getId(), laterRecord.getAmount(), laterRecord.getAction(), laterRecord.getAfterAmount(), update.getAfterAmount());

            }

            if (CollUtil.isNotEmpty(updateList)) {
                this.updateBatchById(updateList);
            }
        }

    }

    @Override
    public BigDecimal getTotalAmountByType(Long customerId, CustomerBalanceTypeEnum typeEnum) {
        return this.getObj(Wrappers.<CustomerBalanceRecordDO>query()
            .select("ifnull(sum(amount),0)")
            .eq("customer_id", customerId)
            .eq("type", typeEnum), v -> {
            if (v == null) {
                return BigDecimal.ZERO;
            } else {
                return new BigDecimal(String.valueOf(v));
            }
        });
    }

    @Override
    public void adjustAfterAmount(Long customerId, Long recordId) {
        List<CustomerBalanceRecordDO> list = this.list(Wrappers.<CustomerBalanceRecordDO>lambdaQuery()
            .eq(CustomerBalanceRecordDO::getCustomerId, customerId)
            .orderByDesc(CustomerBalanceRecordDO::getTransTime, CustomerBalanceRecordDO::getId));
        List<CustomerBalanceRecordDO> updateList = new ArrayList<>();
        int startIndex = 0;
        if (recordId != null) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getId().equals(recordId)) {
                    startIndex = i;
                    break;
                }
            }
        }
        BigDecimal preRecordAfterAmount = startIndex == list.size() - 1
            ? BigDecimal.ZERO
            : list.get(startIndex + 1).getAfterAmount();
        for (int i = startIndex; i >= 0; i--) {
            CustomerBalanceRecordDO record = list.get(i);
            CustomerBalanceRecordDO update = new CustomerBalanceRecordDO();
            update.setId(record.getId());
            if (record.getAction().equals(TransactionActionEnum.IN)) {
                preRecordAfterAmount = preRecordAfterAmount.add(record.getAmount());
            } else {
                preRecordAfterAmount = preRecordAfterAmount.subtract(record.getAmount());
            }
            update.setAfterAmount(preRecordAfterAmount);
            updateList.add(update);
        }
        this.updateBatchById(updateList);
    }

    @Override
    public BigDecimal getTotalRechargeAmount(Long customerId, String platformAdId, LocalDateTime orderTime) {
        return this.baseMapper.getTotalRechargeAmount(customerId, platformAdId, orderTime);
    }

    @Override
    protected void sort(QueryWrapper<CustomerBalanceRecordDO> queryWrapper, SortQuery sortQuery) {
        queryWrapper.orderByDesc("trans_time", "id");
    }

    @Override
    public void uploadCertificate(String id, String fileUrl) {
        if (StringUtils.isBlank(id)) {
            throw new BusinessException("记录不存在");
        }
        CustomerBalanceRecordDO item = this.getById(id);
        if (item == null) {
            throw new BusinessException("记录不存在");
        }
        CustomerBalanceRecordDO updateRecord = new CustomerBalanceRecordDO();
        updateRecord.setId(item.getId());
        updateRecord.setCertificate(fileUrl);
        this.updateById(updateRecord);
    }

    @Override
    public void bindWalletTransId(Long id, String transactionId) {
        CustomerBalanceRecordDO customerBalanceRecord = this.getById(id);
        WalletTransferDO walletTransfer = walletTransferService.getOne(Wrappers.<WalletTransferDO>lambdaQuery()
            .eq(WalletTransferDO::getTransactionId, transactionId));
        if (customerBalanceRecord.getAmount().compareTo(walletTransfer.getAmount()) != 0) {
            throw new BusinessException("交易流水金额不匹配，无法绑定");
        }
        CustomerBalanceRecordDO updateRecord = new CustomerBalanceRecordDO();
        updateRecord.setId(id);
        updateRecord.setWalletTransactionId(transactionId);
        this.updateById(updateRecord);
    }

    @Override
    public BigDecimal getTotalTransferAmount(Long customerId) {
        return this.baseMapper.getTotalTransferAmount(customerId);
    }

    @Override
    public BigDecimal getAdAccountTotalRechargeAmount(Long customerId, String platformAdId, LocalDateTime orderTime) {
        List<CustomerBalanceRecordDO> list = this.list(Wrappers.<CustomerBalanceRecordDO>lambdaQuery()
            .eq(CustomerBalanceRecordDO::getCustomerId, customerId)
            .eq(CustomerBalanceRecordDO::getPlatformAdId, platformAdId)
            .gt(CustomerBalanceRecordDO::getTransTime, orderTime));
        return list.stream().map(CustomerBalanceRecordDO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}