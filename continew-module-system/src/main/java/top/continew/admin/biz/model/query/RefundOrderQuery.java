/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.FbLimitCheckStatusEnum;
import top.continew.admin.biz.enums.RefundOrderCardStatusEnum;
import top.continew.admin.biz.enums.RefundOrderStatusEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 退款订单查询条件
 *
 * <AUTHOR>
 * @since 2025/01/10 15:39
 */
@Data
@Schema(description = "退款订单查询条件")
public class RefundOrderQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @Query(type = QueryType.EQ)
    private String orderNo;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @Query(type = QueryType.EQ, columns = {"o.platform_ad_id"})
    private String platformAdId;

    /**
     * 卡台状态
     */
    @Schema(description = "卡台状态")
    @Query(type = QueryType.EQ)
    private RefundOrderCardStatusEnum cardStatus;

    /**
     * fb检测状态
     */
    @Schema(description = "fb检测状态")
    @Query(type = QueryType.EQ)
    private FbLimitCheckStatusEnum fbCheckStatus;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @Query(type = QueryType.EQ, columns = {"o.status"})
    private RefundOrderStatusEnum status;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    @Query(type = QueryType.EQ)
    private Long handleUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN, columns = {"o.create_time"})
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;

    @Schema(description = "客户业务类型")
    @Query(type = QueryType.EQ,columns = {"c.business_type"})
    private Integer businessType;
}