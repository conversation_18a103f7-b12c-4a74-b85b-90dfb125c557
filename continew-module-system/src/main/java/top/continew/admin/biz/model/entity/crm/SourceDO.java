package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.annotation.DictField;
import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 来源实体
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@TableName("biz_source")
@DictField
public class SourceDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源分组ID
     */
    private Long groupId;

    /**
     * 来源名称
     */
    private String name;
}