/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.utils;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import top.continew.admin.biz.enums.FbAccountStatusEnum;
import top.continew.katai.DaziRequest;
import top.continew.katai.DaziResponse;
import top.continew.katai.RuntimeOptions;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.utils.Common;
import top.continew.katai.utils.Third;
import top.continew.starter.core.exception.BusinessException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FacebookUtils {

    private static final JSONArray timeZoneArray = JSONArray.parseArray(ResourceUtil.readUtf8Str("data/timeZone.json"));

    public static FbAccountStatusEnum check(String cookieStr, String proxyStr, String ua) {
        if (StringUtils.isBlank(cookieStr)) {
            return null;
        }
        String[] proxyArr = proxyStr.split(":");
        int port = Integer.parseInt(proxyArr[1]);
        String proxy = "https://%s:%s@%s:%s".formatted(proxyArr[2], proxyArr[3], proxyArr[0], port);
        JSONArray jsonArray = JSONArray.parseArray(cookieStr);
        StringBuilder cookie = new StringBuilder();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = jsonArray.getJSONObject(i);
            cookie.append(item.getString("name")).append("=").append(item.getString("value")).append(";");
        }
        DaziRequest request = new DaziRequest();
        request.protocol = "https";
        request.method = "GET";
        request.pathname = "/";
        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        headers.put("accept-language", "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,ja;q=0.6");
        headers.put("dpr", "1.5");
        headers.put("priority", "u=0, i");
        headers.put("upgrade-insecure-requests", "1");
        headers.put("viewport-width", "1476");
        headers.put("user-agent", ua);
        headers.put("cookie", cookie.substring(0, cookie.length() - 1));
        headers.put("host", "mbasic.facebook.com");
        request.headers = headers;
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        runtimeOptions.setConnectTimeout(5000);
        runtimeOptions.setReadTimeout(5000);
        runtimeOptions.setHttpsProxy(proxy);
        int time = 1;
        while (time < 4) {
            try {
                DaziResponse response = Third.doAction(request, runtimeOptions);
                Common.readAsString(response.body);
                String location = response.headers.get("location");
                if (StringUtils.isBlank(location)) {
                    String setCookie = response.headers.get("set-cookie");
                    if (StringUtils.isNotBlank(setCookie) && setCookie.contains("deleted")) {
                        return FbAccountStatusEnum.LOGOUT;
                    }
                    return FbAccountStatusEnum.NORMAL;
                }
                if (location.contains("mbasic.facebook.com/login")) {
                    return FbAccountStatusEnum.LOGOUT;
                }
                if (location.contains("mbasic.facebook.com/checkpoint")) {
                    return FbAccountStatusEnum.LOCKED;
                }
                if (location.contains("confirmemail.php")) {
                    return FbAccountStatusEnum.CONFIRM_EMAIL;
                }

            } catch (ThirdException e) {
                log.info("【个号验活】验活失败:{}", ExceptionUtils.getStackTrace(e));
                time++;
            } catch (Exception e) {
                log.info("【个号验活】验活失败:{}", ExceptionUtils.getStackTrace(e));
                break;
            }
        }
        return FbAccountStatusEnum.WAIT;
    }

    public static void checkIpAddress(String proxyStr) {
        String[] proxyArr = proxyStr.split(":");
        String proxy = "https://%s:%s@%s:%s".formatted(proxyArr[2], proxyArr[3], proxyArr[0], proxyArr[1]);
        DaziRequest request = new DaziRequest();
        request.protocol = "https";
        request.method = "GET";
        request.pathname = "/json";
        Map<String, String> headers = new HashMap<>();
        headers.put("host", "myip.ipip.net");
        request.headers = headers;
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        runtimeOptions.setConnectTimeout(5000);
        runtimeOptions.setReadTimeout(5000);
        runtimeOptions.setHttpsProxy(proxy);
        DaziResponse response = Third.doAction(request, runtimeOptions);
        System.out.println(Common.readAsString(response.body));
    }

    public static String getTimezone(String timezoneName) {
        if (StringUtils.isBlank(timezoneName)) {
            return null;
        }
        for (int i = 0; i < timeZoneArray.size(); i++) {
            JSONObject item = timeZoneArray.getJSONObject(i);
            if (timezoneName.equals(item.getString("value"))) {
                return ReUtil.get("\\(([^)]+)\\)", item.getString("label"), 1);
            }
        }
        return "";
    }

    /**
     * 获取广告户数据
     *
     * @param browserNo
     * @param platformAdId
     * @return
     */
    public static JSONObject getAdAccountDetail(String browserNo, String platformAdId) {
        DaziRequest request = new DaziRequest();
        request.protocol = "https";
        request.method = "GET";
        request.pathname = "/getAdAccountDetails";
        Map<String, String> query = new HashMap<>();
        query.put("envId", browserNo);
        query.put("adAccountId", platformAdId);
        request.query = query;
        Map<String, String> headers = new HashMap<>();
        headers.put("host", "fb.ngrok.pro");
        request.headers = headers;
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        runtimeOptions.setConnectTimeout(20000);
        runtimeOptions.setReadTimeout(20000);
        try {
            DaziResponse response = Third.doAction(request, runtimeOptions);
            String result = Common.readAsString(response.body);
            JSONObject json = JSON.parseObject(result);
            int code = json.getIntValue("code");
            if (code != 0) {
                throw new BusinessException("获取广告户数据失败：" + json.getString("msg"));
            }
            return json.getJSONObject("data");
        } catch (ThirdException e) {
            throw new BusinessException(StringUtils.defaultIfBlank(e.getMessage(), e.getCause().getMessage()));
        }
    }

    /**
     * 获取账号消耗截图
     *
     * @param spendLimit
     * @param spend
     * @return
     */
    public static String generateBillingScreen(BigDecimal spendLimit, BigDecimal spend) {
        // http://**************/spent?limit=500&spent=10
        DaziRequest request = new DaziRequest();
        request.protocol = "http";
        request.method = "GET";
        request.pathname = "/spent";
        Map<String, String> query = new HashMap<>();
        query.put("limit", NumberUtil.toStr(spendLimit));
        query.put("spent", NumberUtil.toStr(spend));
        request.query = query;
        Map<String, String> headers = new HashMap<>();
        headers.put("host", "**************");
        request.headers = headers;
        try {
            DaziResponse response = Third.doAction(request);
            String result = Common.readAsString(response.body);
            JSONObject json = JSON.parseObject(result);
            int code = json.getIntValue("code");
            if (code != 0) {
                throw new BusinessException("生成截图失败：" + json.getString("msg"));
            }
            return json.getString("data");
        } catch (ThirdException e) {
            throw new BusinessException(StringUtils.defaultIfBlank(e.getMessage(), e.getCause().getMessage()));
        }
    }

    /**
     * 判断是否是授权交易
     *
     * @param str
     * @return
     */
    public static boolean isAuthTransaction(String str) {
        String regex = "^(FACEBK|METAPAY)\\s*\\*[A-Z0-9]{4}\\s*\\S*";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    /**
     * 判断是否是短链广告户
     *
     * @param str
     * @return
     */
    public static boolean isShortAdAccount(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        return str.length() <= 9;
    }

    public static List<String> ignoreCardBalanceAdjustAdAccounts() {
        List<String> adAccounts = new ArrayList<>();
        return adAccounts;
    }
}
