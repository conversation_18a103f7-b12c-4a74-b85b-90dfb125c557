package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema; // Springdoc/OpenAPI 3 注解
import lombok.Data;

@Data
@Schema(description = "客户封禁统计响应对象") // OpenAPI 文档描述
public class CustomerBanStatsResp {

    private Long customerId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "总下户数")
    private Integer totalCount;

    @Schema(description = "总封禁数")
    private Integer totalBanCount;


    @Schema(description = "未使用被封禁数")
    private Integer unusedBanCount;

    @Schema(description = "未消耗封禁数")
    private Integer unconsumedBanCount;


    @Schema(description = "已消耗封禁数")
    private Integer consumedBanCount;


}
