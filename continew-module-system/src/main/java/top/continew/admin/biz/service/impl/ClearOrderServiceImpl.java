/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.AdAccountClearStatusEnum;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.ClearOrderStatusEnum;
import top.continew.admin.biz.enums.FbLimitCheckStatusEnum;
import top.continew.admin.biz.event.*;
import top.continew.admin.biz.mapper.ClearOrderMapper;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.entity.ClearOrderDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.ClearAccountAnalyzeQuery;
import top.continew.admin.biz.model.query.ClearOrderQuery;
import top.continew.admin.biz.model.req.ClearOrderFinishReq;
import top.continew.admin.biz.model.req.ClearOrderReq;
import top.continew.admin.biz.model.resp.ClearAccountAnalyzeResp;
import top.continew.admin.biz.model.resp.ClearOrderDetailResp;
import top.continew.admin.biz.model.resp.ClearOrderResp;
import top.continew.admin.biz.robot.req.RobotClearReq;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.ClearOrderService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.admin.system.service.FileService;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

/**
 * 清零订单业务实现
 *
 * <AUTHOR>
 * @since 2024/12/31 11:17
 */
@Service
@RequiredArgsConstructor
public class ClearOrderServiceImpl extends BaseServiceImpl<ClearOrderMapper, ClearOrderDO, ClearOrderResp, ClearOrderDetailResp, ClearOrderQuery, ClearOrderReq> implements ClearOrderService {

    private final AdAccountService adAccountService;

    private final FileService fileService;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private final AdAccountOrderService adAccountOrderService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    @Override
    protected void beforeAdd(ClearOrderReq req) {
        AdAccountDO adAccount = adAccountService.getOne(Wrappers.<AdAccountDO>lambdaQuery()
                .eq(AdAccountDO::getPlatformAdId, req.getPlatformAdId()));
        CheckUtils.throwIfNull(adAccount, "未找到相关广告户");
        CheckUtils.throwIf(!adAccount.getClearStatus().equals(AdAccountClearStatusEnum.WAIT), "请勿重复清零");
        boolean exist = adAccountOrderService.exists(Wrappers.<AdAccountOrderDO>lambdaQuery()
                .eq(AdAccountOrderDO::getCustomerId, req.getCustomerId())
                .eq(AdAccountOrderDO::getAdAccountId, req.getPlatformAdId())
                .in(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.RECYCLE));
        CheckUtils.throwIf(!exist, "下户记录不存在");
    }

    @Override
    public Long add(ClearOrderReq req) {
        this.beforeAdd(req);
        ClearOrderDO order = new ClearOrderDO();
        order.setOrderNo(CommonUtils.randomOrderNo("QL"));
        order.setCustomerId(req.getCustomerId());
        order.setPlatformAdId(req.getPlatformAdId());
        order.setStatus(ClearOrderStatusEnum.WAIT);
        this.save(order);
        this.afterAdd(req, order);
        return order.getId();
    }

    @Override
    protected void afterAdd(ClearOrderReq req, ClearOrderDO entity) {
        // 更新广告户清零状态
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                .set(AdAccountDO::getClearStatus, AdAccountClearStatusEnum.PROCESS)
                .eq(AdAccountDO::getPlatformAdId, req.getPlatformAdId()));

    }

    @Override
    public PageResp<ClearOrderResp> page(ClearOrderQuery query, PageQuery pageQuery) {
        QueryWrapper<ClearOrderDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, pageQuery);
        IPage<ClearOrderResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        return PageResp.build(page);
    }

    @Override
    protected <E> List<E> list(ClearOrderQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<ClearOrderDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, sortQuery);
        return BeanUtil.copyToList(baseMapper.selectCustomList(queryWrapper), targetClass);
    }

    @Override
    public synchronized void handleOrder(Long id) {
        ClearOrderDO order = this.getById(id);
        CheckUtils.throwIf(!order.getStatus().equals(ClearOrderStatusEnum.WAIT), "订单已被其他人处理");
        this.update(Wrappers.<ClearOrderDO>lambdaUpdate()
                .set(ClearOrderDO::getHandleUser, UserContextHolder.getUserId())
                .set(ClearOrderDO::getStatus, ClearOrderStatusEnum.HANDLING)
                .set(ClearOrderDO::getHandleTime, LocalDateTime.now())
                .eq(ClearOrderDO::getId, id));
        SpringUtil.publishEvent(new ClearOrderHandleEvent(order.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void finishOrder(ClearOrderFinishReq req) {
        ClearOrderDO order = this.getById(req.getId());
        CheckUtils.throwIf(!order.getHandleUser().equals(UserContextHolder.getUserId()), "仅限处理人进行操作");
        CheckUtils.throwIf(!order.getStatus().equals(ClearOrderStatusEnum.HANDLING), "订单未在处理中");
        this.update(Wrappers.<ClearOrderDO>lambdaUpdate()
                .set(ClearOrderDO::getClearAmount, req.getClearAmount())
                .set(ClearOrderDO::getCardBalance, req.getCardBalance())
                .set(StringUtils.isNotBlank(req.getCertificate()), ClearOrderDO::getCertificate, req.getCertificate())
                .set(StringUtils.isNotBlank(req.getRemark()), ClearOrderDO::getRemark, req.getRemark())
                .set(ClearOrderDO::getFinishTime, LocalDateTime.now())
                .set(ClearOrderDO::getStatus, ClearOrderStatusEnum.FINISH)
                .eq(ClearOrderDO::getId, req.getId()));
        SpringUtil.publishEvent(new ClearOrderFinishEvent(order.getId()));
    }

    @Override
    public synchronized void cancelOrder(Long id) {
        ClearOrderDO order = this.getById(id);
        CheckUtils.throwIf(order.getHandleUser() != null && !order.getHandleUser()
                .equals(UserContextHolder.getUserId()), "仅限处理人进行操作");
        CheckUtils.throwIf(order.getStatus().equals(ClearOrderStatusEnum.FINISH) || order.getStatus()
                .equals(ClearOrderStatusEnum.CANCEL), "订单状态错误，无法取消");
        SpringUtil.publishEvent(new ClearOrderCancelEvent(order.getId()));
    }

    @Override
    public void generateCertificate(Long id) {
        ClearOrderDO order = this.getById(id);
        CheckUtils.throwIf(!order.getStatus().equals(ClearOrderStatusEnum.HANDLING), "订单未在处理中");
        AdAccountDO adAccount = adAccountService.getByPlatformAdId(order.getPlatformAdId());
        CheckUtils.throwIfBlank(adAccount.getParentBrowserNo(), "当前广告户未绑定观察户");
        this.update(Wrappers.<ClearOrderDO>lambdaUpdate()
                .set(ClearOrderDO::getCertificate, "")
                .eq(ClearOrderDO::getId, id));
        threadPoolTaskExecutor.execute(() -> {
            JSONObject accountDetail = FacebookUtils.getAdAccountDetail(adAccount.getParentBrowserNo(), order.getPlatformAdId());
            BigDecimal limit = CommonUtils.divide100(accountDetail.getBigDecimal("spend_cap"), BigDecimal.ZERO);
            BigDecimal spend = CommonUtils.divide100(accountDetail.getBigDecimal("amount_spent"), BigDecimal.ZERO);
            String base64Str = FacebookUtils.generateBillingScreen(limit, spend);
            byte[] imageBytes = Base64.getDecoder().decode(base64Str);
            FileInfo fileInfo = fileService.uploadImage(imageBytes, "");
            this.update(Wrappers.<ClearOrderDO>lambdaUpdate()
                    .set(ClearOrderDO::getClearAmount, limit.subtract(spend))
                    .set(ClearOrderDO::getCertificate, fileInfo.getUrl())
                    .eq(ClearOrderDO::getId, id));
        });
    }

    @Override
    public void checkModifyLimit(String platformAdId, String fbOpsId) {
        ClearOrderDO order = this.getOne(Wrappers.<ClearOrderDO>lambdaQuery()
                .eq(ClearOrderDO::getPlatformAdId, platformAdId)
                .eq(ClearOrderDO::getStatus, ClearOrderStatusEnum.HANDLING));
        CheckUtils.throwIfNull(order, "当前广告户不存在进行中的清零订单");
        CheckUtils.throwIf(order.getFbCheckStatus()
                .equals(FbLimitCheckStatusEnum.SUCCESS), "当前广告户不存在未检测的清零订单");
        this.update(Wrappers.<ClearOrderDO>lambdaUpdate()
                .set(ClearOrderDO::getFbCheckStatus, FbLimitCheckStatusEnum.SUCCESS)
                .set(ClearOrderDO::getFbOpsId, fbOpsId)
                .eq(ClearOrderDO::getId, order.getId()));
    }

    @Override
    public void uploadModifyLimitCertificate(String platformAdId, String fbOpsId, String imageBase64) {
        ClearOrderDO order = this.getOne(Wrappers.<ClearOrderDO>lambdaQuery()
                .eq(ClearOrderDO::getPlatformAdId, platformAdId)
                .eq(ClearOrderDO::getFbOpsId, fbOpsId)
                .eq(ClearOrderDO::getStatus, ClearOrderStatusEnum.HANDLING));
        CheckUtils.throwIfNull(order, "清零订单不存在");
        String certificate = "";
        if (StringUtils.isNotBlank(imageBase64)) {
            byte[] imageBytes = Base64.getDecoder().decode(imageBase64);
            FileInfo fileInfo = fileService.uploadImage(imageBytes, "");
            certificate = fileInfo.getUrl();
        }
        this.update(Wrappers.<ClearOrderDO>lambdaUpdate()
                .set(ClearOrderDO::getFbCheckStatus, FbLimitCheckStatusEnum.FINISH)
                .set(StringUtils.isNotBlank(certificate), ClearOrderDO::getCertificate, certificate)
                .eq(ClearOrderDO::getId, order.getId()));
    }

    @Override
    public PageResp<ClearAccountAnalyzeResp> getClearAccountAnalyze(ClearAccountAnalyzeQuery query) {
        IPage<ClearAccountAnalyzeResp> page = baseMapper.selectClearAccountAnalyze(new Page<>(query.getPage(), query.getSize()), query);
        return PageResp.build(page);
    }

    @Override
    public void clearApply(ClearOrderReq req) {
        CustomerService customerService = SpringUtil.getBean(CustomerService.class);
        CustomerDO customer = customerService.getById(req.getCustomerId());
        CheckUtils.throwIfNull(customer, "客户不存在");
        AdAccountDO adAccount = adAccountService.getOne(Wrappers.<AdAccountDO>lambdaQuery()
                .eq(AdAccountDO::getPlatformAdId, req.getPlatformAdId()));
        CheckUtils.throwIfNull(adAccount, "未找到相关广告户");
        AdAccountOrderDO adAccountOrder = adAccountOrderService.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
                .eq(AdAccountOrderDO::getCustomerId, customer.getId())
                .eq(AdAccountOrderDO::getAdAccountId, req.getPlatformAdId())
                .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
        CheckUtils.throwIfNull(adAccountOrder, "下户记录不存在");
        CheckUtils.throwIf(!adAccount.getClearStatus().equals(AdAccountClearStatusEnum.WAIT), "请勿重复清零");
        ClearOrderDO order = new ClearOrderDO();
        order.setOrderNo(CommonUtils.randomOrderNo("QL"));
        order.setCustomerId(customer.getId());
        order.setPlatformAdId(req.getPlatformAdId());
        order.setStatus(ClearOrderStatusEnum.WAIT);
        order.setAdAccountOrderId(adAccountOrder.getId());
        save(order);
        // 更新广告户清零状态
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                .set(AdAccountDO::getClearStatus, AdAccountClearStatusEnum.PROCESS)
                .eq(AdAccountDO::getId, adAccount.getId()));
        // 更新广告户清零状态
        adAccountOrderService.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
                .set(AdAccountOrderDO::getClearStatus, AdAccountClearStatusEnum.PROCESS)
                .eq(AdAccountOrderDO::getId, adAccountOrder.getId()));
        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getBusinessChatId())
                .text(String.format("""
                         【待确认的清零操作】
                         客户：%s
                         广告户ID：%s
                        """, customer.getName(), req.getPlatformAdId()
                ))
                .build()));
    }
}