package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.constant.ContainerPool;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 客户商务对接实体
 *
 * <AUTHOR>
 * @since 2025/08/05 15:35
 */
@Data
@TableName("biz_customer_business_user")
public class CustomerBusinessUserDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID，用于关联客户信息表
     */
    private Long customerId;

    /**
     * 关联的商务用户ID
     */
    private Long userId;


    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}