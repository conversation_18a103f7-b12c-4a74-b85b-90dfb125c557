/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.admin.biz.excel.converter.BooleanConverter;
import top.continew.admin.common.base.BaseResp;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * BM5账号详情信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:48
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "BM5账号详情信息")
public class BusinessManagerDetailResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联渠道
     */
    @Schema(description = "关联渠道")
    private Long channelId;

    @ExcelProperty(value = "关联渠道")
    private String channelName;

    /**
     * BM5 ID
     */
    @Schema(description = "BM ID")
    @ExcelProperty(value = "BM ID")
    private String platformId;

    private Long type;

    @ExcelProperty(value = "类型")
    private String typeName;

    @ExcelProperty(value = "账号信息")
    private String content;

    @ExcelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ExcelProperty(value = "状态", converter = ExcelBaseEnumConverter.class)
    private BusinessManagerStatusEnum status;

    @ExcelProperty(value = "封禁原因", converter = ExcelBaseEnumConverter.class)
    private BusinessManagerBannedReasonEnum bannedReason;

    @ExcelProperty(value = "售后状态", converter = ExcelBaseEnumConverter.class)
    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    @ExcelProperty(value = "售后原因")
    private String afterSaleReason;

    @ExcelProperty(value = "是否使用",converter = BooleanConverter.class)
    private Boolean isUse;

    @ExcelProperty(value = "使用者")
    private String user;

    @ExcelProperty(value = "使用时间")
    private LocalDateTime useTime;

    @ExcelProperty(value = "总坑位")
    private Integer num;

    @ExcelProperty(value = "已使用坑位")
    private Integer useItemNum;

    @ExcelProperty(value = "已拉户数")
    private Integer useNum;

    @ExcelProperty(value = "管理大黑号数")
    private Integer useBlackNum;

    @ExcelProperty(value = "封禁时间")
    private LocalDateTime banTime;

    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "操作号")
    private String opsBrowser;
    @ExcelProperty(value = "备用号")
    private String reserveBrowser;
    @ExcelProperty(value = "观察号")
    private String observeBrowser;
    @ExcelProperty(value = "外部渠道号",converter = BooleanConverter.class)
    private Boolean isExternal;

    private Boolean isEnterpriseAuth;

    private Boolean isRemoveAdmin;

    private String reserveBrowserBak;
    @ExcelProperty(value = "是否补号", converter = BooleanConverter.class)
    private Boolean isBu;
}