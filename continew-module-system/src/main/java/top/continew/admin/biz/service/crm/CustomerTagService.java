package top.continew.admin.biz.service.crm;

import java.util.List;
import java.util.Map;

import top.continew.admin.biz.model.entity.crm.CustomerTagDO;
import top.continew.admin.biz.model.req.crm.CustomerTagAddReq;
import top.continew.admin.biz.model.resp.crm.CustomerTagResp;

/**
 * 客户标签业务接口
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
public interface CustomerTagService {
    
    /**
     * 添加客户标签
     *
     * @param req 客户标签请求
     */
    void addCustomerTag(CustomerTagAddReq req);
    
    /**
     * 删除客户标签
     *
     * @param customerId 客户ID
     * @param tagId 标签ID
     */
    void deleteCustomerTag(Long customerId, Long tagId);
    
    /**
     * 批量删除客户标签
     *
     * @param customerId 客户ID
     * @param tagIds 标签ID列表
     */
    void batchDeleteCustomerTag(Long customerId, List<Long> tagIds);
    
    /**
     * 获取客户标签列表
     *
     * @param customerId 客户ID
     * @return 客户标签列表
     */
    List<CustomerTagResp> getCustomerTagList(Long customerId);
    
    /**
     * 批量获取客户标签列表
     *
     * @param customerIds 客户ID列表
     * @return 客户ID与标签列表的映射
     */
    Map<Long, List<CustomerTagResp>> batchGetCustomerTagList(List<Long> customerIds);
}