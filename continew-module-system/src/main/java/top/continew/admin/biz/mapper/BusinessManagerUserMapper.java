package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.BusinessManagerDO;
import top.continew.admin.biz.model.resp.BusinessManagerResp;
import top.continew.admin.biz.model.resp.BusinessManagerUserResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.BusinessManagerUserDO;

/**
* bm管理员 Mapper
*
* <AUTHOR>
* @since 2025/04/22 16:45
*/
public interface BusinessManagerUserMapper extends BaseMapper<BusinessManagerUserDO> {

    IPage<BusinessManagerUserResp> selectCustomPage(IPage<BusinessManagerUserDO> page,
                                                    @Param(Constants.WRAPPER) QueryWrapper<BusinessManagerUserDO> queryWrapper);
}