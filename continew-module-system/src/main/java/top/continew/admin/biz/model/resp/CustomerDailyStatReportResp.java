/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ExcelIgnoreUnannotated
public class CustomerDailyStatReportResp {

    /**
     * 统计日期
     */
    @ExcelProperty("统计日期")
    private String statDate;

    /**
     * 打款金额
     */
    @ExcelProperty("打款金额")
    private BigDecimal transferAmount;

    @ExcelProperty("退款金额")
    private BigDecimal refundAmount;

    /**
     * 充值手续费
     */
    @ExcelProperty("服务费")
    private BigDecimal fee;

    /**
     * 广告户充值金额
     */
    @ExcelProperty("广告户充值金额")
    private BigDecimal adAccountRechargeAmount;

    @ExcelProperty("广告户开户费")
    private BigDecimal adAccountBuyAmount;

    /**
     * 取款金额
     */
    @ExcelProperty("取款金额")
    private BigDecimal withdrawAmount;

    @ExcelProperty("卡台消耗")
    private BigDecimal cardSpent;
}
