package top.continew.admin.biz.service.crm;

import top.continew.admin.biz.model.query.crm.SourceQuery;
import top.continew.admin.biz.model.req.crm.SourceGroupReq;
import top.continew.admin.biz.model.req.crm.SourceReq;
import top.continew.admin.biz.model.resp.crm.SourceDetailResp;
import top.continew.admin.biz.model.resp.crm.SourceGroupResp;
import top.continew.admin.biz.model.resp.crm.SourceResp;
import top.continew.starter.extension.crud.service.BaseService;
import java.util.List;

public interface SourceService extends BaseService<SourceResp, SourceDetailResp, SourceQuery, SourceReq> {
    
    List<SourceGroupResp> listGroup();
    
    void addGroup(SourceGroupReq req);
    
    void deleteGroup(Long id);
    
    void updateGroup(Long id, SourceGroupReq req);
}