/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.IoUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.IpMapper;
import top.continew.admin.biz.model.entity.IpDO;
import top.continew.admin.biz.model.query.IpQuery;
import top.continew.admin.biz.model.req.IpReq;
import top.continew.admin.biz.model.resp.IpDetailResp;
import top.continew.admin.biz.model.resp.IpResp;
import top.continew.admin.biz.service.IpService;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * IP库业务实现
 *
 * <AUTHOR>
 * @since 2025/01/20 13:46
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IpServiceImpl extends BaseServiceImpl<IpMapper, IpDO, IpResp, IpDetailResp, IpQuery, IpReq> implements IpService {

    @Override
    public void addUse(Long id) {
        this.update(Wrappers.<IpDO>lambdaUpdate().setSql("use_times = use_times + 1").eq(IpDO::getId, id));
    }

    @Override
    protected void beforeAdd(IpReq req) {
        if (req.getExpireDay() != null) {
            req.setExpireTime(LocalDateTimeUtil.offset(LocalDateTime.now(), req.getExpireDay(), ChronoUnit.DAYS));
        }
    }

    @Override
    public void addSuccess(Long id) {
        this.update(Wrappers.<IpDO>lambdaUpdate().setSql("success_times = success_times + 1").eq(IpDO::getId, id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<IpDO> getAvailableIp(String countryCode) {
        return list(new LambdaQueryWrapper<IpDO>().lt(IpDO::getUseTimes, 10)
                .lt(IpDO::getSuccessTimes, 3)
                .eq(IpDO::getCountry, countryCode));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importIp(MultipartFile file, String country, Integer expireDay) {
        CheckUtils.throwIf(StringUtils.isBlank(country), "国家不能为空");
        List<String> txtData = new ArrayList<>();
        try {
            IoUtil.readUtf8Lines(file.getInputStream(), txtData);
        } catch (IOException e) {
            log.error("ip文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("ip文件解析异常");
        }
        LocalDateTime expireTime = null;
        if (expireDay != null) {
            expireTime = LocalDateTimeUtil.offset(LocalDateTime.now(), expireDay, ChronoUnit.DAYS);
        }
        List<IpDO> saveList = new ArrayList<>();
        for (String txtDatum : txtData) {
            if (StringUtils.isBlank(txtDatum)) {
                continue;
            }
            String[] split = txtDatum.split(":");
            IpDO ipDO = new IpDO();
            ipDO.setCountry(country);
            ipDO.setHost(split[0]);
            ipDO.setPort(split[1]);
            ipDO.setUsername(split[2]);
            ipDO.setPassword(split[3]);
            ipDO.setExpireTime(expireTime);
            saveList.add(ipDO);
        }
        saveBatch(saveList);
    }

    @Override
    public void addUseCount(Long id, Integer count) {
        this.update(Wrappers.<IpDO>lambdaUpdate().setSql("use_times = use_times + " + count).eq(IpDO::getId, id));
    }
}