/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.ClearOrderService;
import top.continew.admin.biz.service.RechargeOrderService;
import top.continew.admin.biz.service.RefundOrderService;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.core.exception.BusinessException;

import java.math.BigDecimal;

@Service
@RequiredArgsConstructor
public class ScreenshotAfterUpdateLimitStrategyImpl implements ReportOpsStrategy {

    private final AdAccountService adAccountService;

    private final RechargeOrderService rechargeOrderService;

    private final RefundOrderService refundOrderService;

    private final ClearOrderService clearOrderService;

    @Override
    public ReportType getReport() {
        return ReportType.SCREENSHOT_AFTER_UPDATE_LIMIT;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
//        JSONObject data = JSONObject.from(browserReq.getData());
//        JSONObject input = data.getJSONObject("body").getJSONObject("input");
//        String platformAdId = input.getString("billable_account_payment_legacy_account_id");
//        JSONObject loggingData = input.getJSONObject("logging_data");
//        String loggingId = loggingData.getString("logging_id");
//        JSONObject newSpendLimit = input.getJSONObject("new_spend_limit");
//        BigDecimal newSpendLimitAmount = newSpendLimit.getBigDecimal("amount");
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("platformAdId", platformAdId);
//        jsonObject.put("loggingId", loggingId);
//        jsonObject.put("newSpendLimitAmount", newSpendLimitAmount);
//        JSONObject res = data.getJSONObject("res");
//        JSONObject resData = res.getJSONObject("data");
//        if (resData == null) {
//            return "";
//        }
//        JSONObject billableAccountEditSpendLimit = resData.getJSONObject("billable_account_edit_spend_limit");
//        if (billableAccountEditSpendLimit == null) {
//            return "";
//        }
//        JSONObject billableAccount = billableAccountEditSpendLimit.getJSONObject("billable_account");
//        if (billableAccount == null) {
//            return "";
//        }
//        JSONObject spendingInfo = billableAccount.getJSONObject("spending_info");
//        if (spendingInfo == null) {
//            return "";
//        }
//        AdAccountDO adAccount = adAccountService.getByPlatformAdId(platformAdId);
//        if (adAccount == null) {
//            return "";
//        }
//        try {
//            if (newSpendLimitAmount.compareTo(new BigDecimal("0.01")) == 0) {
//                String base64 = data.getString("preScreenshot");
//                clearOrderService.uploadModifyLimitCertificate(platformAdId, loggingId, base64);
//            } else {
//                // 养号中
//                if (newSpendLimitAmount.compareTo(new BigDecimal(20)) <= 0) {
//                    return jsonObject.toString();
//                }
//                String base64 = data.getString("screenshot");
//                try {
//                    rechargeOrderService.uploadModifyLimitCertificate(platformAdId, loggingId, base64);
//                } catch (BusinessException e) {
//                    refundOrderService.uploadModifyLimitCertificate(platformAdId, loggingId, base64);
//                }
//            }
//        } catch (Exception ignore) {
//
//        }
//        return jsonObject.toString();
        return "";
    }
}
