/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.RefundOrderStatusEnum;
import top.continew.admin.biz.event.RefundOrderCreateEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.RefundOrderDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotRefundReq;
import top.continew.admin.biz.robot.strategy.BaseRobotCommandService;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.RefundOrderService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.core.validation.CheckUtils;

import java.math.BigDecimal;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class RobotRefundStrategyImpl extends BaseRobotCommandService implements RobotCommandStrategy {

    private final RefundOrderService refundOrderService;

    private final AdAccountService adAccountService;

    private final AdAccountOrderService adAccountOrderService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.REFUND;
    }

    @Override
    public String execute(Update update) {
        if (!this.hasPermission(this.getCommand(), update.getMessage().getChatId())) {
            return null;
        }
        RobotRefundReq req = BotUtils.parseBotMsgToObject(update.getMessage()
            .getText(), "\n", ":", RobotRefundReq.class);
        log.info("{}收到一条减款请求：{}", getCommand().getDescription(), JSONObject.toJSONString(req));
        CheckUtils.throwIfNull(req.getAmount().compareTo(BigDecimal.ZERO) <= 0, "减款金额必须大于0");
        CustomerDO customer = getCustomer(req.getCustomerName(), update.getMessage().getChatId());
        AdAccountDO adAccount = adAccountService.getOne(Wrappers.<AdAccountDO>lambdaQuery()
            .eq(AdAccountDO::getPlatformAdId, req.getPlatformAdId()));
        CheckUtils.throwIfNull(adAccount, "未找到相关广告户");
        adAccountOrderService.checkExistOrder(customer.getId(), req.getPlatformAdId());
        // 一个广告户只允许一条进行中的退款订单
        RefundOrderDO existOrder = refundOrderService.getOne(Wrappers.<RefundOrderDO>lambdaQuery()
            .eq(RefundOrderDO::getPlatformAdId, req.getPlatformAdId())
            .in(RefundOrderDO::getStatus, List.of(RefundOrderStatusEnum.WAIT, RefundOrderStatusEnum.HANDLING)));
        CheckUtils.throwIfNotNull(existOrder, "请先处理之前的减款订单");

        RefundOrderDO order = new RefundOrderDO();
        order.setOrderNo(CommonUtils.randomOrderNo("JK"));
        order.setCustomerId(customer.getId());
        order.setPlatformAdId(req.getPlatformAdId());
        order.setAmount(req.getAmount());
        order.setStatus(RefundOrderStatusEnum.WAIT);
        order.setApplyMessageId(update.getMessage().getMessageId());
        refundOrderService.save(order);
        log.info("{}订单入库成功", getCommand().getDescription());
        SpringUtil.publishEvent(new RefundOrderCreateEvent(order.getId()));
        return "订单%s提交成功，等待客服人员处理".formatted(order.getOrderNo());
    }
}
