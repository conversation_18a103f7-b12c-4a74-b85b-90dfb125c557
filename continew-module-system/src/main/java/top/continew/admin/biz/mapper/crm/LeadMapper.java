package top.continew.admin.biz.mapper.crm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.crm.LeadDO;
import top.continew.admin.biz.model.query.crm.LeadQuery;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.starter.extension.datapermission.annotation.DataPermission;

import java.time.LocalDateTime;

/**
* 线索 Mapper
*
* <AUTHOR>
* @since 2025/05/16 17:48
*/
public interface LeadMapper extends BaseMapper<LeadDO> {

    /**
     * 分页查询线索列表
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页数据
     */
    @DataPermission(userId = "l.handler_user_id", deptId = "ur.dept_id")
    IPage<LeadDO> selectLeadPage(IPage<LeadDO> page, @Param("query") LeadQuery query);

    /**
     * 统计用户待跟进线索数量
     *
     * @param handlerUserId 对接人ID
     * @return 待跟进数量
     */
    Long countPendingLeads(@Param("handlerUserId") Long handlerUserId);

    /**
     * 统计用户今日需跟进线索数量
     *
     * @param handlerUserId 对接人ID
     * @param todayStart 今日开始时间
     * @param todayEnd 今日结束时间
     * @param waitOverHour 待跟进超时时长(小时)
     * @param pendingOverHour 跟进中超时时长(小时)
     * @return 今日需跟进数量
     */
    Long countTodayFollowLeads(@Param("handlerUserId") Long handlerUserId,
                               @Param("todayStart") LocalDateTime todayStart,
                               @Param("todayEnd") LocalDateTime todayEnd,
                               @Param("waitOverHour") Integer waitOverHour,
                               @Param("pendingOverHour") Integer pendingOverHour);

    /**
     * 统计用户超时线索数量
     *
     * @param handlerUserId 对接人ID
     * @param currentTime 当前时间
     * @param waitOverHour 待跟进超时时长(小时)
     * @param pendingOverHour 跟进中超时时长(小时)
     * @return 超时数量
     */
    Long countOverdueLeads(@Param("handlerUserId") Long handlerUserId,
                           @Param("currentTime") LocalDateTime currentTime,
                           @Param("waitOverHour") Integer waitOverHour,
                           @Param("pendingOverHour") Integer pendingOverHour);

    /**
     * 更新为长期跟进
     */
    void updateLongTermFollowUp(@Param("remindTime") LocalDateTime remindTime);
}