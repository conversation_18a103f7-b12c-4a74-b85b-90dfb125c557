package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 客户回访任务明细实体
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
@TableName("biz_customer_visit_task_detail")
public class CustomerVisitTaskDetailDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 回访任务ID
     */
    private Long taskId;

    /**
     * 回访方式：电话、微信、邮件、上门拜访、会议等
     */
    private String visitMethod;

    /**
     * 回访时间
     */
    private LocalDateTime visitTime;

    /**
     * 回访纪要
     */
    private String visitSummary;

    /**
     * 附件URL，多个用逗号分隔
     */
    private String attachmentUrls;
}