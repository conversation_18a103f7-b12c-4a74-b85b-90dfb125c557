/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.*;
import top.continew.starter.extension.crud.annotation.DictField;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 广告账号实体
 *
 * <AUTHOR>
 * @since 2024/12/30 17:50
 */
@Data
@DictField(labelKey = "platformAdId")
@TableName("biz_ad_account")
public class AdAccountDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联BM5 ID
     */
    private Long businessManagerId;

    /**
     * 平台用户ID
     */
    private String platformAccountId;

    /**
     * 平台广告ID
     */
    private String platformAdId;

    /**
     * 完整卡号
     */
    private String fullCardNumber;

    /**
     * 模糊卡号
     */
    private String fuzzyCardNumber;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 账号状态
     */
    private AdAccountStatusEnum accountStatus;

    /**
     * 养号状态
     */
    private AdAccountKeepStatusEnum keepStatus;

    /**
     * 出售状态
     */
    private AdAccountSaleStatusEnum saleStatus;

    /**
     * 个号地区
     */
    private String personalArea;

    /**
     * 浏览器编号
     */
    private String browserNo;

    /**
     * 清零状态
     */
    private AdAccountClearStatusEnum clearStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 花费限额
     */
    private BigDecimal spendCap;

    /**
     * 消耗
     */
    private BigDecimal amountSpent;

    /**
     * 剩余应付
     */
    private BigDecimal balance;

    /**
     * 观察户ID
     */
    private String parentBrowserNo;

    private String bmId;

    private String tag;

    private String name;

    private AdAccountAppealStatusEnum appealStatus;

    /**
     * 总花费
     */
    private BigDecimal totalSpent;

    private String billCountry;

    private String billCurrency;

    private LocalDateTime bmAuthTime;

    private LocalDateTime banTime;

    private LocalDateTime saleTime;

    private BigDecimal cost;

    private String bm1Browser;

    private Long bm1Id;

    private Boolean isRemoveAdmin;

    private Boolean isLowLimit;

    private BigDecimal realAdtrustDsl;

    private Long bmItemType;

    private Long bmItemChannelId;

    private Integer voStatus;

    private String headers;

    private BigDecimal prepayAccountBalance;

    private String browserId;

    private BigDecimal billingThresholdCurrencyAmount;

    private Boolean isPrepay;

    /**
     * 是否可用
     */
    private Boolean usable;

    /**
     * 不可用原因
     */
    private AdAccountUnusableReasonEnum unusableReason;

    private String disabledReason;


    public static class builder {
        /**
         * 关联BM5 ID
         */
        private Long businessManagerId;

        /**
         * 平台用户ID
         */
        private String platformAccountId;

        /**
         * 平台广告ID
         */
        private String platformAdId;

        /**
         * 完整卡号
         */
        private String fullCardNumber;

        /**
         * 模糊卡号
         */
        private String fuzzyCardNumber;

        /**
         * 时区
         */
        private String timezone;

        /**
         * 账号状态
         */
        private AdAccountStatusEnum accountStatus;

        /**
         * 养号状态
         */
        private AdAccountKeepStatusEnum keepStatus;

        /**
         * 出售状态
         */
        private AdAccountSaleStatusEnum saleStatus;

        /**
         * 个号地区
         */
        private String personalArea;

        /**
         * 浏览器编号
         */
        private String browserNo;

        /**
         * 清零状态
         */
        private AdAccountClearStatusEnum clearStatus;

        /**
         * 备注
         */
        private String remark;

        /**
         * 花费限额
         */
        private BigDecimal spendCap;

        /**
         * 消耗
         */
        private BigDecimal amountSpent;

        private Long createUser;

        /**
         * 剩余应付
         */
        private BigDecimal balance;

        private String parentBrowserNo;

        private String bmId;

        private String tag;

        private String billCountry;

        private String billCurrency;

        private LocalDateTime bmAuthTime;

        private LocalDateTime banTime;

        private String name;

        private Long bmItemType;

        private Long bmItemChannelId;

        private String headers;

        private String browserId;

        private BigDecimal billingThresholdCurrencyAmount;

        private BigDecimal prepayAccountBalance;

        public builder businessManagerId(Long businessManagerId) {
            this.businessManagerId = businessManagerId;
            return this;
        }

        public builder bmItemType(Long bmItemType) {
            this.bmItemType = bmItemType;
            return this;
        }

        public builder bmItemChannelId(Long bmItemChannelId) {
            this.bmItemChannelId = bmItemChannelId;
            return this;
        }

        public builder platformAccountId(String platformAccountId) {
            this.platformAccountId = platformAccountId;
            return this;
        }

        public builder platformAdId(String platformAdId) {
            this.platformAdId = platformAdId;
            return this;
        }

        public builder fullCardNumber(String fullCardNumber) {
            this.fullCardNumber = fullCardNumber;
            return this;
        }

        public builder fuzzyCardNumber(String fuzzyCardNumber) {
            this.fuzzyCardNumber = fuzzyCardNumber;
            return this;
        }

        public builder timezone(String timezone) {
            this.timezone = timezone;
            return this;
        }

        public builder accountStatus(AdAccountStatusEnum accountStatus) {
            this.accountStatus = accountStatus;
            return this;
        }

        public builder keepStatus(AdAccountKeepStatusEnum keepStatus) {
            this.keepStatus = keepStatus;
            return this;
        }

        public builder saleStatus(AdAccountSaleStatusEnum saleStatus) {
            this.saleStatus = saleStatus;
            return this;
        }

        public builder personalArea(String personalArea) {
            this.personalArea = personalArea;
            return this;
        }

        public builder browserNo(String browserNo) {
            this.browserNo = browserNo;
            return this;
        }

        public builder clearStatus(AdAccountClearStatusEnum clearStatus) {
            this.clearStatus = clearStatus;
            return this;
        }

        public builder remark(String remark) {
            this.remark = remark;
            return this;
        }

        public builder spendCap(BigDecimal spendCap) {
            this.spendCap = spendCap;
            return this;
        }

        public builder amountSpent(BigDecimal amountSpent) {
            this.amountSpent = amountSpent;
            return this;
        }

        public builder balance(BigDecimal balance) {
            this.balance = balance;
            return this;
        }

        public builder parentBrowserNo(String parentBrowserNo) {
            this.parentBrowserNo = parentBrowserNo;
            return this;
        }

        public builder createUser(Long createUser) {
            this.createUser = createUser;
            return this;
        }

        public builder bmId(String bmId) {
            this.bmId = bmId;
            return this;
        }

        public builder tag(String tag) {
            this.tag = tag;
            return this;
        }
        public builder billCountry(String billCountry) {
            this.billCountry = billCountry;
            return this;
        }

        public builder billCurrency(String billCurrency) {
            this.billCurrency = billCurrency;
            return this;
        }

        public builder bmAuthTime(LocalDateTime bmAuthTime) {
            this.bmAuthTime = bmAuthTime;
            return this;
        }

        public builder banTime(LocalDateTime banTime) {
            this.banTime = banTime;
            return this;
        }

        public builder name(String name) {
            this.name = name;
            return this;
        }

        public builder headers(String headers) {
            this.headers = headers;
            return this;
        }

        public builder browserId(String browserId) {
            this.browserId = browserId;
            return this;
        }

        public builder billingThresholdCurrencyAmount(BigDecimal billingThresholdCurrencyAmount) {
            this.billingThresholdCurrencyAmount = billingThresholdCurrencyAmount;
            return this;
        }

        public builder prepayAccountBalance(BigDecimal prepayAccountBalance) {
            this.prepayAccountBalance = prepayAccountBalance;
            return this;
        }

        public AdAccountDO build() {
            AdAccountDO adAccountDO = new AdAccountDO();
            adAccountDO.setBusinessManagerId(businessManagerId);
            adAccountDO.setPlatformAccountId(platformAccountId);
            adAccountDO.setPlatformAdId(platformAdId);
            adAccountDO.setFullCardNumber(fullCardNumber);
            adAccountDO.setFuzzyCardNumber(fuzzyCardNumber);
            adAccountDO.setTimezone(timezone);
            adAccountDO.setAccountStatus(accountStatus);
            adAccountDO.setKeepStatus(keepStatus);
            adAccountDO.setSaleStatus(saleStatus);
            adAccountDO.setPersonalArea(personalArea);
            adAccountDO.setBrowserNo(browserNo);
            adAccountDO.setClearStatus(clearStatus);
            adAccountDO.setRemark(remark);
            adAccountDO.setSpendCap(spendCap);
            adAccountDO.setAmountSpent(amountSpent);
            adAccountDO.setBalance(balance);
            adAccountDO.setParentBrowserNo(parentBrowserNo);
            adAccountDO.setCreateUser(createUser);
            adAccountDO.setBmId(bmId);
            adAccountDO.setTag(tag);
            adAccountDO.setBillCountry(billCountry);
            adAccountDO.setBillCurrency(billCurrency);
            adAccountDO.setBmAuthTime(bmAuthTime);
            adAccountDO.setBanTime(banTime);
            adAccountDO.setName(name);
            adAccountDO.setBmItemType(bmItemType);
            adAccountDO.setBmItemChannelId(bmItemChannelId);
            adAccountDO.setHeaders(headers);
            adAccountDO.setBrowserId(browserId);
            adAccountDO.setBillingThresholdCurrencyAmount(billingThresholdCurrencyAmount);
            adAccountDO.setPrepayAccountBalance(prepayAccountBalance);
            return adAccountDO;
        }
    }
}