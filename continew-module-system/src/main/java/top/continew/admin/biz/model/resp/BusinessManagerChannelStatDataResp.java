package top.continew.admin.biz.model.resp;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Data
@Schema(description = "BM渠道统计数据")
public class BusinessManagerChannelStatDataResp {

    @Schema(description = "渠道ID")
    private Long channelId;
    @Schema(description = "渠道名称")
    private String channelName;
    
    @Schema(description = "总数")
    private Long totalCount;

    @Schema(description = "广告户-总数")
    private Long adAccountTotalCount;

    @Schema(description = "广告户-作废数")
    private Long adAccountInvalidCount;

    @Schema(description = "广告户-作废率")
    private String adAccountInvalidRatio;



    @Schema(description = "广告户-下户订单数")
    private Long adAccountOrderCount;

    @Schema(description = "广告户-不消耗订单数")
    private Long adAccountOrderNoSpendCount;

    @Schema(description = "广告户-不消耗率")
    private String adAccountOrderNoSpendRatio;


    @Schema(description = "剩余可用数")
    private Long  availableCount;

    @Schema(description = "封禁BM数")
    private Long bannedCount;

    @Schema(description = "掉额BM数")
    private Long lowerLimitCount;

    @Schema(description = "已使用封禁BM数")
    private Long usedBannedCount;

    @Schema(description = "掉额广告户数")
    private Long lowerLimitAdCount;



}