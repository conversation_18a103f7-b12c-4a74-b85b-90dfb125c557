package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 钱包流水信息
 *
 * <AUTHOR>
 * @since 2025/07/22 15:59
 */
@Data
@Schema(description = "钱包流水信息")
public class WalletTransferResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易号
     */
    @Schema(description = "交易号")
    private String transactionId;

    /**
     * 是否确认
     */
    @Schema(description = "是否确认")
    private Boolean confirmed;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    private LocalDateTime transTime;

    /**
     * contractRet
     */
    @Schema(description = "contractRet")
    private String contractRet;

    /**
     * 合约地址
     */
    @Schema(description = "合约地址")
    private String contractAddress;

    /**
     * 合约类型
     */
    @Schema(description = "合约类型")
    private String contractType;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型")
    private String eventType;

    /**
     * finalResult
     */
    @Schema(description = "finalResult")
    private String finalResult;

    /**
     * 来源地址
     */
    @Schema(description = "来源地址")
    private String fromAddress;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * to_address
     */
    @Schema(description = "to_address")
    private String toAddress;

    private String tokenSymbol;

    private String customerName;
}