package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


@Data
@Schema(description = "工作记录统计查询条件")
public class OperationsRecordStatQuery {
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[]  createTime;

    private Long createUser;
}
