package top.continew.admin.biz.model.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Schema(description = "商务业绩统计")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BusinessPerformanceStatisticsResp {

    /**
     * 商务姓名
     */
    @Schema(description = "商务人员姓名", example = "周九")
    private String businessName;

    // --- 试岗期独有字段 ---
    @Schema(description = "入职时间")
    private LocalDate entryTime;

    /**
     * 微信好友数量 (仅试岗期有效)
     */
    @Schema(description = "微信好友数量 (仅试岗期有效)", example = "100", nullable = true)
    private Integer wechatFriendsCount;

    /**
     * Telegram 好友数量 (仅试岗期有效)
     */
    @Schema(description = "Telegram 好友数量 (仅试岗期有效)", example = "50", nullable = true)
    private Integer telegramFriendsCount;

    // --- 试用期独有字段 ---


    /**
     * 意向客户数 (跟进中、长期跟进中、赢单的商机数量，仅试用期有效)
     */
    @Schema(description = "意向客户数量 (仅试用期有效)", example = "20", nullable = true)
    private Integer intendedCustomersCount;


    /**
     * 广告户使用率 (已开启广告的广告户/正常广告户，仅正式期有效)
     */
    @Schema(description = "广告户使用率 (0.0-1.0之间，仅正式期有效)", example = "0.92", type = "number", format = "double", nullable = true)
    private Double adAccountUsageRate;

    /**
     * 客户单户平均消耗 (单位：元，仅正式期有效)
     */
    @Schema(description = "客户单户平均消耗 (单位：元，仅正式期有效)", example = "35000.00", type = "number", format = "double", nullable = true)
    private BigDecimal avgSingleAccountSpend;

    /**
     * 客户留存率 (当月流失客户/总客户数，以申请退款时间为准，仅正式期有效)
     */
    @Schema(description = "客户留存率 (0.0-1.0之间，仅正式期有效)", example = "0.85", type = "number", format = "double", nullable = true)
    private Double customerRetentionRate;

    // --- 多个时期通用字段 ---

    /**
     * 成交客户数 (在筛选范围内的正式客户数量)
     */
    @Schema(description = "成交客户数量 (试用期和正式期有效)", example = "8", nullable = true)
    private Integer closedCustomersCount;

    /**
     * 交友数 (从客咨表中获取总数量，试用期和正式期有效)
     */
    @Schema(description = "交友数量 (试用期和正式期有效)", example = "75", nullable = true)
    private Integer friendsCount;

    /**
     * 客户总消耗 (关联客户的卡台消耗，试用期和正式期有效)
     */
    @Schema(description = "客户总消耗 (单位：元，试用期和正式期有效)", example = "450000.00", type = "number", format = "double", nullable = true)
    private BigDecimal totalCustomerSpend;

    /**
     * 日报漏填写数量 (试用期和正式期有效)
     */
    @Schema(description = "日报漏填写数量 (试用期和正式期有效)", example = "0", nullable = true)
    private Integer dailyReportMissingCount;

    /**
     * 客咨漏填写数量 (试岗期、试用期和正式期均有效)
     */
    @Schema(description = "客咨漏填写数量 (所有周期有效)", example = "1", nullable = true)
    private Integer customerConsultingMissingCount;
}
