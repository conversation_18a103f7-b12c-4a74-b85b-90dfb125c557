package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 成本分析详情信息
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "成本分析详情信息")
public class BusinessManagerStatisticsDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @ExcelProperty(value = "统计日期")
    private LocalDate statisticsDate;

    @ExcelProperty(value = "采购成本")
    private BigDecimal purchaseCost;

    /**
     * 剩余正常库存
     */
    @Schema(description = "剩余正常库存")
    @ExcelProperty(value = "剩余正常库存")
    private Integer remainingNormalInventory;

    @ExcelProperty(value = "回收户库存")
    private Integer recycleNum;

    @ExcelProperty(value = "申诉户库存")
    private Integer appealNum;

    @ExcelProperty(value = "bm5户库存")
    private Integer bm5Num;

    @ExcelProperty(value = "bm250户库存")
    private Integer bm250Num;

    @ExcelProperty(value = "bm2500户库存")
    private Integer bm2500Num;

    @ExcelProperty(value = "bm10000户库存")
    private Integer bm10000Num;

    @ExcelProperty(value = "短链户库存")
    private Integer shortNum;

    /**
     * 库存详情
     */
    private String inventoryDetail;

    /**
     * 当日回收数量
     */
    @ExcelProperty(value = "当日回收数量")
    private Long dailyRecycleCount;

    /**
     * 当天接入坑位
     */
    @Schema(description = "当天接入坑位")
    @ExcelProperty(value = "当天接入坑位")
    private Integer dailyReceivedCount;

    /**
     * 总成本
     */
    @Schema(description = "总成本")
    @ExcelProperty(value = "总成本")
    private BigDecimal totalCost;

    /**
     * 当天剩余坑位
     */
    @ExcelProperty(value = "当天剩余坑位")
    private Long dailyRemainingCount;

    /**
     * 已使用正常
     */
    @Schema(description = "已使用正常")
    @ExcelProperty(value = "已使用正常")
    private Integer usedNormal;

    /**
     * 未使用正常
     */
    @Schema(description = "未使用正常")
    @ExcelProperty(value = "未使用正常")
    private Integer unusedNormal;

    /**
     * 已使用封禁
     */
    @Schema(description = "已使用封禁")
    @ExcelProperty(value = "已使用封禁")
    private Integer usedBanned;

    /**
     * 未使用封禁
     */
    @Schema(description = "未使用封禁")
    @ExcelProperty(value = "未使用封禁")
    private Integer unusedBanned;

    /**
     * 剩余正常坑位
     */
    @Schema(description = "剩余正常坑位")
    @ExcelProperty(value = "剩余正常坑位")
    private Integer remainingNormalCount;

    /**
     * 存活率
     */
    @Schema(description = "存活率")
    @ExcelProperty(value = "存活率(%)")
    private BigDecimal survivalRate;

    /**
     * 平均备户成本
     */
    @Schema(description = "平均备户成本")
    @ExcelProperty(value = "平均备户成本")
    private BigDecimal averagePrepareCost;

    /**
     * 备户存活率
     */
    @ExcelProperty(value = "备户存活率(%)")
    private BigDecimal prepareSurvivalRate;

    /**
     * 当日出售
     */
    @Schema(description = "当日出售")
    @ExcelProperty(value = "当日出售")
    private Integer dailySales;

    /**
     * 当日出售已绑定BM
     */
    @ExcelProperty(value = "当日出售BM户")
    private Long dailySalesForBm;

    /**
     * 当日封禁
     */
    @Schema(description = "当日封禁")
    @ExcelProperty(value = "当日封禁")
    private Integer dailyBanned;

    /**
     * 下户成本
     */
    @Schema(description = "下户成本")
    @ExcelProperty(value = "下户成本")
    private BigDecimal orderCost;

}