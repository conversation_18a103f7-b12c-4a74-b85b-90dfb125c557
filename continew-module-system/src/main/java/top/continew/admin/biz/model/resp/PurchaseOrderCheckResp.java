package top.continew.admin.biz.model.resp;

import lombok.Data;
import top.continew.admin.biz.enums.PurchaseOrderTypeEnum;

import java.math.BigDecimal;

@Data
public class PurchaseOrderCheckResp {

    private Long channelId;

    private String channelName;

    private PurchaseOrderTypeEnum type;

    private Integer purchaseNum;

    private BigDecimal purchasePrice;

    private Integer receiveNum;

    private BigDecimal receivePrice;

    private Integer writeNum;

    private BigDecimal writePrice;

    private String message;
}
