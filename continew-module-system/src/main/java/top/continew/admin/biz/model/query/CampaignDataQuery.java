package top.continew.admin.biz.model.query;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Data
public class CampaignDataQuery {

    @NotNull(message = "客户不能为空")
    private Long customerId;

    @NotBlank(message = "广告账户不能为空")
    private String adAccountId;

    @NotNull(message = "统计不能为空")
    private LocalDate statDate;

}
