package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class AdAccountSalesStatisticsQuery {
    @Schema(description = "统计时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate[] statTime;
}
