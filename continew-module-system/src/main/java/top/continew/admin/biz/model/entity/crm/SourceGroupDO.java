package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 来源分组实体
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@TableName("biz_source_group")
public class SourceGroupDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分组名称
     */
    private String name;
}