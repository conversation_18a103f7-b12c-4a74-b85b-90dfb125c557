package top.continew.admin.biz.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema; // 导入 @Schema 注解
import lombok.Data;

import java.math.BigDecimal;


@Data
public class CampaignPerformanceResp {

    @JsonIgnore
    private String platformId;

    /**
     * 广告系列名称
     */
    @Schema(description = "广告系列名称")
    private String campaignName;

    /**
     * 广告系列状态
     */
    @Schema(description = "广告系列状态")
    private String campaignStatus;

    /**
     * 已花费金额
     */
    @Schema(description = "已花费金额")
    private BigDecimal spentAmount;

    /**
     * 成效名称
     */
    @Schema(description = "成效名称")
    private String performanceMetricName;

    /**
     * 成效数量
     */
    @Schema(description = "成效数量")
    private BigDecimal performanceMetricValue;

}