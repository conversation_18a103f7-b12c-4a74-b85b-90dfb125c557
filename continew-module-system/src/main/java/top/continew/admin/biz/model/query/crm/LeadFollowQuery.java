package top.continew.admin.biz.model.query.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import top.continew.starter.extension.crud.model.query.PageQuery;

/**
 * 线索跟进记录查询
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "线索跟进记录查询")
public class LeadFollowQuery extends PageQuery {

    /**
     * 线索ID
     */
    @NotNull(message = "线索ID不能为空")
    @Schema(description = "线索ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long leadId;
}