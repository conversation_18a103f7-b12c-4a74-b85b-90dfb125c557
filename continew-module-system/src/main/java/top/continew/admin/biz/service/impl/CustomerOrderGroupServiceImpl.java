package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.req.DistributeGroupReq;
import top.continew.admin.biz.model.req.IdsReq;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.CustomerOrderGroupMapper;
import top.continew.admin.biz.model.entity.CustomerOrderGroupDO;
import top.continew.admin.biz.model.query.CustomerOrderGroupQuery;
import top.continew.admin.biz.model.req.CustomerOrderGroupReq;
import top.continew.admin.biz.model.resp.CustomerOrderGroupDetailResp;
import top.continew.admin.biz.model.resp.CustomerOrderGroupResp;
import top.continew.admin.biz.service.CustomerOrderGroupService;

import java.util.List;

/**
 * 客户下户订单分组业务实现
 *
 * <AUTHOR>
 * @since 2025/07/17 17:14
 */
@Service
@RequiredArgsConstructor
public class CustomerOrderGroupServiceImpl extends BaseServiceImpl<CustomerOrderGroupMapper, CustomerOrderGroupDO, CustomerOrderGroupResp, CustomerOrderGroupDetailResp, CustomerOrderGroupQuery, CustomerOrderGroupReq> implements CustomerOrderGroupService {

    private final AdAccountOrderService adAccountOrderService;

    @Override
    public void createGroup(CustomerOrderGroupReq req) {
        CustomerOrderGroupDO customerOrderGroupDO = new CustomerOrderGroupDO();
        customerOrderGroupDO.setName(req.getName());
        customerOrderGroupDO.setCustomerId(req.getCustomerId());
        save(customerOrderGroupDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteGroup(List<Long> ids, Long customerId) {
        remove(new LambdaQueryWrapper<CustomerOrderGroupDO>()
                .in(CustomerOrderGroupDO::getId, ids)
                .eq(CustomerOrderGroupDO::getCustomerId, customerId));
    }

    @Override
    public void editGroup(CustomerOrderGroupReq req) {
        CustomerOrderGroupDO groupDO = getById(req.getGroupId());
        CheckUtils.throwIfNull(groupDO, "分组不存在");
        lambdaUpdate()
                .eq(CustomerOrderGroupDO::getId, req.getGroupId())
                .eq(CustomerOrderGroupDO::getCustomerId, req.getCustomerId())
                .set(CustomerOrderGroupDO::getName, req.getName())
                .update();
    }

    @Override
    public void distributeGroup(DistributeGroupReq req) {
        adAccountOrderService.lambdaUpdate()
                .eq(AdAccountOrderDO::getCustomerId, req.getCustomerId())
                .in(AdAccountOrderDO::getId, req.getIds())
                .set(AdAccountOrderDO::getGroupId, req.getGroupId())
                .update();
    }

    @Override
    public void clearGroup(IdsReq req, Long customerId) {
        adAccountOrderService.lambdaUpdate()
                .eq(AdAccountOrderDO::getCustomerId, customerId)
                .in(AdAccountOrderDO::getId, req.getIds())
                .set(AdAccountOrderDO::getGroupId, null)
                .update();
    }
}