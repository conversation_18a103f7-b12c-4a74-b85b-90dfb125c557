package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.AdPlatformEnum;
import top.continew.admin.biz.enums.AdProjectEnum;
import top.continew.admin.biz.enums.PurchaseOrderStatusEnum;
import top.continew.admin.biz.enums.PurchaseOrderTypeEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 采购订单实体
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@TableName("biz_purchase_order")
public class PurchaseOrderDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 物料类型
     */
    private Long type;

    /**
     * 预计采购数量
     */
    private Integer expectNum;
    /**
     * 预计采购金额
     */
    private BigDecimal totalPrice;

    /**
     * 是否付款
     */
    private Boolean isPay;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 付款金额
     */
    private BigDecimal payPrice;

    /**
     * 状态（1=进行中，2=已完成）
     */
    private PurchaseOrderStatusEnum status;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    private Integer receiveNum;

    private BigDecimal receivePrice;

    private String receiveUser;

    /**
     * 备注
     */
    private String remark;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;

    private LocalDate purchaseTime;

    private LocalDate receiveDate;

    /**
     * 媒体平台
     */
    private AdPlatformEnum adPlatform;

    /**
     * 项目
     */
    private AdProjectEnum project;

    private Boolean openReceive;
}