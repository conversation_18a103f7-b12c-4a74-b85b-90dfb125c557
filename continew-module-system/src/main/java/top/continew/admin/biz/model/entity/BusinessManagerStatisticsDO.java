package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 成本分析实体
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
@Data
@TableName("biz_business_manager_statistics")
public class BusinessManagerStatisticsDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDate statisticsDate;

    /**
     * 剩余正常库存
     */
    private Long remainingNormalInventory;

    /**
     * 库存详情
     */
    private String inventoryDetail;

    /**
     * 当日回收数量
     */
    private Long dailyRecycleCount;

    /**
     * 当天接入坑位
     */
    private Long dailyReceivedCount;

    /**
     * 总成本
     */
    private BigDecimal totalCost;

    /**
     * 已使用正常
     */
    private Long usedNormal;

    /**
     * 未使用正常
     */
    private Long unusedNormal;

    /**
     * 已使用封禁
     */
    private Long usedBanned;

    /**
     * 未使用封禁
     */
    private Long unusedBanned;

    /**
     * 剩余正常坑位
     */
    private Long remainingNormalCount;

    /**
     * 当天剩余坑位
     */
    private Long dailyRemainingCount;

    /**
     * 存活率
     */
    private BigDecimal survivalRate;

    /**
     * 平均备户成本
     */
    private BigDecimal averagePrepareCost;

    /**
     * 备户存活率
     */
    private BigDecimal prepareSurvivalRate;

    /**
     * 当日出售
     */
    private Long dailySales;

    /**
     * 当日出售已绑定BM
     */
    private Long dailySalesForBm;

    /**
     * 当日封禁
     */
    private Long dailyBanned;

    /**
     * 下户成本
     */
    private BigDecimal orderCost;

    private BigDecimal purchaseCost;

}