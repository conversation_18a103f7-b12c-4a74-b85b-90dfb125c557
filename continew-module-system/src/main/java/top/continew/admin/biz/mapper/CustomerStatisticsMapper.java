package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.query.BusinessStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerStatisticsQuery;
import top.continew.admin.biz.model.resp.BusinessStatisticsResp;
import top.continew.admin.biz.model.resp.CustomerSpentTrendResp;
import top.continew.admin.biz.model.resp.CustomerStatisticsOverviewResp;
import top.continew.admin.biz.model.resp.CustomerStatisticsResp;

import java.util.List;

@Mapper
public interface CustomerStatisticsMapper {

    /**
     * 查询客户统计数据
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 统计数据
     */
    IPage<CustomerStatisticsResp> selectStatisticsWithCondition(@Param("page") IPage<CustomerStatisticsResp> page,
                                                                @Param("query") CustomerStatisticsQuery query);

    List<CustomerStatisticsResp> selectCustomerStatisticsList(@Param("query") CustomerStatisticsQuery query);

    List<CustomerStatisticsResp> selectStatisticsWithoutCondition(@Param("page") IPage<CustomerStatisticsResp> page,
                                                                  @Param("query") CustomerStatisticsQuery query);

    List<BusinessStatisticsResp> selectBusinessStatistics(@Param("page") IPage<BusinessStatisticsResp> page,
                                                          @Param("query") BusinessStatisticsQuery query);

    List<BusinessStatisticsResp> selectBusinessStatisticsList(@Param("query") BusinessStatisticsQuery query);

    /**
     * 查询客户统计总览数据
     *
     * @param query 查询条件
     * @return 统计总览数据
     */
    CustomerStatisticsOverviewResp selectCustomerStatisticsSummary(@Param("query") CustomerStatisticsQuery query);

    CustomerStatisticsOverviewResp selectStatisticsOverviewWithoutCondition(@Param("query") CustomerStatisticsQuery query);

    /**
     * 查询客户对比趋势
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<CustomerSpentTrendResp> selectCustomerSpentTrendList(@Param("page") IPage<CustomerSpentTrendResp> page,
                                                               @Param(Constants.WRAPPER) QueryWrapper<CustomerSpentTrendResp> queryWrapper);
}
