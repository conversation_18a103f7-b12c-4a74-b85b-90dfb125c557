package top.continew.admin.biz.model.query.crm;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 客户回访任务查询条件
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Data
@Schema(description = "客户回访任务查询条件")
public class CustomerVisitTaskQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 任务状态：1-待处理，2-处理中，3-已完成，4-已关闭
     */
    @Schema(description = "任务状态：1-待处理，2-处理中，3-已完成，4-已关闭")
    @Query(type = QueryType.EQ)
    private Integer taskStatus;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    @Query(type = QueryType.EQ)
    private Long assigneeId;
}