/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.RechargeOrderDO;
import top.continew.admin.biz.model.resp.RechargeOrderResp;
import top.continew.admin.biz.model.resp.UserOrderStatResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 充值订单 Mapper
 *
 * <AUTHOR>
 * @since 2024/12/30 17:09
 */
public interface RechargeOrderMapper extends BaseMapper<RechargeOrderDO> {

    IPage<RechargeOrderResp> selectCustomPage(@Param("page") IPage<RechargeOrderDO> page,
                                              @Param(Constants.WRAPPER) QueryWrapper<RechargeOrderDO> queryWrapper);

    List<RechargeOrderResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<RechargeOrderDO> queryWrapper);

    List<UserOrderStatResp> selectUserOrderStat(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);


    List<RechargeOrderDO> selectLast(@Param("adAccountIds") List<String> adAccountIds);
}