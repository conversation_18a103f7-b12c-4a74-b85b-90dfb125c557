package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.AdAccountSaleStatusEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * bm坑位查询条件
 *
 * <AUTHOR>
 * @since 2025/03/11 11:52
 */
@Data
@Schema(description = "bm坑位查询条件")
public class BusinessManagerItemQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Query(type = QueryType.EQ, columns = {"bbmi.channel_id"})
    private Long businessManagerChannelId;


    @Query(type = QueryType.EQ, columns = {"bbmi.type"})
    private Long type;

    /**
     * bm 主键ID
     */
    @Schema(description = "bm 主键ID")
    @Query(type = QueryType.EQ, columns = {"bbm.platform_id"})
    private String businessManager;

    /**
     * 状态（1=正常，2=封禁）
     */
    @Schema(description = "状态（1=正常，2=封禁）")
    @Query(type = QueryType.EQ, columns = {"bbmi.status"})
    private Integer status;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @Query(type = QueryType.EQ, columns = {"bbmi.platform_ad_id"})
    private String platformAdId;

    /**
     * 是否使用
     */
    @Schema(description = "是否使用")
    @Query(type = QueryType.EQ, columns = {"bbmi.is_use"})
    private Boolean isUse;

    /**
     * 使用时间
     */
    @Schema(description = "使用时间")
    @Query(type = QueryType.BETWEEN, columns = {"bbmi.use_time"})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] useTime;

    /**
     * 封禁时间
     */
    @Schema(description = "封禁时间")
    @Query(type = QueryType.BETWEEN, columns = {"bbmi.ban_time"})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] banTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN, columns = {"bbmi.create_time"})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] createTime;


    @Schema(description = "出售时间")
    @Query(type = QueryType.BETWEEN, columns = {"a.sale_time"})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] saleTime;

    @Query(type = QueryType.EQ, columns = {"a.sale_status"})
    private AdAccountSaleStatusEnum saleStatus;

    @Query(type = QueryType.EQ,columns = {"bbmi.is_bu"})
    private Boolean isBu;
}