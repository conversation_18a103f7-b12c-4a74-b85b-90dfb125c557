/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.methods.send.SendPhoto;
import top.continew.admin.biz.event.BusinessTelegramMessageEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.robot.TelegramBotMessageProcessor;
import top.continew.admin.biz.robot.business.BusinessTelegramBotMessageProcessor;

@Component
@Slf4j
@RequiredArgsConstructor
public class TelegramListener {

    @Async
    @EventListener
    public void sendMessage(TelegramMessageEvent event) {
        if (event.getSource() instanceof SendMessage) {
            TelegramBotMessageProcessor.pushTextMessage((SendMessage)event.getSource());
        } else if (event.getSource() instanceof SendPhoto) {
            TelegramBotMessageProcessor.pushPhotoMessage((SendPhoto)event.getSource());
        }
    }

    @Async
    @EventListener
    public void sendMessage(BusinessTelegramMessageEvent event) {
        if (event.getSource() instanceof SendMessage) {
            BusinessTelegramBotMessageProcessor.pushTextMessage((SendMessage)event.getSource());
        } else if (event.getSource() instanceof SendPhoto) {
            BusinessTelegramBotMessageProcessor.pushPhotoMessage((SendPhoto)event.getSource());
        }
    }
}
