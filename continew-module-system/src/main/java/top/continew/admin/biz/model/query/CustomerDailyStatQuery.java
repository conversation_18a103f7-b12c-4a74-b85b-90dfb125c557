package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 客户每日统计查询条件
 *
 * <AUTHOR>
 * @since 2025/07/17 10:11
 */
@Data
@Schema(description = "客户每日统计查询条件")
public class CustomerDailyStatQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @Query(type = QueryType.EQ, columns = "customer_id")
    private Long customerId;

    /**
     * 关联客户
     */
    @Schema(description = "关联商务")
    @Query(type = QueryType.EQ, columns = "c.business_user_id")
    private Long businessUserId;

    /**
     * 统计时间
     */
    @Schema(description = "统计时间")
    @Query(type = QueryType.BETWEEN, columns = "stat_date")
    private LocalDate[] statDate;

    @QueryIgnore
    private Boolean usedRateLowerThan50percent;
}