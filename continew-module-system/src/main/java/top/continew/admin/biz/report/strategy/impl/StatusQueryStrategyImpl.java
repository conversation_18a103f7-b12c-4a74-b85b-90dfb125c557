/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.enums.FbAccountStatusEnum;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.event.AdAccountStatusChangeEvent;
import top.continew.admin.biz.event.FbAccountUpdateEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.FbAccountDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.system.model.entity.UserDO;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class StatusQueryStrategyImpl implements ReportOpsStrategy {

    private final AdAccountService adAccountService;

    @Override
    public ReportType getReport() {
        return ReportType.ADS_ACCOUNT_STORE_NEW_SOURCE_SERVER_QUERY;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {

        JSONObject data = JSONObject.from(browserReq.getData());
        String adAccountId = data.getJSONObject("body").getString("adAccountID").replace("act_", "");

        String accountStatus = data.getJSONObject("res")
            .getJSONObject("data")
            .getJSONObject("ad_account")
            .getString("account_status");
        if (!accountStatus.equals("ACTIVE")) {
            FbAccountDO fbAccountDO = new FbAccountDO();
            fbAccountDO.setStatus(FbAccountStatusEnum.BANNED);
            fbAccountDO.setPlatformAccountId(browserReq.getFbAccountId());
            fbAccountDO.setBrowserSerialNo(browserReq.getBrowserNo());
            fbAccountDO.setBrowserId(browserReq.getBrowserId());
            SpringUtil.publishEvent(new FbAccountUpdateEvent(fbAccountDO));
            AdAccountStatusEnum accountStatusEnum = switch (accountStatus) {
                case "DISABLED" -> AdAccountStatusEnum.BANNED;
                case "UNSETTLED" -> AdAccountStatusEnum.ABNORMAL;
                case "PENDING_CLOSURE" -> AdAccountStatusEnum.PENDING_CLOSURE;
                case "CLOSED" -> AdAccountStatusEnum.CLOSED;
                default -> AdAccountStatusEnum.NORMAL;
            };
            adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                .set(AdAccountDO::getAccountStatus, accountStatusEnum)
                .set(AdAccountDO::getBanTime, LocalDateTime.now())
                .eq(AdAccountDO::getPlatformAdId, adAccountId)
                .eq(AdAccountDO::getAccountStatus, AdAccountStatusEnum.NORMAL));
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("platformAdId", adAccountId);
        jsonObject.put("accountStatus", accountStatus);
        return jsonObject.toString();
    }
}