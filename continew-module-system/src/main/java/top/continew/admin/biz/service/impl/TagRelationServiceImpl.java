package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.TagRelationMapper;
import top.continew.admin.biz.model.entity.TagRelationDO;
import top.continew.admin.biz.model.query.TagRelationQuery;
import top.continew.admin.biz.model.req.TagRelationReq;
import top.continew.admin.biz.model.resp.TagRelationDetailResp;
import top.continew.admin.biz.model.resp.TagRelationResp;
import top.continew.admin.biz.service.TagRelationService;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.util.List;

/**
 * 标签关联业务实现
 *
 * <AUTHOR>
 * @since 2025/05/08 16:37
 */
@Service
@RequiredArgsConstructor
public class TagRelationServiceImpl extends BaseServiceImpl<TagRelationMapper, TagRelationDO, TagRelationResp, TagRelationDetailResp, TagRelationQuery, TagRelationReq> implements TagRelationService {
    @Override
    public List<Long> listOrderId(List<Long> tagIds) {
        return this.baseMapper.listOrderId(tagIds);
    }

    @Override
    public List<Long> listAdAccountId(List<Long> tagIds) {
        return this.baseMapper.listAdAccountId(tagIds);
    }

    @Override
    public String tagNamesByOrderId(Long orderId) {
        return baseMapper.tagNamesByOrderId(orderId);
    }
}