package top.continew.admin.biz.model.resp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AdAccountOrderStatisticsResp {

    /**
     * 总下户数
     */
    private Integer totalOrderCount;

    /**
     * 正常数
     */
    private Integer normalCount;

    /**
     * 正常率
     */
    private BigDecimal normalRate;

    /**
     * 挂户数
     */
    private Integer suspendedCount;

    /**
     * 挂户率
     */
    private BigDecimal suspendedRate;

    /**
     * 未开启广告户数
     */
    private Integer notStartedCount;

    /**
     * 不消耗户数
     */
    private Integer noSpendCount;

    /**
     * 不消耗户率
     */
    private BigDecimal noSpendRate;

}