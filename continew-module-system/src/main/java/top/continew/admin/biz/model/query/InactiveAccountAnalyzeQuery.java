package top.continew.admin.biz.model.query;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 异常广告户分析查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InactiveAccountAnalyzeQuery extends PageQuery {
    
    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 广告户ID
     */
    private String platformAdId;

    /**
     * 清零状态
     */
    private Integer clearStatus;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 账号类型（BM类型）
     */
    private List<Long> bmItemType;

    /**
     * BM授权时间范围
     */
    private List<String> bmAuthTime;

    /**
     * 统计天数，默认7天
     */
    private Integer day;

    /**
     * 消耗金额限制，默认10
     */
    private Integer spentLimit;

    /**
     * 下户天数，默认7天前
     */
    private Integer orderDaysBefore;

    private Boolean isStartCampaign;
}