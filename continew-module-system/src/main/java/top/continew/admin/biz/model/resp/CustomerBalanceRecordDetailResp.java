/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.net.URL;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.converters.string.StringImageConverter;
import com.alibaba.excel.converters.url.UrlImageConverter;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

/**
 * 客户余额变更记录详情信息
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "客户余额变更记录详情信息")
public class CustomerBalanceRecordDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    @AssembleMethod(props = @Mapping(src = "name", ref = "customerName"), targetType = CustomerService.class, method = @ContainerMethod(bindMethod = "get", resultType = CustomerResp.class))
    @Schema(description = "关联客户")
    private Long customerId;
    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @ExcelProperty(value = "交易时间")
    private LocalDateTime transTime;

    @ExcelProperty(value = "客户")
    private String customerName;

    @ExcelProperty(value = "广告户ID")
    private String platformAdId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @ExcelProperty(value = "交易类型", converter = ExcelBaseEnumConverter.class)
    private CustomerBalanceTypeEnum type;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    @ExcelProperty(value = "交易金额")
    private BigDecimal amount;

    /**
     * 交易金额
     */
    @Schema(description = "交易后余额")
    @ExcelProperty(value = "交易后余额")
    private BigDecimal afterAmount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "凭证", converter = UrlImageConverter.class)
    private URL certificate;

    private String walletTransactionId;
}