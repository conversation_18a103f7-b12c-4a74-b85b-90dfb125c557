package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.AdProductStatDO;
import top.continew.admin.biz.model.resp.AdProductStatDetailResp;
import top.continew.admin.biz.model.resp.AdProductStatResp;
import top.continew.admin.biz.model.resp.ProfitDetailResp;
import top.continew.admin.biz.model.resp.ProfitResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.ProfitDO;

import java.util.List;

/**
* 利润 Mapper
*
* <AUTHOR>
* @since 2025/08/16 16:14
*/
public interface ProfitMapper extends BaseMapper<ProfitDO> {

    IPage<ProfitResp> selectCustomPage(@Param("page") IPage<ProfitDO> page,
                                       @Param(Constants.WRAPPER) QueryWrapper<ProfitDO> queryWrapper);

    List<ProfitDetailResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<ProfitDO> queryWrapper);
}