package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 客户问题查询条件
 *
 * <AUTHOR>
 * @since 2025/04/30 14:35
 */
@Data
@Schema(description = "客户问题查询条件")
public class CustomerQuestionQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @Query(type = QueryType.EQ)
    private Integer type;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;


    @Query(type = QueryType.LIKE)
    private String remark;
}