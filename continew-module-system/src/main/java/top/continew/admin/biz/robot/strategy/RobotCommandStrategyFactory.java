/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
public class RobotCommandStrategyFactory {

    private Map<RobotCommandEnum, RobotCommandStrategy> strategies;

    @Autowired
    public RobotCommandStrategyFactory(Set<RobotCommandStrategy> strategySet) {
        createStrategy(strategySet);
    }

    public RobotCommandStrategy findStrategy(int platform) {
        RobotCommandEnum fansCountPlatformEnum = null;
        for (RobotCommandEnum platformEnum : RobotCommandEnum.values()) {
            if (platformEnum.getValue() == platform) {
                fansCountPlatformEnum = platformEnum;
                break;
            }
        }
        if (fansCountPlatformEnum == null) {
            return null;
        }
        return strategies.get(fansCountPlatformEnum);
    }

    public RobotCommandStrategy findStrategy(RobotCommandEnum platform) {
        return strategies.get(platform);
    }

    private void createStrategy(Set<RobotCommandStrategy> strategySet) {
        strategies = new HashMap<>();
        strategySet.forEach(strategy -> strategies.put(strategy.getCommand(), strategy));
    }
}
