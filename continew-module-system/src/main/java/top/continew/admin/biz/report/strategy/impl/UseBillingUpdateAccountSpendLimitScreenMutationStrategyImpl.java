package top.continew.admin.biz.report.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.ClearOrderService;
import top.continew.admin.biz.service.RechargeOrderService;
import top.continew.admin.biz.service.RefundOrderService;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.core.exception.BusinessException;

import java.math.BigDecimal;

@RequiredArgsConstructor
@Service
public class UseBillingUpdateAccountSpendLimitScreenMutationStrategyImpl implements ReportOpsStrategy {


    private final AdAccountService adAccountService;

    private final RechargeOrderService rechargeOrderService;

    private final RefundOrderService refundOrderService;

    private final ClearOrderService clearOrderService;

    @Override
    public ReportType getReport() {
        return ReportType.USE_BILLING_UPDATE_ACCOUNT_SPEND_LIMIT_SCREEN_MUTATION;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
        JSONObject data = JSONObject.from(browserReq.getData());
        JSONObject input = data.getJSONObject("body").getJSONObject("input");
        String platformAdId = input.getString("billable_account_payment_legacy_account_id");
//        JSONObject loggingData = input.getJSONObject("logging_data");
//        String loggingId = loggingData.getString("logging_id");
        String loggingId = "";
        JSONObject newSpendLimit = input.getJSONObject("new_spend_limit");
        BigDecimal newSpendLimitAmount = newSpendLimit.getBigDecimal("amount");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("platformAdId", platformAdId);
        jsonObject.put("loggingId", loggingId);
        jsonObject.put("newSpendLimitAmount", newSpendLimitAmount);
        JSONObject res = data.getJSONObject("res");
        JSONObject resData = res.getJSONObject("data");
        if (resData == null) {
            return "";
        }
        JSONObject billableAccountEditSpendLimit = resData.getJSONObject("billable_account_edit_spend_limit");
        if (billableAccountEditSpendLimit == null) {
            return "";
        }
        JSONObject billableAccount = billableAccountEditSpendLimit.getJSONObject("billable_account");
        if (billableAccount == null) {
            return "";
        }
        JSONObject spendingInfo = billableAccount.getJSONObject("spending_info");
        if (spendingInfo == null) {
            return "";
        }
        AdAccountDO adAccount = adAccountService.getByPlatformAdId(platformAdId);
        if (adAccount == null) {
            return "";
        }
        try {
            if (newSpendLimitAmount.compareTo(new BigDecimal("0.01")) == 0) {
                clearOrderService.uploadModifyLimitCertificate(platformAdId, loggingId, "");
            } else {
                // 养号中
                if (newSpendLimitAmount.compareTo(new BigDecimal(20)) <= 0) {
                    return jsonObject.toString();
                }
                try {
                    rechargeOrderService.uploadModifyLimitCertificate(platformAdId, loggingId, "");
                } catch (BusinessException e) {
                    refundOrderService.uploadModifyLimitCertificate(platformAdId, loggingId, "");
                }
            }
        } catch (Exception ignore) {

        }
        return jsonObject.toString();
    }
}
