package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 测试任务详情信息
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "测试任务详情信息")
public class TestOrderDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 测试标题
     */
    @Schema(description = "测试标题")
    @ExcelProperty(value = "测试标题")
    private String title;

    /**
     * trello链接
     */
    @Schema(description = "trello链接")
    @ExcelProperty(value = "trello链接")
    private String trelloUrl;

    /**
     * 状态(1=待进行，2=进行中，3=已完成）
     */
    @Schema(description = "状态(1=待进行，2=进行中，3=已完成）")
    @ExcelProperty(value = "状态(1=待进行，2=进行中，3=已完成）")
    private Integer status;
}