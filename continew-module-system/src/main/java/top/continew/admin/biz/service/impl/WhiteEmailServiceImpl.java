package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.WhiteEmailMapper;
import top.continew.admin.biz.model.entity.WhiteEmailDO;
import top.continew.admin.biz.model.query.WhiteEmailQuery;
import top.continew.admin.biz.model.req.WhiteEmailReq;
import top.continew.admin.biz.model.resp.WhiteEmailDetailResp;
import top.continew.admin.biz.model.resp.WhiteEmailResp;
import top.continew.admin.biz.service.WhiteEmailService;

import java.util.ArrayList;
import java.util.List;

/**
 * 白名单邮箱业务实现
 *
 * <AUTHOR>
 * @since 2025/07/01 14:36
 */
@Service
@RequiredArgsConstructor
public class WhiteEmailServiceImpl extends BaseServiceImpl<WhiteEmailMapper, WhiteEmailDO, WhiteEmailResp, WhiteEmailDetailResp, WhiteEmailQuery, WhiteEmailReq> implements WhiteEmailService {
    @Override
    public void saveByEmail(List<String> emails) {
        if (emails.isEmpty()) {
            return;
        }
        List<WhiteEmailDO> whiteEmailDOS = new ArrayList<>();
        for (String email : emails) {
            WhiteEmailDO whiteEmailDO = new WhiteEmailDO();
            whiteEmailDO.setEmail(email);
            whiteEmailDOS.add(whiteEmailDO);
        }
        this.saveBatch(whiteEmailDOS);
    }
}