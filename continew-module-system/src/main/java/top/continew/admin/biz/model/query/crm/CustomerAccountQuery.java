package top.continew.admin.biz.model.query.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 客户社交账号查询请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "客户账号查询请求")
public class CustomerAccountQuery {

    @Schema(description = "实体类型：1-线索、2-商机、3-客户")
    @NotNull(message = "实体类型不能为空")
    private Integer entityType;

    @Schema(description = "实体ID")
    @NotNull(message = "实体ID不能为空")
    private Long entityId;
}