package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 投放日报明细查询条件
 *
 * <AUTHOR>
 * @since 2025/08/13 21:02
 */
@Data
@Schema(description = "投放日报明细查询条件")
public class AdProductStatItemQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计数据ID
     */
    @Schema(description = "统计数据ID")
    @Query(type = QueryType.EQ)
    private Long statId;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    @Query(type = QueryType.EQ)
    private String platformAdId;
}