/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.util.List;

/**
 * 客户周期对比查询条件
 *
 * <AUTHOR>
 * @since 2024/12/31 10:00
 */
@Data
@Schema(description = "客户周期对比查询条件")
public class CustomerCycleComparePeriodQuery {

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private List<Long> customerIds;


    /**
     * 周期类型：1是天，2是周，3是月
     */
    @Schema(description = "周期类型")
    private Integer periodType;

    /**
     * 对比周期数
     */
    @Schema(description = "对比周期数")
    private Integer comparePeriods;

    /**
     * 消耗区间配置
     */
    @Schema(description = "消耗区间配置")
    private List<SpentRangeConfig> spentRanges;

    /**
     * 消耗区间配置内部类
     */
    @Data
    @Schema(description = "消耗区间配置")
    public static class SpentRangeConfig {
        /**
         * 区间标签
         */
        @Schema(description = "区间标签")
        private String label;

        /**
         * 最小值
         */
        @Schema(description = "最小值")
        private Integer min;

        /**
         * 最大值（null表示无上限）
         */
        @Schema(description = "最大值")
        private Integer max;
    }

}