/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.entity.CustomerRequirementDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.ProcessOfAdAccountOrderReq;
import top.continew.admin.biz.robot.strategy.BaseRobotCommandService;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.CustomerRequirementService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ProcessOfAdAccountOrderStrategyImpl extends BaseRobotCommandService implements RobotCommandStrategy {

    private final CustomerRequirementService customerRequirementService;

    private final AdAccountOrderService adAccountOrderService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.PROCESS_OF_AD_ACCOUNT_ORDER;
    }

    @Override
    public String execute(Update update) {
        if (!telegramChatIdConfig.getBusinessChatId().equals(update.getMessage().getChatId())) {
            return null;
        }
        ProcessOfAdAccountOrderReq req = BotUtils.parseBotMsgToObject(update.getMessage()
            .getText(), "\n", ":", ProcessOfAdAccountOrderReq.class);
        LocalDate date;
        try {
            date = LocalDate.parse(req.getDate());
        } catch (DateTimeParseException e) {
            return "日期格式错误，示例2025-03-22";
        }
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = start.plusDays(1);
        long totalRequirement = customerRequirementService.list()
            .stream()
            .mapToInt(CustomerRequirementDO::getQuantity)
            .sum();
        List<AdAccountOrderDO> orderList = adAccountOrderService.list(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .between(AdAccountOrderDO::getCreateTime, start, end)
            .in(AdAccountOrderDO::getStatus, List.of(AdAccountOrderStatusEnum.PENDING, AdAccountOrderStatusEnum.PROCESS, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.RECYCLE)));
        StringBuilder result = new StringBuilder();
        result.append("总需求量：").append(totalRequirement).append("\n").append("==================").append("\n");
        result.append("已提交数：").append(orderList.size()).append("\n");
        result.append("待处理：")
            .append(orderList.stream().filter(v -> v.getStatus().equals(AdAccountOrderStatusEnum.PENDING)).count())
            .append("\n");
        result.append("进行中：")
            .append(orderList.stream().filter(v -> v.getStatus().equals(AdAccountOrderStatusEnum.PROCESS)).count())
            .append("\n");
        result.append("已完成：")
            .append(orderList.stream()
                .filter(v -> v.getStatus().equals(AdAccountOrderStatusEnum.AUTH_COMPLETED))
                .count())
            .append("\n");
        result.append("已回收：")
            .append(orderList.stream().filter(v -> v.getStatus().equals(AdAccountOrderStatusEnum.RECYCLE)).count())
            .append("\n");
        return result.toString();
    }
}
