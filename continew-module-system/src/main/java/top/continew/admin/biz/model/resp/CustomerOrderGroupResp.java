package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 客户下户订单分组信息
 *
 * <AUTHOR>
 * @since 2025/07/17 17:14
 */
@Data
@Schema(description = "客户下户订单分组信息")
public class CustomerOrderGroupResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
}