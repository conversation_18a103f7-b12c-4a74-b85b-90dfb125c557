package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.resp.AdAccountResp;
import top.continew.admin.biz.model.resp.BusinessManagerItemResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.BusinessManagerItemDO;

import java.time.LocalDateTime;
import java.util.List;

/**
* bm坑位 Mapper
*
* <AUTHOR>
* @since 2025/03/11 11:52
*/
public interface BusinessManagerItemMapper extends BaseMapper<BusinessManagerItemDO> {

    IPage<BusinessManagerItemResp> selectCustomPage(@Param("page") IPage<BusinessManagerItemDO> page,
                                                    @Param(Constants.WRAPPER) QueryWrapper<BusinessManagerItemDO> queryWrapper);

    List<BusinessManagerItemResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<BusinessManagerItemDO> queryWrapper);
}