/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.mapper.CardBalanceMapper;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.query.CardBalanceQuery;
import top.continew.admin.biz.model.req.CardBalanceReq;
import top.continew.admin.biz.model.resp.CardBalanceDetailResp;
import top.continew.admin.biz.model.resp.CardBalanceResp;
import top.continew.admin.biz.service.CardBalanceService;
import top.continew.admin.biz.utils.CardHelper;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 卡片余额流水业务实现
 *
 * <AUTHOR>
 * @since 2024/12/29 13:45
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CardBalanceServiceImpl extends BaseServiceImpl<CardBalanceMapper, CardBalanceDO, CardBalanceResp, CardBalanceDetailResp, CardBalanceQuery, CardBalanceReq> implements CardBalanceService {

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    @Override
    public List<CardBalanceDO> listByPlatform(Integer platform) {
        return this.list(Wrappers.<CardBalanceDO>lambdaQuery().eq(CardBalanceDO::getPlatform, platform));
    }

    @Override
    public void syncData(LocalDateTime start, LocalDateTime end) {
        for (CardPlatformEnum value : CardPlatformEnum.values()) {
            if (!value.isEnable()) {
                continue;
            }
            this.syncData(value, start, end);
        }
    }

    @Override
    @Async
    public void syncData(CardPlatformEnum platform, LocalDateTime start, LocalDateTime end) {
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(platform);
        if (cardOpsStrategy != null) {
            List<CardBalanceDO> saveList = new ArrayList<>();
            List<CardBalanceDO> list = cardOpsStrategy.getCardBalanceList(start, end);
            List<CardBalanceDO> existList = this.listByPlatform(platform.getValue());
            for (CardBalanceDO item : list) {
                CardBalanceDO exist = existList.stream()
                    .filter(v -> v.getTransactionId().equalsIgnoreCase(item.getTransactionId()))
                    .findFirst()
                    .orElse(null);
                if (exist == null) {
                    if(CardPlatformEnum.PHOTON_PAY.equals(platform) && StrUtil.isNotBlank(item.getPlatformCardId())) {
                        String cardNumber = CardHelper.getCardNumber(platform, item.getPlatformCardId());
                        if(StrUtil.isNotBlank(cardNumber)) {
                            item.setCardNumber(cardNumber);
                        }
                    }
                    saveList.add(item);
                }
            }
            this.saveBatch(saveList);
            log.info("======【{}】卡片余额流水入库完成======", platform.getDescription());
            if (!saveList.isEmpty()) {
                // 光子易中，对list按platformCardId进行分组，取到最近一条交易的余额(交易时间：transTime，余额为afterAmount)，进行卡片余额的更新
                if(CardPlatformEnum.PHOTON_PAY.equals(platform)) {
                    Map<String, List<CardBalanceDO>> groupedByPlatformCardId = saveList.stream()
                        .collect(Collectors.groupingBy(CardBalanceDO::getPlatformCardId));

                    for (Map.Entry<String, List<CardBalanceDO>> entry : groupedByPlatformCardId.entrySet()) {
                        List<CardBalanceDO> group = entry.getValue();
                        // 按交易时间排序，取最近一条交易
                        group.sort(Comparator.comparing(CardBalanceDO::getTransTime).reversed());
                        if (!group.isEmpty()) {
                            CardBalanceDO latestTransaction = group.get(0);
                            CardHelper.updateCardBalance(CardPlatformEnum.PHOTON_PAY, latestTransaction.getPlatformCardId(), latestTransaction.getAfterAmount());
                            log.info("光子易处理卡片余额更新，平台ID：{}, 卡号：{}，余额：{}", latestTransaction.getPlatformCardId(), latestTransaction.getCardNumber(), latestTransaction.getAfterAmount());
                        }
                    }
                }
            }
        }
    }


    @Override
    public void updateCardNumber(CardPlatformEnum platform) {
        baseMapper.updateCardNumber(platform.getValue());
    }
}