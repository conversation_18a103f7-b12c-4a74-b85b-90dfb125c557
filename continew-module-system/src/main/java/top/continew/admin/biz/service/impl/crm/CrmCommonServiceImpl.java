package top.continew.admin.biz.service.impl.crm;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.VisitTaskStatusEnum;
import top.continew.admin.biz.mapper.crm.CustomerVisitTaskMapper;
import top.continew.admin.biz.mapper.crm.LeadMapper;
import top.continew.admin.biz.mapper.crm.OpportunityMapper;
import top.continew.admin.biz.model.entity.crm.CustomerVisitTaskDO;
import top.continew.admin.biz.model.resp.crm.CrmWorkbenchResp;
import top.continew.admin.biz.service.crm.CrmCommonService;
import top.continew.admin.system.service.OptionService;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * CRM公共服务实现类
 *
 * @version: 1.00.00
 * @description:
 * @date: 2025/6/3 14:53
 */
@Service
@RequiredArgsConstructor
public class CrmCommonServiceImpl implements CrmCommonService {
    private final LeadMapper leadMapper;
    private final OpportunityMapper opportunityMapper;
    private final OptionService optionService;
    private final CustomerVisitTaskMapper customerVisitTaskMapper;


    @Override
    public CrmWorkbenchResp getCrmWorkbenchStats(boolean showLeadCount, boolean showOpportunityCount, boolean showVisitTaskCount) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayStart = now.with(LocalTime.MIN);
        LocalDateTime todayEnd = now.with(LocalTime.MAX);
    
        CrmWorkbenchResp resp = new CrmWorkbenchResp();
        
        // 根据权限决定是否执行线索统计
        if (showLeadCount) {
            // 获取配置的超时时长
            Integer leadWaitOverHour = getConfigValue("CRM_LEAD_WAIT_OVER_HOUR", 24);
            Integer leadPendingOverHour = getConfigValue("CRM_LEAD_PENDING_OVER_HOUR", 72);
            
            // 统计线索数据
            resp.setLeadPendingCount(leadMapper.countPendingLeads(currentUserId));
            resp.setLeadTodayFollowCount(leadMapper.countTodayFollowLeads(
                    currentUserId, todayStart, todayEnd, leadWaitOverHour, leadPendingOverHour));
            resp.setLeadOverdueCount(leadMapper.countOverdueLeads(
                    currentUserId, now, leadWaitOverHour, leadPendingOverHour));
        } else {
            // 设置默认值
            resp.setLeadPendingCount(0L);
            resp.setLeadTodayFollowCount(0L);
            resp.setLeadOverdueCount(0L);
        }
    
        // 根据权限决定是否执行任务统计
        if (showVisitTaskCount) {
            resp.setVisitTaskTodayFollowCount(customerVisitTaskMapper.selectCount(new LambdaQueryWrapper<CustomerVisitTaskDO>()
                    .eq(CustomerVisitTaskDO::getAssigneeId, currentUserId).in(CustomerVisitTaskDO::getTaskStatus, VisitTaskStatusEnum.TO_DO, VisitTaskStatusEnum.PENDING)
                    .between(CustomerVisitTaskDO::getReminderTime, todayStart, todayEnd)));
            //requiredFinishTime小于当前时间，表示超时
            resp.setVisitTaskOverdueCount(customerVisitTaskMapper.selectCount(new LambdaQueryWrapper<CustomerVisitTaskDO>().eq(CustomerVisitTaskDO::getAssigneeId, currentUserId).in(CustomerVisitTaskDO::getTaskStatus, VisitTaskStatusEnum.TO_DO, VisitTaskStatusEnum.PENDING)
                    .lt(CustomerVisitTaskDO::getRequiredFinishTime, now)));
        } else {
            // 设置默认值
            resp.setVisitTaskTodayFollowCount(0L);
            resp.setVisitTaskOverdueCount(0L);
        }


        // 根据权限决定是否执行商机统计
        if (showOpportunityCount) {
            // 获取配置的超时时长
            Integer opportunityWaitOverHour = getConfigValue("CRM_OPPORTUNITY_WAIT_OVER_HOUR", 24);
            Integer opportunityPendingOverHour = getConfigValue("CRM_OPPORTUNITY_PENDING_OVER_HOUR", 72);

            // 统计商机数据
            resp.setOpportunityPendingCount(opportunityMapper.countPendingOpportunities(currentUserId));
            resp.setOpportunityTodayFollowCount(opportunityMapper.countTodayFollowOpportunities(
                    currentUserId, todayStart, todayEnd, opportunityWaitOverHour, opportunityPendingOverHour));
            resp.setOpportunityOverdueCount(opportunityMapper.countOverdueOpportunities(
                    currentUserId, now, opportunityWaitOverHour, opportunityPendingOverHour));
        } else {
            // 设置默认值
            resp.setOpportunityPendingCount(0L);
            resp.setOpportunityTodayFollowCount(0L);
            resp.setOpportunityOverdueCount(0L);
        }
    
        return resp;
    }

    /**
     * 获取配置值
     *
     * @param code 配置编码
     * @param defaultValue 默认值
     * @return 配置值
     */
    private Integer getConfigValue(String code, Integer defaultValue) {
        try {
            return optionService.getValueByCode2Int(code);
        } catch (Exception e) {
            return defaultValue;
        }
    }
}
