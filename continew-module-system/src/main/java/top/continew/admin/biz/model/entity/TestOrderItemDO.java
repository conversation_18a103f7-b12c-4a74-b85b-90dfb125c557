package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 测试任务详情实体
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Data
@TableName("biz_test_order_item")
public class TestOrderItemDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联测试任务
     */
    private Long orderId;

    /**
     * 广告户ID
     */
    private String platformAdId;

    /**
     * 状态（1=待进行，2=进行中，3=测试通过，4=测试失败）
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}