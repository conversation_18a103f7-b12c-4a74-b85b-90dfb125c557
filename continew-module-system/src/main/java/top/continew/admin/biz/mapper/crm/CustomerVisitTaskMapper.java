package top.continew.admin.biz.mapper.crm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.crm.CustomerVisitTaskDO;
import top.continew.admin.biz.model.entity.crm.LeadDO;
import top.continew.admin.biz.model.query.crm.CustomerVisitTaskQuery;
import top.continew.admin.biz.model.query.crm.LeadQuery;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.starter.extension.datapermission.annotation.DataPermission;

/**
* 客户回访任务 Mapper
*
* <AUTHOR>
* @since 2025/06/05 14:54
*/
public interface CustomerVisitTaskMapper extends BaseMapper<CustomerVisitTaskDO> {
    @DataPermission(userId = "l.assignee_id", deptId = "ur.dept_id")
    IPage<CustomerVisitTaskDO> selectTaskPage(IPage<CustomerVisitTaskDO> page, @Param("query") CustomerVisitTaskQuery query);
}