package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.admin.biz.model.entity.WalletTransferDO;
import top.continew.admin.biz.model.query.WalletTransferQuery;
import top.continew.admin.biz.model.resp.WalletTransferDetailResp;
import top.continew.admin.biz.model.resp.WalletTransferResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.AdProductMapper;
import top.continew.admin.biz.model.entity.AdProductDO;
import top.continew.admin.biz.model.query.AdProductQuery;
import top.continew.admin.biz.model.req.AdProductReq;
import top.continew.admin.biz.model.resp.AdProductDetailResp;
import top.continew.admin.biz.model.resp.AdProductResp;
import top.continew.admin.biz.service.AdProductService;

import java.util.List;

/**
 * 投放产品业务实现
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Service
@RequiredArgsConstructor
public class AdProductServiceImpl extends BaseServiceImpl<AdProductMapper, AdProductDO, AdProductResp, AdProductDetailResp, AdProductQuery, AdProductReq> implements AdProductService {

    @Override
    public PageResp<AdProductResp> page(AdProductQuery query, PageQuery pageQuery) {
        QueryWrapper<AdProductDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, pageQuery);
        IPage<AdProductResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        page.getRecords().forEach(this::fill);
        return PageResp.build(page);
    }

    @Override
    protected <E> List<E> list(AdProductQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<AdProductDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        List<AdProductDetailResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        entityList.forEach(this::fill);
        return BeanUtil.copyToList(entityList, targetClass);
    }
}