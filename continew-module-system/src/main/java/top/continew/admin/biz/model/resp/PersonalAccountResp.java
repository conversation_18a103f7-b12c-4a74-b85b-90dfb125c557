package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.PersonalAccountStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAppealStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutTypeEnum;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 个号信息
 *
 * <AUTHOR>
 * @since 2025/02/27 14:48
 */
@Data
@Schema(description = "个号信息")
public class PersonalAccountResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    private String channelName;

    private Long channelId;

    /**
     * 账号信息
     */
    @Schema(description = "账号信息")
    private String content;

    /**
     * 接入人
     */
    @Schema(description = "接入人")
    private String accessUser;

    /**
     * 是否接入
     */
    @Schema(description = "是否接入")
    private Boolean isAccess;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    private String browserNo;

    @Schema(description = "浏览器ID")
    private String browserId;

    /**
     * 账号状态
     */
    @Schema(description = "账号状态")
    private PersonalAccountStatusEnum accountStatus;

    /**
     * 单价
     */
    @Schema(description = "单价")
    private BigDecimal unitPrice;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    private Boolean isAfterSale;

    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    private String afterSaleReason;

    private PersonalAccoutAppealStatusEnum appealStatus;

    private Boolean isChangePwd;

    /**
     * 类型
     */
    private PersonalAccoutTypeEnum type;

    private LocalDateTime purchaseTime;

    private Integer bmNum;

    private String platformAccountId;

    private String uniqueKey;

    private String proxy;
}