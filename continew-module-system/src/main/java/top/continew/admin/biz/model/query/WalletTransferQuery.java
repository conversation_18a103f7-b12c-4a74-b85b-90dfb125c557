package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 钱包流水查询条件
 *
 * <AUTHOR>
 * @since 2025/07/22 15:59
 */
@Data
@Schema(description = "钱包流水查询条件")
public class WalletTransferQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @Query(type = QueryType.BETWEEN, columns = "t.trans_time")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] transTime;

    /**
     * 来源地址
     */
    @Schema(description = "来源地址")
    @Query(type = QueryType.IN)
    private List<String> fromAddress;

    /**
     * 金额
     */
    @Schema(description = "金额")
    @Query(type = QueryType.EQ, columns = "t.amount")
    private BigDecimal amount;

    /**
     * to_address
     */
    @Schema(description = "to_address")
    @Query(type = QueryType.EQ)
    private String toAddress;
}