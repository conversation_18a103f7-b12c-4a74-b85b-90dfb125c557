package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 客户余额提现订单查询条件
 *
 * <AUTHOR>
 * @since 2025/01/22 14:23
 */
@Data
@Schema(description = "客户余额提现订单查询条件")
public class CustomerWithdrawOrderQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @Query(type = QueryType.EQ)
    private String orderNo;

    /**
     * 订单状态（1:审核中, 2:审核通过, 3:审核拒绝, 4:退款完成, 5:已取消）
     */
    @Schema(description = "订单状态（1:审核中, 2:审核通过, 3:审核拒绝, 4:退款完成, 5:已取消）")
    @Query(type = QueryType.EQ)
    private Integer status;

    /**
     * 关联客户ID
     */
    @Schema(description = "关联客户ID")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 预计退款时间
     */
    @Schema(description = "预计退款时间")
    @Query(type = QueryType.BETWEEN)
    private String[] expectedRefundTime;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN)
    private String[] createTime;



    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Query(type = QueryType.EQ)
    private Long createUser;

    @Schema(description = "客户业务类型")
    @Query(type = QueryType.EQ,columns = {"c.business_type"})
    private Integer businessType;


}