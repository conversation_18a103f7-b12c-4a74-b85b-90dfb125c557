package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.annotation.DictField;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 投放产品实体
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@TableName("biz_ad_product")
@DictField(labelKey = "agentNo")
public class AdProductDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    private Long customerId;

    /**
     * 代理线
     */
    private String agentNo;

    /**
     * 产品类型
     */
    private Integer type;

    private String country;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;
}