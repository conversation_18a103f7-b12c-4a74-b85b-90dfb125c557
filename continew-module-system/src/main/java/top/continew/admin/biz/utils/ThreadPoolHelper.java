/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.poi.ss.formula.functions.T;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolHelper {

    private static volatile ThreadPoolExecutor orderExecutor;

    private static volatile ThreadPoolExecutor adAccountSpentCheckExecutor;

    private static volatile ThreadPoolExecutor AdAccountInfoExecutor;
    
    private static volatile ThreadPoolExecutor gmailProcessorExecutor;

    private ThreadPoolHelper() {

    }

    public static ThreadPoolExecutor getOrderInstance() {
        if (orderExecutor == null) {
            synchronized (ThreadPoolHelper.class) {
                if (orderExecutor == null) {
                    orderExecutor = new ThreadPoolExecutor(500, 1000, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(), new ThreadFactoryBuilder()
                        .setNameFormat("facebook-pool-%d")
                        .build(), new ThreadPoolExecutor.AbortPolicy());
                }
            }
        }
        return orderExecutor;
    }

    public static ThreadPoolExecutor getAdAccountSpentCheckInstance() {
        if (adAccountSpentCheckExecutor == null) {
            synchronized (ThreadPoolHelper.class) {
                if (adAccountSpentCheckExecutor == null) {
                    adAccountSpentCheckExecutor = new ThreadPoolExecutor(20, 20, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(), new ThreadFactoryBuilder()
                        .setNameFormat("ad-spent-check-pool-%d")
                        .build(), new ThreadPoolExecutor.AbortPolicy());
                }
            }
        }
        return adAccountSpentCheckExecutor;
    }

    public static ThreadPoolExecutor getAdAccountInfoInstance() {
        if (AdAccountInfoExecutor == null) {
            synchronized (ThreadPoolHelper.class) {
                if (AdAccountInfoExecutor == null) {
                    AdAccountInfoExecutor = new ThreadPoolExecutor(5, 200, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(), new ThreadFactoryBuilder()
                        .setNameFormat("ad-account-info-pool-%d")
                        .build(), new ThreadPoolExecutor.AbortPolicy());
                }
            }
        }
        return AdAccountInfoExecutor;
    }
    
    /**
     * 获取Gmail邮件处理线程池实例
     * 核心线程数：5，最大线程数：10，适合IO密集型任务
     */
    public static ThreadPoolExecutor getGmailProcessorInstance() {
        if (gmailProcessorExecutor == null) {
            synchronized (ThreadPoolHelper.class) {
                if (gmailProcessorExecutor == null) {
                    gmailProcessorExecutor = new ThreadPoolExecutor(
                        5,  // 核心线程数
                        10, // 最大线程数
                        2,  // 空闲线程存活时间
                        TimeUnit.MINUTES, 
                        new LinkedBlockingQueue<>(100), // 队列容量
                        new ThreadFactoryBuilder()
                            .setNameFormat("gmail-processor-pool-%d")
                            .build(), 
                        new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者运行
                    );
                }
            }
        }
        return gmailProcessorExecutor;
    }
}
