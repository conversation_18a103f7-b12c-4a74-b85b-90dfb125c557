package top.continew.admin.biz.model.query.crm;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 来源查询条件
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@Schema(description = "来源查询条件")
public class SourceQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Query(type = QueryType.EQ)
    private Long groupId;
}