package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.ScheduleTypeEnum;
import top.continew.admin.biz.mapper.SalesScheduleMapper;
import top.continew.admin.biz.model.entity.SalesScheduleDO;
import top.continew.admin.biz.model.query.SalesScheduleQuery;
import top.continew.admin.biz.model.req.SalesScheduleReq;
import top.continew.admin.biz.model.resp.SalesScheduleDetailResp;
import top.continew.admin.biz.model.resp.SalesScheduleResp;
import top.continew.admin.biz.service.SalesScheduleService;
import top.continew.admin.biz.utils.WorkUtils;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商务人员排班/请假记录业务实现
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Service
@RequiredArgsConstructor
public class SalesScheduleServiceImpl extends BaseServiceImpl<SalesScheduleMapper, SalesScheduleDO, SalesScheduleResp, SalesScheduleDetailResp, SalesScheduleQuery, SalesScheduleReq> implements SalesScheduleService {

    @Override
    protected void beforeAdd(SalesScheduleReq req) {
        if (exists(new LambdaQueryWrapper<SalesScheduleDO>()
                .eq(SalesScheduleDO::getSalesId, req.getSalesId())
                .eq(SalesScheduleDO::getScheduleDate, req.getScheduleDate())
                .eq(SalesScheduleDO::getScheduleType, req.getScheduleType()))) {
            throw new BusinessException("已存在记录");
        }
    }

    @Override
    public List<UserDO> getUserListByTelegramId(List<String> telegramIds) {
        return baseMapper.getUserListByTelegramId(telegramIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void telegramSaveBatch(List<SalesScheduleDO> scheduleList) {
        if (CollUtil.isEmpty(scheduleList)) {
            return;
        }

        // 批量查询现有记录，避免N+1查询问题
        List<SalesScheduleDO> existingRecords = lambdaQuery()
                .in(SalesScheduleDO::getSalesId, scheduleList.stream().map(SalesScheduleDO::getSalesId).distinct().collect(Collectors.toList()))
                .in(SalesScheduleDO::getScheduleDate, scheduleList.stream().map(SalesScheduleDO::getScheduleDate).distinct().collect(Collectors.toList()))
                .in(SalesScheduleDO::getScheduleType, scheduleList.stream().map(SalesScheduleDO::getScheduleType).distinct().collect(Collectors.toList()))
                .list();

        // 创建映射表，用于快速查找现有记录
        Map<String, SalesScheduleDO> existingMap = existingRecords.stream()
                .collect(Collectors.toMap(
                        record -> generateKey(record.getSalesId(), record.getScheduleDate(), record.getScheduleType()),
                        record -> record
                ));

        // 处理每个要保存的记录
        for (SalesScheduleDO schedule : scheduleList) {
            String key = generateKey(schedule.getSalesId(), schedule.getScheduleDate(), schedule.getScheduleType());
            SalesScheduleDO existingRecord = existingMap.get(key);

            if (existingRecord != null) {
                // 存在旧记录，设置ID进行更新
                schedule.setId(existingRecord.getId());
            }
            // 如果不存在，则作为新记录插入（ID为null）
        }

        // 批量保存或更新
        saveOrUpdateBatch(scheduleList);
    }

    @Override
    public void telegramSave(SalesScheduleDO salesSchedule) {
        SalesScheduleDO schedule = lambdaQuery().eq(SalesScheduleDO::getSalesId, salesSchedule.getSalesId())
                .eq(SalesScheduleDO::getScheduleDate, salesSchedule.getScheduleDate())
                .eq(SalesScheduleDO::getScheduleType, salesSchedule.getScheduleType())
                .one();
        if (schedule != null) {
            // 存在旧记录，设置ID进行更新
            salesSchedule.setId(schedule.getId());
        }
        saveOrUpdate(salesSchedule);
    }

    @Override
    public Integer getWorkingDays(Long userId, YearMonth month) {
        int totalWorkingDays;
        if (month.equals(YearMonth.now())) {
            totalWorkingDays = WorkUtils.getWorkingDaysInMonthUntilToday(month);
        } else {
            totalWorkingDays = WorkUtils.getWorkingDaysInMonth(month);
        }
        // 找出当前用户的请假天数
        Long leaveDays = lambdaQuery().eq(SalesScheduleDO::getSalesId, userId)
                .eq(SalesScheduleDO::getScheduleType, ScheduleTypeEnum.LEAVE)
                .between(SalesScheduleDO::getScheduleDate, month.atDay(1), month.atEndOfMonth())
                .count();
        // 找出当前用户排班的天数
        Long scheduleDays = lambdaQuery().eq(SalesScheduleDO::getSalesId, userId)
                .eq(SalesScheduleDO::getScheduleType, ScheduleTypeEnum.SCHEDULE)
                .between(SalesScheduleDO::getScheduleDate, month.atDay(1), month.atEndOfMonth())
                .count();
        // 当天用户总的工作天数是 总工作日 - 请假天数 + 排班天数
        return totalWorkingDays - leaveDays.intValue() + scheduleDays.intValue();
    }

    /**
     * 生成唯一键，用于标识排班记录
     */
    private String generateKey(Long salesId, java.time.LocalDate scheduleDate, top.continew.admin.biz.enums.ScheduleTypeEnum scheduleType) {
        return salesId + "_" + scheduleDate + "_" + scheduleType.getValue();
    }
}