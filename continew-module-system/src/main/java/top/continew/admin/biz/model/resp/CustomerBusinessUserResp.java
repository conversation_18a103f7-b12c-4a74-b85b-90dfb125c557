package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;
import top.continew.admin.common.constant.ContainerConstants;
import top.continew.starter.extension.crud.constant.ContainerPool;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 客户商务对接信息
 *
 * <AUTHOR>
 * @since 2025/08/05 15:35
 */
@Data
@Schema(description = "客户商务对接信息")
public class CustomerBusinessUserResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID，用于关联客户信息表
     */
    @Schema(description = "客户ID，用于关联客户信息表")
    private Long customerId;

    /**
     * 关联的商务用户ID
     */
    @Schema(description = "关联的商务用户ID")
    @Assemble(container = ContainerConstants.USER_NICKNAME, props = @Mapping(ref = "salesName"))
    private Long userId;

    @ExcelProperty(value = "商务人员")
    private String salesName;

    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

}