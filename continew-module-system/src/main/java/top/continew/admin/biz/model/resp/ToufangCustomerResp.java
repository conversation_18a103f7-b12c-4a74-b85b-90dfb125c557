package top.continew.admin.biz.model.resp;

import lombok.Data;
import top.continew.admin.biz.enums.CustomerStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ToufangCustomerResp {

    private String id;

    private String name;

    private String country;

    private BigDecimal totalTransfer;

    private BigDecimal feeRate;

    private BigDecimal spend;

    private BigDecimal fee;

    private BigDecimal totalSpend;

    private BigDecimal Balance;

    private BigDecimal toufangBalance;

    private CustomerStatusEnum status;

    private LocalDateTime createTime;


}
