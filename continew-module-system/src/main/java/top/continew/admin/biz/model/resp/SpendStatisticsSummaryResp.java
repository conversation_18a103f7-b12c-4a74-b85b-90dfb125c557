package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "消耗统计汇总响应")
public class SpendStatisticsSummaryResp {
    
    @Schema(description = "总下户数量")
    private Integer totalOrderCount;
    
    @Schema(description = "总正常数量")
    private Integer totalNormalCount;

    private Integer recycleOrderCount;

    private Integer saleOrderCount;
    
    @Schema(description = "总下户成本")
    private BigDecimal totalOrderCost;
}