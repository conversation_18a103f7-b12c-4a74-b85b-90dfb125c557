package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.ScheduleTypeEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商务人员排班/请假记录实体
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Data
@TableName("biz_sales_schedule")
public class SalesScheduleDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商务人员ID
     */
    private Long salesId;

    /**
     * 排班/请假的具体日期
     */
    private LocalDate scheduleDate;

    /**
     * 类型: 1=请假, 2=排班
     */
    private ScheduleTypeEnum scheduleType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}