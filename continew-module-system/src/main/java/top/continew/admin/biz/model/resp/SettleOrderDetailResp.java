package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.common.base.BaseDetailResp;

/**
 * 结算订单详情信息
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "结算订单详情信息")
public class SettleOrderDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @AssembleMethod(props = @Mapping(src = "name", ref = "customerName"), targetType = CustomerService.class, method = @ContainerMethod(bindMethod = "get", resultType = CustomerResp.class))
    private Long customerId;

    @ExcelProperty(value = "客户")
    private String customerName;

    /**
     * 总消耗
     */
    @Schema(description = "总消耗")
    @ExcelProperty(value = "总消耗")
    private BigDecimal totalSpent;

    /**
     * 结算消耗
     */
    @Schema(description = "结算消耗")
    @ExcelProperty(value = "结算消耗")
    private BigDecimal settleSpent;

    /**
     * 结算时间
     */
    @Schema(description = "结算时间")
    @ExcelProperty(value = "结算时间")
    private LocalDateTime settleTime;
}