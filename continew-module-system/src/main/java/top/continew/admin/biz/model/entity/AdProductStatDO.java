package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 产品日报实体
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@TableName("biz_ad_product_stat")
public class AdProductStatDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    private Long customerId;

    /**
     * 产品
     */
    private Long productId;

    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 实际花费
     */
    private BigDecimal actualSpend;

    /**
     * 花费
     */
    private BigDecimal spend;

    /**
     * 服务费
     */
    private BigDecimal fee;

    /**
     * 额外数据
     */
    private String extraData;

    private String effectData;

    private BigDecimal feeRate;

    private BigDecimal reflowSpend;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;


    private BigDecimal bearCost;
}