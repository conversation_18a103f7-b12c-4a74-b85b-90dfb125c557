/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.*;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 广告账号详情信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:50
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "广告账号详情信息")
public class AdAccountDetailResp {

    /**
     * 关联BM5 ID
     */
    @Schema(description = "关联BM5")
    @ExcelProperty(value = "BM ID")
    private String bmId;

    private Long bmItemType;

    @ExcelProperty(value = "BM类型")
    private String typeName;

    /**
     * 平台广告ID
     */
    @Schema(description = "广告户ID")
    @ExcelProperty(value = "广告户ID")
    private String platformAdId;

    /**
     * 时区
     */
    @Schema(description = "时区")
    @ExcelProperty(value = "时区")
    private String timezone;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    @ExcelProperty(value = "浏览器编号")
    private String browserNo;
    /**
     * 账号状态
     */
    @Schema(description = "账号状态")
    @ExcelProperty(value = "账号状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountStatusEnum accountStatus;

    @ExcelProperty(value = "封禁时间")
    private LocalDateTime banTime;

    /**
     * 养号状态
     */
    @Schema(description = "养号状态")
    @ExcelProperty(value = "养号状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountKeepStatusEnum keepStatus;

    @ExcelProperty(value = "BM授权时间")
    private LocalDateTime bmAuthTime;

    /**
     * 出售状态
     */
    @Schema(description = "出售状态")
    @ExcelProperty(value = "出售状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountSaleStatusEnum saleStatus;

    @ExcelProperty(value = "客户名称")
    private String customerName;

    @ExcelProperty(value = "出售时间")
    private LocalDateTime saleTime;

    /**
     * 清零状态
     */
    @Schema(description = "清零状态")
    @ExcelProperty(value = "清零状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountClearStatusEnum clearStatus;

    @ExcelProperty(value = "花费限额")
    private BigDecimal spendCap;
    @ExcelProperty(value = "总消耗")
    private BigDecimal totalSpent;
    @ExcelProperty(value = "当前消耗")
    private BigDecimal amountSpent;
    @ExcelProperty(value = "剩余应付")
    private BigDecimal balance;

    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "备户成本")
    private BigDecimal cost;

    private String parentBrowserNo;

    private Boolean isRemoveAdmin;

    private Boolean isLowLimit;

    private Integer voStatus;
    @ExcelProperty(value = "标签")
    private String tags;

    @ExcelProperty(value = "是否预充户")
    private Boolean isPrepay;

    /**
     * 是否可用
     */
    @ExcelProperty(value = "是否可用")
    private Boolean usable;

    /**
     * 不可用原因
     */
    @ExcelProperty(value = "不可用原因", converter = ExcelBaseEnumConverter.class)
    private AdAccountUnusableReasonEnum unusableReason;

    @ExcelProperty(value = "封禁原因")
    private String disabledReason;
}