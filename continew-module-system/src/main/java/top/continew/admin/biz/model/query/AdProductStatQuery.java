package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 产品日报查询条件
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@Schema(description = "产品日报查询条件")
public class AdProductStatQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 产品
     */
    @Schema(description = "产品")
    @Query(type = QueryType.EQ)
    private Long productId;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @Query(type = QueryType.BETWEEN)
    private LocalDate[] statDate;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    @Query(type = QueryType.EQ)
    private String platformAdId;
}