package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 投放日报明细实体
 *
 * <AUTHOR>
 * @since 2025/08/13 21:02
 */
@Data
@TableName("biz_ad_product_stat_item")
public class AdProductStatItemDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计数据ID
     */
    private Long statId;

    /**
     * 广告户
     */
    private String platformAdId;

    /**
     * 消耗
     */
    private BigDecimal spend;

    /**
     * 回流
     */
    private BigDecimal reflowSpend;

    /**
     * 成效数据
     */
    private String effectData;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;
}