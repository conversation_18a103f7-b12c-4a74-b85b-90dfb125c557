package top.continew.admin.biz.model.resp;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import top.continew.admin.biz.utils.CommonUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ExcelIgnoreUnannotated
public class CustomerProfitStatResp {

    private Long customerId;

    @ExcelProperty("客户")
    private String name;

    @ExcelProperty("合作日期")
    private LocalDate createTime;

    private Integer cooperateDay;

    @ExcelProperty("合作周期")
    private String cooperatePeriod;

    @ExcelProperty("退款日期")
    private LocalDate refundTime;

    @ExcelProperty("合作政策")
    private String cooperatePolicy;

    @ExcelProperty("服务费")
    private BigDecimal serviceFee;

    @ExcelProperty("户费")
    private BigDecimal openAccountFee;

    @ExcelProperty("总下户数")
    private Integer totalAccount;

    @ExcelProperty("自费户数")
    private BigDecimal selfCostAccount;

    @ExcelProperty("自费成本")
    private BigDecimal selfCost;

    @ExcelProperty("客户成本")
    private BigDecimal customerCost;

    @ExcelProperty("总成本")
    private BigDecimal totalCost;

    @ExcelProperty("中介返利")
    private BigDecimal rebateAmount;

    @ExcelProperty("服务费退款")
    private BigDecimal refundServiceFee;

    @ExcelProperty("毛利")
    private BigDecimal profit;

    public String getCooperatePeriod() {
        return CommonUtils.convertDaysToYMD(this.getCooperateDay());
    }
}
