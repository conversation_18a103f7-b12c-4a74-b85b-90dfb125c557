package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CustomerAnalyzeQuery {

    private Integer type;


    private List<String> cardHeader;


    private List<String> timeZone;


    private Boolean oneKnifeFlow;

    private Boolean paymentIssue;

    private Long customerId;

    private String adAccountIds;

    @Schema(description = "【低消耗定义】广告开启的天数。此条件必须与 lowConsumptionAmount 同时提供才生效。",
            example = "1",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer lowConsumptionDay;
    @Schema(description = "【低消耗定义】消耗金额上限（美元）。此条件必须与 lowConsumptionHours 同时提供才生效。",
            example = "10.00",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal lowConsumptionAmount;

    @Schema(description = "查询日期范围的开始时间。格式：yyyy-MM-dd HH:mm:ss",
            example = "2024-01-01 00:00:00",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @Schema(description = "查询日期范围的结束时间。格式：yyyy-MM-dd HH:mm:ss",
            example = "2024-12-31 23:59:59",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;



}
