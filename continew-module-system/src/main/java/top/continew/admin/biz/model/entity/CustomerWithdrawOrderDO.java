package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.CustomerWithdrawOrderStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 客户余额提现订单实体
 *
 * <AUTHOR>
 * @since 2025/01/22 14:23
 */
@Data
@TableName("biz_customer_withdraw_order")
public class CustomerWithdrawOrderDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单状态（1:审核中, 2:审核通过, 3:审核拒绝, 4:退款完成, 5:已取消）
     */
    private CustomerWithdrawOrderStatusEnum status;

    /**
     * 关联客户ID
     */
    private Long customerId;

    /**
     * 预计退款时间
     */
    private LocalDateTime expectedRefundTime;

    /**
     * 备注
     */
    private String remark;

    private String auditRemark;

    /**
     * 审核人
     */
    private Long auditor;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 实际退款时间
     */
    private LocalDateTime actualRefundTime;

    /**
     * 实际退款金额
     */
    private BigDecimal actualRefundAmount;

    /**
     * 关联商务用户ID
     */
    private Long businessUserId;

}