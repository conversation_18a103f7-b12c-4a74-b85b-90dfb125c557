/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountBalanceTypeEnum;
import top.continew.admin.biz.enums.TransactionActionEnum;
import top.continew.admin.common.base.BaseResp;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 广告户余额变更记录信息
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
@Data
@Schema(description = "广告户余额变更记录信息")
public class AdAccountBalanceRecordResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    private String platformAdId;

    /**
     * 交易动作
     */
    @Schema(description = "交易动作")
    @ExcelProperty(value = "交易动作", converter = ExcelBaseEnumConverter.class)
    private TransactionActionEnum action;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @ExcelProperty(value = "交易类型", converter = ExcelBaseEnumConverter.class)
    private AdAccountBalanceTypeEnum type;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    private BigDecimal amount;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    private LocalDateTime transTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}