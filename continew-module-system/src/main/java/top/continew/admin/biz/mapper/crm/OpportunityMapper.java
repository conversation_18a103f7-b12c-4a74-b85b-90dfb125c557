package top.continew.admin.biz.mapper.crm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.crm.OpportunityDO;
import top.continew.admin.biz.model.query.crm.OpportunityQuery;
import top.continew.admin.biz.model.resp.crm.OpportunityResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.starter.extension.datapermission.annotation.DataPermission;

import java.time.LocalDateTime;

/**
* 商机 Mapper
*
* <AUTHOR>
* @since 2025/05/16 17:48
*/
public interface OpportunityMapper extends BaseMapper<OpportunityDO> {
    @DataPermission(userId = "o.handler_user_id", deptId = "ur.dept_id")
    Page<OpportunityResp> selectOpportunityPage(Page<OpportunityResp> page, @Param("query") OpportunityQuery query);

    /**
     * 统计用户待跟进商机数量
     *
     * @param handlerUserId 对接人ID
     * @return 待跟进数量
     */
    Long countPendingOpportunities(@Param("handlerUserId") Long handlerUserId);

    /**
     * 统计用户今日需跟进商机数量
     *
     * @param handlerUserId 对接人ID
     * @param todayStart 今日开始时间
     * @param todayEnd 今日结束时间
     * @param waitOverHour 待跟进超时时长(小时)
     * @param pendingOverHour 跟进中超时时长(小时)
     * @return 今日需跟进数量
     */
    Long countTodayFollowOpportunities(@Param("handlerUserId") Long handlerUserId,
                                       @Param("todayStart") LocalDateTime todayStart,
                                       @Param("todayEnd") LocalDateTime todayEnd,
                                       @Param("waitOverHour") Integer waitOverHour,
                                       @Param("pendingOverHour") Integer pendingOverHour);

    /**
     * 统计用户超时商机数量
     *
     * @param handlerUserId 对接人ID
     * @param currentTime 当前时间
     * @param waitOverHour 待跟进超时时长(小时)
     * @param pendingOverHour 跟进中超时时长(小时)
     * @return 超时数量
     */
    Long countOverdueOpportunities(@Param("handlerUserId") Long handlerUserId,
                                   @Param("currentTime") LocalDateTime currentTime,
                                   @Param("waitOverHour") Integer waitOverHour,
                                   @Param("pendingOverHour") Integer pendingOverHour);

    void updateLongTermFollowUp(LocalDateTime remindTime);
}