package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 结算订单查询条件
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Data
@Schema(description = "结算订单查询条件")
public class SettleOrderQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 结算时间
     */
    @Schema(description = "结算时间")
    @Query(type = QueryType.BETWEEN)
    private LocalDateTime[] settleTime;

    @Schema(description = "客户业务类型")
    @Query(type = QueryType.EQ,columns = {"c.business_type"})
    private Integer businessType;
}