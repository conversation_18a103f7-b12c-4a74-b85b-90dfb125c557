/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.util.List;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 广告户每日消耗查询条件
 *
 * <AUTHOR>
 * @since 2025/01/14 14:43
 */
@Data
@Schema(description = "广告户每日消耗查询条件")
public class AdAccountInsightQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    @Query(type = QueryType.EQ)
    private List<String> adAccountIds;

    private AdAccountStatusEnum accountStatus;

    private AdAccountOrderStatusEnum orderStatus;

    /**
     * 统计时间
     */
    @Schema(description = "统计时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] transTime;

    private Long customerId;

    private String sortField;

    private Boolean ascSortFlag;
}