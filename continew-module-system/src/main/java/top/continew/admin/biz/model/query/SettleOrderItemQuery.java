package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 结算订单详情查询条件
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Data
@Schema(description = "结算订单详情查询条件")
public class SettleOrderItemQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联订单
     */
    @Schema(description = "关联订单")
    @Query(type = QueryType.EQ)
    private Long orderId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @Query(type = QueryType.EQ)
    private String platformAdId;
}