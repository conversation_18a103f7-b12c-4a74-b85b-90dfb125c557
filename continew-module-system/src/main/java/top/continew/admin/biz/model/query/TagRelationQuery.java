package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 标签关联查询条件
 *
 * <AUTHOR>
 * @since 2025/05/08 16:37
 */
@Data
@Schema(description = "标签关联查询条件")
public class TagRelationQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @Schema(description = "标签ID")
    @Query(type = QueryType.EQ)
    private Long tagId;

    /**
     * 类型(1=广告户 2=下户订单)
     */
    @Schema(description = "类型(1=广告户 2=下户订单)")
    @Query(type = QueryType.EQ)
    private Integer type;

    /**
     * 关联ID
     */
    @Schema(description = "关联ID")
    @Query(type = QueryType.EQ)
    private Long relationId;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    @Query(type = QueryType.EQ)
    private LocalDateTime createTime;
}