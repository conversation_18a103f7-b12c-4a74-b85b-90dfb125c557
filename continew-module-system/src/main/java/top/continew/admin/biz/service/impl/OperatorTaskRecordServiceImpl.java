package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.query.OperationsRecordStatQuery;
import top.continew.admin.biz.model.query.OperationsStatQuery;
import top.continew.admin.biz.model.resp.crm.OperatorTaskSummaryStatResp;
import top.continew.admin.biz.model.resp.operationTaskStatResp;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.system.model.entity.DictDO;
import top.continew.admin.system.model.entity.DictItemDO;
import top.continew.admin.system.service.DictItemService;
import top.continew.admin.system.service.DictService;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.OperatorTaskRecordMapper;
import top.continew.admin.biz.model.entity.OperatorTaskRecordDO;
import top.continew.admin.biz.model.query.OperatorTaskRecordQuery;
import top.continew.admin.biz.model.req.OperatorTaskRecordReq;
import top.continew.admin.biz.model.resp.OperatorTaskRecordDetailResp;
import top.continew.admin.biz.model.resp.OperatorTaskRecordResp;
import top.continew.admin.biz.service.OperatorTaskRecordService;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 运营人员工作记录业务实现
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
@Service
@RequiredArgsConstructor
public class OperatorTaskRecordServiceImpl extends BaseServiceImpl<OperatorTaskRecordMapper, OperatorTaskRecordDO, OperatorTaskRecordResp, OperatorTaskRecordDetailResp, OperatorTaskRecordQuery, OperatorTaskRecordReq> implements OperatorTaskRecordService {

    private final AdAccountOrderService adAccountOrderService;

    private final DictItemService dictItemService;

    private final DictService dictService;

    @Override
    protected void beforeAdd(OperatorTaskRecordReq req) {
        if (StringUtils.isNotBlank(req.getPlatformAdId())) {
            AdAccountOrderDO adAccountOrder = adAccountOrderService.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
                .eq(AdAccountOrderDO::getAdAccountId, req.getPlatformAdId())
                .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
            if (adAccountOrder != null) {
                req.setCustomerId(adAccountOrder.getCustomerId());
            }
        }
    }

    @Override
    public List<OperatorTaskSummaryStatResp> summaryStat(OperatorTaskRecordQuery query) {
        QueryWrapper<OperatorTaskRecordDO> operatorTaskRecordDOQueryWrapper = this.buildQueryWrapper(query);
        return this.baseMapper.summaryStat(operatorTaskRecordDOQueryWrapper);
    }

    @Override
    public void export(OperatorTaskRecordQuery query, SortQuery sortQuery, HttpServletResponse response) {
        //获取全部的key
        DictDO dict = dictService.getOne(Wrappers.<DictDO>lambdaQuery().eq(DictDO::getCode, "operator_task_type"));

        //获取全部的value
        List<DictItemDO> itemList = dictItemService.list(Wrappers.<DictItemDO>lambdaQuery().eq(DictItemDO::getDictId, dict.getId()));

        List<OperatorTaskRecordDetailResp> list = this.<OperatorTaskRecordDetailResp>list(query, sortQuery, OperatorTaskRecordDetailResp.class);
        list.forEach(item -> {
            itemList.forEach(dictItem -> {
                if (dictItem.getValue().equals(item.getType())) {
                    item.setType(dictItem.getLabel());
                }
            });
        });
        list.forEach(this::fill);
        ExcelUtils.export(list, "导出数据", OperatorTaskRecordDetailResp.class, response);

    }

    /**
     *
     * @param page
     * @param query
     * @return
     */
    @Override
    public IPage<operationTaskStatResp> selectOperationTaskStat(Page<operationTaskStatResp> page, OperationsRecordStatQuery query) {
        IPage<operationTaskStatResp> pageList = this.baseMapper.selectOperationTaskStat(page,query);
        pageList.getRecords().forEach(item -> {
            OperatorTaskRecordQuery recordQuery = BeanUtil.copyProperties(query, OperatorTaskRecordQuery.class);

            recordQuery.setCreateUser(item.getUserId());
            List<OperatorTaskSummaryStatResp> resp = this.summaryStat(recordQuery);
            item.setDateStatList(resp);
        });
        return pageList;
    }
}