/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.AdAccountClearStatusEnum;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.CustomerStatusEnum;
import top.continew.admin.biz.enums.RechargeOrderStatusEnum;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.RechargeOrderDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotRechargeReq;
import top.continew.admin.biz.robot.strategy.BaseRobotCommandService;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.RechargeOrderService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.core.validation.CheckUtils;

import java.math.BigDecimal;

@Service
@Slf4j
@RequiredArgsConstructor
public class RobotRechargeStrategyImpl extends BaseRobotCommandService implements RobotCommandStrategy {

    private final RechargeOrderService rechargeOrderService;

    private final AdAccountService adAccountService;

    private final AdAccountOrderService adAccountOrderService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.RECHARGE;
    }

    @Override
    public String execute(Update update) {
        if (!hasPermission(getCommand(), update.getMessage().getChatId())) {
            return "";
        }
        RobotRechargeReq rechargeReq = BotUtils.parseBotMsgToObject(update.getMessage()
            .getText(), "\n", ":", RobotRechargeReq.class);
        log.info("{}收到一条充值请求：{}", getCommand().getDescription(), JSONObject.toJSONString(rechargeReq));
        CheckUtils.throwIfNull(rechargeReq.getAmount().compareTo(BigDecimal.ZERO) <= 0, "充值金额必须大于0");
        CustomerDO customer = getCustomer(rechargeReq.getCustomerName(), update.getMessage().getChatId());
        CheckUtils.throwIf(customer.getStatus().equals(CustomerStatusEnum.TERMINATED), "请重新启用客户合作状态");
        AdAccountDO adAccount = adAccountService.getOne(Wrappers.<AdAccountDO>lambdaQuery()
            .eq(AdAccountDO::getPlatformAdId, rechargeReq.getPlatformAdId()));
        CheckUtils.throwIfNull(adAccount, "未找到相关广告户");
        CheckUtils.throwIf(!adAccount.getUsable(), "广告户不可用，请核对后充值");
        AdAccountOrderDO adAccountOrder = adAccountOrderService.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .eq(AdAccountOrderDO::getCustomerId, customer.getId())
            .eq(AdAccountOrderDO::getAdAccountId, rechargeReq.getPlatformAdId())
            .in(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.PROCESS));
        CheckUtils.throwIfNull(adAccountOrder, "下户记录不存在");
        CheckUtils.throwIf(adAccountOrder.getClearStatus()
            .equals(AdAccountClearStatusEnum.CLEARED), "广告户已清零，请前往下户订单取消清零再充值");
        // 一个广告户只允许一条进行中的充值订单
        RechargeOrderDO existOrder = rechargeOrderService.getUnFinishOrder(rechargeReq.getPlatformAdId());
        CheckUtils.throwIfNotNull(existOrder, "请先处理之前的充值订单");
        RechargeOrderDO order = new RechargeOrderDO();
        order.setOrderNo(CommonUtils.randomOrderNo("CZ"));
        order.setCustomerId(customer.getId());
        order.setPlatformAdId(rechargeReq.getPlatformAdId());
        order.setAmount(rechargeReq.getAmount());
        order.setStatus(RechargeOrderStatusEnum.WAIT);
        order.setApplyMessageId(update.getMessage().getMessageId());
        rechargeOrderService.save(order);
        log.info("{}订单入库成功", getCommand().getDescription());
        return "订单%s提交成功，等待客服人员处理".formatted(order.getOrderNo());
    }
}
