package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;
import java.util.List;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import cn.crane4j.annotation.condition.ConditionOnPropertyNotNull;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.CustomerRequirementStatusEnum;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.common.base.BaseResp;
import top.continew.starter.extension.crud.constant.ContainerPool;

/**
 * 客户需求信息
 *
 * <AUTHOR>
 * @since 2025/02/20 14:03
 */
@Data
@Schema(description = "客户需求信息")
public class CustomerRequirementResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @AssembleMethod(props = @Mapping(src = "name", ref = "customerName"), targetType = CustomerService.class, method = @ContainerMethod(bindMethod = "get", resultType = CustomerResp.class))
    private Long customerId;

    /**
     * 时区
     */
    @Schema(description = "时区")
    private String timezone;

    private String customerName;

    /**
     * 需求时间， 时间格式化为日期
     */
    @Schema(description = "需求时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime requirementTime;

    /**
     * 需求数量
     */
    @Schema(description = "需求数量")
    private Integer quantity;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    private Long handleUser;

    private String handleUserString;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 接单时间
     */
    private LocalDateTime acceptTime;

    /**
     * 状态:待接单，处理中，已完成，取消
     */
    private CustomerRequirementStatusEnum status;

    @Schema(description = "完成数量")
    private Integer finishQuantity;

    private String cancelReason;


    /**
     * bm类型
     */
    private Long bmType;

    private String typeName;

    /**
     * 客户BM ID
     */
    private String customerBmId;

    /**
     * 开户费
     */
    private BigDecimal payAmount;

    /**
     * 广告户名称
     */
    private String adAccountName;


    /**
     * 下户订单编号
     */
    private List<String> orderNos;

    private String customerEmail;

    private BigDecimal customerBalance;
}