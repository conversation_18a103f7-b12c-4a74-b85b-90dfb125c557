package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.starter.extension.crud.annotation.DictField;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 交易对象实体
 *
 * <AUTHOR>
 * @since 2025/08/17 14:22
 */
@Data
@TableName("biz_transaction_user")
@DictField
public class TransactionUserDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易类型
     */
    private TransactionUserTypeEnum transactionType;

    /**
     * 关联ID
     */
    private Long referId;

    /**
     * 名字
     */
    private String name;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;

}