package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 投放产品查询条件
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@Schema(description = "投放产品查询条件")
public class AdProductQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 代理线
     */
    @Schema(description = "代理线")
    @Query(type = QueryType.EQ)
    private String agentNo;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    @Query(type = QueryType.EQ)
    private Integer type;
}