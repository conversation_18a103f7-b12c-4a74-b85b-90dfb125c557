package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.math.BigDecimal;
import java.util.List;
import cn.hutool.core.date.DatePattern;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Data
@Schema(description = "客户统计查询")
public class CustomerStatisticsQuery {

    @Schema(description = "客户")
    private Long customerId;

    @Schema(description = "关联商务")
    private Long businessUserId;

    @Schema(description = "统计时间范围")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] statTimes;


    @Schema(description = "下户时间范围")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] orderTimes;

    private String timezone;

    private @Min(
        value = 1L,
        message = "页码最小值为 {value}"
    ) Integer page = 1;
    @Schema(
        description = "每页条数",
        example = "10"
    )
    private @Range(
        min = 1L,
        max = 1000L,
        message = "每页条数（取值范围 {min}-{max}）"
    ) Integer size = 10;

    private String[] sort;
}
