package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.CustomerRequirementStatusEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 客户需求查询条件
 *
 * <AUTHOR>
 * @since 2025/02/20 14:03
 */
@Data
@Schema(description = "客户需求查询条件")
public class CustomerRequirementQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 时区
     */
    @Schema(description = "时区")
    @Query(type = QueryType.EQ)
    private String timezone;

    /**
     * 需求时间
     */
    @Schema(description = "需求时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] requirementTime;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;


    /**
     * 备注
     */
    @Schema(description = "备注")
    @Query(type = QueryType.LIKE)
    private String remark;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Query(type = QueryType.EQ)
    private Long createUser;

    @Schema(description = "处理人")
    @Query(type = QueryType.EQ)
    private Long handleUser;

    @Query(type = QueryType.EQ, columns = "status")
    private CustomerRequirementStatusEnum status;
}