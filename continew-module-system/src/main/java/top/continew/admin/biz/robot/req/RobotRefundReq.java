/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.req;

import lombok.Getter;
import lombok.Setter;
import top.continew.admin.biz.robot.annotation.TelegramBotFormItem;

import java.math.BigDecimal;

@Getter
@Setter
public class RobotRefundReq {

    @TelegramBotFormItem(label = "客户名称", ignored = true)
    private String customerName;

    @TelegramBotFormItem(label = "广告户ID", required = true)
    private String platformAdId;

    @TelegramBotFormItem(label = "减款金额", required = true)
    private BigDecimal amount;
}
