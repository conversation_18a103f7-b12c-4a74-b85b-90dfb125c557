/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.FbAdSetsDO;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Facebook 广告组 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/16 10:00
 */
public interface FbAdSetsMapper extends BaseMapper<FbAdSetsDO> {

    /**
     * 根据广告户ID查询广告组列表
     *
     * @param adAccountId 广告户ID
     * @return 广告组列表
     */
    List<FbAdSetsDO> selectByAdAccountId(@Param("adAccountId") String adAccountId);

    /**
     * 根据系列ID查询广告组列表
     *
     * @param campaignId 系列ID
     * @return 广告组列表
     */
    List<FbAdSetsDO> selectByCampaignId(@Param("campaignId") String campaignId);

    /**
     * 根据平台ID查询广告组
     *
     * @param platformId 平台ID
     * @return 广告组
     */
    FbAdSetsDO selectByPlatformId(@Param("platformId") String platformId);

    /**
     * 根据状态查询广告组列表
     *
     * @param status 状态
     * @return 广告组列表
     */
    List<FbAdSetsDO> selectByStatus(@Param("status") String status);

    /**
     * 批量插入或更新广告组
     * 如果platform_id已存在则更新，否则插入
     *
     * @param adSets 广告组列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("adSets") List<FbAdSetsDO> adSets);

    /**
     * 批量插入广告组（忽略重复）
     * 如果platform_id已存在则忽略
     *
     * @param adSets 广告组列表
     * @return 影响行数
     */
    int batchInsertIgnore(@Param("adSets") List<FbAdSetsDO> adSets);

    /**
     * 根据广告账户ID和更新时间删除不匹配的广告组
     *
     * @param adAccountId 广告账户ID
     * @param updateTime  更新时间
     * @return 删除的行数
     */
    int deleteByAdAccountIdAndUpdateTimeNot(@Param("adAccountId") String adAccountId, 
                                           @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 根据广告系列ID删除广告组
     *
     * @param campaignId 广告系列ID
     * @return 删除的行数
     */
    int deleteByCampaignId(@Param("adAccountId") String adAccountId, @Param("campaignId") String campaignId);

    /**
     * 根据平台ID查询广告组的优化目标和事件类型
     *
     * @param platformId 平台ID
     * @return 广告组信息（包含优化目标和事件类型）
     */
    FbAdSetsDO selectOptimizationInfoByPlatformId(@Param("platformId") String platformId);
}