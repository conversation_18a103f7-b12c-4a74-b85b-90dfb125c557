/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.event.AdAccountUpdateEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.admin.system.model.entity.UserDO;

@Service
public class BillingAccountInformationScreenQueryStrategyImpl implements ReportOpsStrategy {
    @Override
    public ReportType getReport() {
        return ReportType.BILLING_ACCOUNT_INFORMATION_SCREEN_QUERY;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
//        JSONObject data = JSONObject.from(browserReq.getData());
//        String platformAdId = data.getJSONObject("body").getString("paymentAccountID");
//        JSONObject res = data.getJSONObject("res");
//        JSONObject resData = res.getJSONObject("data");
//        JSONObject paymentAccount = resData.getJSONObject("payment_account");
//        AdAccountDO accountDO = new AdAccountDO();
//        accountDO.setPlatformAdId(platformAdId);
//        if (paymentAccount == null) {
//            return JSONObject.toJSONString(accountDO);
//        }
//        JSONObject billableAccount = paymentAccount.getJSONObject("billable_account");
//        if (billableAccount == null) {
//            return JSONObject.toJSONString(accountDO);
//        }
//        String timezoneName = billableAccount.getJSONObject("timezone_info").getString("timezone");
//        String timezone = FacebookUtils.getTimezone(timezoneName);
//        accountDO.setBillCurrency(billableAccount.getString("currency"));
//        accountDO.setBillCountry(billableAccount.getJSONObject("billable_account_tax_info")
//            .getString("business_country_code"));
//        accountDO.setTimezone(timezone);
//        // SpringUtil.publishEvent(new AdAccountUpdateEvent(accountDO));
//        JSONObject jsonObject = JSONObject.from(accountDO);
//        jsonObject.put("timezoneName", timezoneName);

        return "";
    }
}
