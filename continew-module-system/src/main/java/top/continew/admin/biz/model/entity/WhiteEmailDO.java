package top.continew.admin.biz.model.entity;

import java.io.Serial;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 白名单邮箱实体
 *
 * <AUTHOR>
 * @since 2025/07/01 14:36
 */
@Data
@TableName("biz_white_email")
public class WhiteEmailDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮箱
     */
    private String email;
}