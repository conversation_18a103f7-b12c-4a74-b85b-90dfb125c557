package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.event.BMDisabledEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.TestOrderItemDO;
import top.continew.admin.biz.model.req.AdAccountTestOrderReq;
import top.continew.admin.biz.model.req.TestOrderStatusReq;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.TestOrderItemService;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.TestOrderMapper;
import top.continew.admin.biz.model.entity.TestOrderDO;
import top.continew.admin.biz.model.query.TestOrderQuery;
import top.continew.admin.biz.model.req.TestOrderReq;
import top.continew.admin.biz.model.resp.TestOrderDetailResp;
import top.continew.admin.biz.model.resp.TestOrderResp;
import top.continew.admin.biz.service.TestOrderService;

import java.util.ArrayList;
import java.util.List;

import static cn.hutool.json.XMLTokener.entity;

/**
 * 测试任务业务实现
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Service
@RequiredArgsConstructor
public class TestOrderServiceImpl extends BaseServiceImpl<TestOrderMapper, TestOrderDO, TestOrderResp, TestOrderDetailResp, TestOrderQuery, TestOrderReq> implements TestOrderService {

    private final TestOrderItemService testOrderItemService;

    private final AdAccountService adAccountService;

    @Override
    protected void afterAdd(TestOrderReq req, TestOrderDO entity) {

        String[] adAccountIds = req.getAdAccountIds().replaceAll("，", ",")
                .replaceAll("\\n", ",")
                .replaceAll(" ", "")
                .split(",");
        List<TestOrderItemDO> list = new ArrayList<>();
        for (String adAccountId : adAccountIds) {
            TestOrderItemDO testOrderItemDO = new TestOrderItemDO();
            testOrderItemDO.setOrderId(entity.getId());
            testOrderItemDO.setPlatformAdId(adAccountId);
            list.add(testOrderItemDO);
        }
        testOrderItemService.saveBatch(list);
    }

    @Override
    protected QueryWrapper<TestOrderDO> buildQueryWrapper(TestOrderQuery query) {
        QueryWrapper<TestOrderDO> wrapper = super.buildQueryWrapper(query);
        if (StringUtils.isNoneBlank(query.getAdAccountId())) {
            List<Long> ids = testOrderItemService.list(new LambdaQueryWrapper<TestOrderItemDO>()
                    .eq(TestOrderItemDO::getPlatformAdId, query.getAdAccountId())).stream().map(TestOrderItemDO::getOrderId).toList();
            wrapper.in("id", ids);
        }
        return wrapper;
    }

    @Override
    public void updateStatus(Long id, TestOrderStatusReq req) {
        TestOrderDO testOrder = getById(id);
        CheckUtils.throwIfNull(testOrder, "没有找到对应的测试任务");

        Integer status = testOrder.getStatus();
        if (status == 1 && req.getType() == 2) {
            update(new LambdaUpdateWrapper<TestOrderDO>()
                    .eq(TestOrderDO::getId, id)
                    .set(TestOrderDO::getStatus, req.getType()));
            testOrderItemService.update(new LambdaUpdateWrapper<TestOrderItemDO>()
                    .in(TestOrderItemDO::getOrderId, id)
                    .set(TestOrderItemDO::getStatus, req.getType()));
        }
        if (status == 2 && req.getType() == 3) {
            update(new LambdaUpdateWrapper<TestOrderDO>()
                    .eq(TestOrderDO::getId, id)
                    .set(TestOrderDO::getStatus, req.getType()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrder(AdAccountTestOrderReq req) {
        List<AdAccountDO> adAccountDOS = adAccountService.listByIds(req.getAdAccountIds());
        List<TestOrderItemDO> list = new ArrayList<>();
        Long orderId = req.getTestOrderId();

        if (req.getType() == 1) {
            TestOrderDO testOrderDO = new TestOrderDO();
            testOrderDO.setTitle(req.getTitle());
            testOrderDO.setTrelloUrl(req.getTrelloUrl());
            save(testOrderDO);
            orderId = testOrderDO.getId();
        }
        for (AdAccountDO adAccountDO : adAccountDOS) {
            TestOrderItemDO testOrderItemDO = new TestOrderItemDO();
            testOrderItemDO.setOrderId(orderId);
            testOrderItemDO.setPlatformAdId(adAccountDO.getPlatformAdId());
            list.add(testOrderItemDO);
        }
        testOrderItemService.saveBatch(list);
    }

    @Override
    protected void afterDelete(List<Long> ids) {
        testOrderItemService.remove(new LambdaQueryWrapper<TestOrderItemDO>()
                .in(TestOrderItemDO::getOrderId, ids));
    }
}