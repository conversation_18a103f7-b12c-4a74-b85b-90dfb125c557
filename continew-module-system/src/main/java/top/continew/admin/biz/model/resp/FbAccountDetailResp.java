/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * fb账号详情信息
 *
 * <AUTHOR>
 * @since 2025/01/03 16:32
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "fb账号详情信息")
public class FbAccountDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联账号ID
     */
    @Schema(description = "关联账号ID")
    private String platformAccountId;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    private String browserSerialNo;

    /**
     * 状态（1=正常，2=封禁）
     */
    @Schema(description = "状态（1=正常，2=封禁）")
    private Integer status;


    @ExcelProperty("价格")
    private BigDecimal price;

    private Long channelId;

    private String tag;

    /**
     *
     */
    @ExcelProperty("name")
    private String name;

    /**
     *
     */
    @ExcelProperty("remark")
    private String remark;

    /**
     *
     */
    @ExcelProperty("tab")
    private String tab;

    /**
     *
     */
    @ExcelProperty("platform")
    private String platform;

    /**
     *
     */
    @ExcelProperty("username")
    private String username;

    /**
     *
     */
    @ExcelProperty("password")
    private String password;

    /**
     *
     */
    @ExcelProperty("fakey")
    private String fakey;

    /**
     *
     */
    @ExcelProperty("cookie")
    private String cookie;

    /**
     *
     */
    @ExcelProperty("proxytype")
    private String proxyType;

    /**
     *
     */
    @ExcelProperty("ipchecker")
    private String ipChecker;

    /**
     *
     */
    @ExcelProperty("proxy")
    private String proxy;

    /**
     *
     */
    @ExcelProperty("proxyurl")
    private String proxyUrl;

    /**
     *
     */
    @ExcelProperty("proxyid")
    private Integer proxyId;

    /**
     *
     */
    @ExcelProperty("ip")
    private String ip;

    /**
     *
     */
    @ExcelProperty("countrycode")
    private String countryCode;

    /**
     *
     */
    @ExcelProperty("regioncode")
    private String regionCode;

    /**
     *
     */
    @ExcelProperty("citycode")
    private String cityCode;

    /**
     *
     */
    @ExcelProperty("ua")
    private String ua;

    /**
     *
     */
    @ExcelProperty("resolution")
    private String resolution;


    @ExcelIgnore
    private String customRemark;
}