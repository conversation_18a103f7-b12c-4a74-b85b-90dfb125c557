package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 中介返点查询条件
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Data
@Schema(description = "中介返点查询条件")
public class AgentRebateQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Query(type = QueryType.EQ, columns = "a.business_user_id")
    private Long businessUserId;

    /**
     * 关联中介
     */
    @Schema(description = "关联中介")
    @Query(type = QueryType.EQ)
    private Long agentId;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 结算月份
     */
    @Schema(description = "结算月份")
    @Query(type = QueryType.BETWEEN)
    private String[] settleMonth;

    /**
     * 返点日期
     */
    @Schema(description = "返点日期")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] rebateTime;
}