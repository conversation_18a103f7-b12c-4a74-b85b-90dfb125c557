package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.*;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.PurchaseOrderTypeEnum;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.admin.common.base.BaseResp;
import top.continew.starter.extension.crud.constant.ContainerPool;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

/**
 * 采购验收单详情信息
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "采购验收单详情信息")
public class PurchaseReceiveOrderDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联采购单
     */
    @Schema(description = "关联采购单")
    private Long purchaseOrderId;

    @ExcelProperty(value = "渠道")
    private String channelName;

    @ExcelProperty(value = "物料类型", converter = ExcelBaseEnumConverter.class)
    private PurchaseOrderTypeEnum type;

    /**
     * 验收数量
     */
    @Schema(description = "验收数量")
    @ExcelProperty(value = "验收数量")
    private Integer receiveNum;

    @ExcelProperty(value = "验收金额")
    private BigDecimal receivePrice;

    @ExcelProperty(value = "验收时间")
    private LocalDateTime receiveTime;

    /**
     * 创建人
     */
    @JsonIgnore
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "createUserString"))
    private Long createUser;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "超级管理员")
    @ExcelProperty(value = "创建人")
    private String createUserString;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2023-08-08 08:08:08", type = "string")
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;
}