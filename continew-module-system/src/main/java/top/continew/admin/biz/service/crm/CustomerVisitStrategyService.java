package top.continew.admin.biz.service.crm;

import top.continew.admin.biz.model.query.crm.CustomerVisitStrategyQuery;
import top.continew.admin.biz.model.req.crm.CustomerVisitStrategyReq;
import top.continew.admin.biz.model.resp.crm.CustomerExecuteStrategyResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitStrategyDetailResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitStrategyResp;
import top.continew.starter.extension.crud.service.BaseService;


/**
 * 客户回访策略业务接口
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
public interface CustomerVisitStrategyService extends BaseService<CustomerVisitStrategyResp, CustomerVisitStrategyDetailResp, CustomerVisitStrategyQuery, CustomerVisitStrategyReq> {

    /**
     * 执行客户回访策略
     *
     * @param strategyId 策略ID
     * @return 执行结果
     */
    CustomerExecuteStrategyResp executeStrategy(Long strategyId);

}