package top.continew.admin.biz.robot.strategy.impl;

import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotLeaveReq;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;

@Service
public class RobotLeaveStrategyImpl implements RobotCommandStrategy {
    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.LEAVE_TEMP;
    }

    @Override
    public String execute(Update update) {
        return BotUtils.parseObjToMsg(RobotCommandEnum.BUSINESS_LEAVE.getDescription(), RobotLeaveReq.class, true);
    }
}