package top.continew.admin.biz.model.resp;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ClearAccountAnalyzeResp {
    
    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 广告户ID
     */
    private String platformAdId;

    /**
     * 账号状态
     */
    private Integer accountStatus;

    /**
     * 清零状态
     */
    private Integer status;

    /**
     * 清零时间
     */
    private LocalDateTime finishTime;

    /**
     * 沉寂天数
     */
    private Integer sleepDays;

    /**
     * 时区
     */
    private String timezone;
}