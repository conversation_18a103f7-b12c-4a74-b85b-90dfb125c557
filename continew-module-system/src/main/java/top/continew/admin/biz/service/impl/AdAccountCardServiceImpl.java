/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.mapper.AdAccountCardMapper;
import top.continew.admin.biz.model.entity.AdAccountCardDO;
import top.continew.admin.biz.model.req.AdAccountOpenCardReq;
import top.continew.admin.biz.model.resp.AdAccountCardResp;
import top.continew.admin.biz.service.AdAccountCardService;

import java.util.List;

/**
 * 广告户关联卡业务实现
 *
 * <AUTHOR>
 * @since 2025/01/02 11:24
 */
@Service
@RequiredArgsConstructor
public class AdAccountCardServiceImpl extends ServiceImpl<AdAccountCardMapper, AdAccountCardDO> implements AdAccountCardService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefault(String platformAdId, Long cardId) {
        update(new LambdaUpdateWrapper<AdAccountCardDO>().eq(AdAccountCardDO::getPlatformAdId, platformAdId)
            .set(AdAccountCardDO::getIsDefault, false));

        update(new LambdaUpdateWrapper<AdAccountCardDO>().eq(AdAccountCardDO::getPlatformAdId, platformAdId)
            .eq(AdAccountCardDO::getId, cardId)
            .set(AdAccountCardDO::getIsDefault, true));
    }

    @Override
    public void syncCardNo() {

    }

    @Override
    public List<AdAccountCardDO> listValidByPlatformAdId(String platformAdId) {
        return this.list(Wrappers.<AdAccountCardDO>lambdaQuery()
            .eq(AdAccountCardDO::getPlatformAdId, platformAdId)
            .eq(AdAccountCardDO::getIsRemove, false));
    }

    @Override
    public AdAccountCardDO getDefaultCard(String platformAdId) {
        return this.getOne(Wrappers.<AdAccountCardDO>lambdaQuery()
            .eq(AdAccountCardDO::getPlatformAdId, platformAdId)
            .eq(AdAccountCardDO::getIsDefault, true)
            .eq(AdAccountCardDO::getIsRemove, false));
    }

    @Override
    public List<AdAccountCardDO> listByPlatformAdId(String platformAdId) {
        return this.list(Wrappers.<AdAccountCardDO>lambdaQuery().eq(AdAccountCardDO::getPlatformAdId, platformAdId));
    }

    @Override
    public void openCard(AdAccountOpenCardReq req) {

    }

    @Override
    public List<AdAccountCardResp> listCardByPlatformAdId(String platformAdId) {
        return this.baseMapper.listByPlatformAdId(platformAdId);
    }
}