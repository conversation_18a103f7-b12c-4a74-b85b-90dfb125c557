package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "BM封禁分析数据")
public class BusinessManagerBanStatDataResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "封禁原因")
    private BusinessManagerBannedReasonEnum bannedReason;

    @Schema(description = "封禁数量")
    private Integer bannedCount;


}