package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import com.alibaba.excel.annotation.format.NumberFormat;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.common.base.BaseDetailResp;

/**
 * 客户每日统计详情信息
 *
 * <AUTHOR>
 * @since 2025/07/17 11:39
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "客户每日统计详情信息")
public class CustomerDailyStatDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    private Long customerId;

    @ExcelProperty(value = "关联商务")
    private String businessUserName;

    @ExcelProperty(value = "关联客户")
    private String customerName;
    /**
     * 统计时间
     */
    @Schema(description = "统计时间")
    @ExcelProperty(value = "统计时间")
    private LocalDate statDate;

    /**
     * 总户数
     */
    @Schema(description = "总户数")
    @ExcelProperty(value = "总户数")
    private Integer totalAccount;

    /**
     * 当前户数
     */
    @Schema(description = "当前户数")
    @ExcelProperty(value = "当前户数")
    private Integer totalFinishAccount;

    /**
     * 正常户数
     */
    @Schema(description = "正常户数")
    @ExcelProperty(value = "正常户数")
    private Integer totalNormalAccount;

    /**
     * 使用户数
     */
    @Schema(description = "使用户数")
    @ExcelProperty(value = "使用户数")
    private Integer todayUsedAccount;

    @Schema(description = "使用率")
    @ExcelProperty(value = "使用率")
    @NumberFormat("0.00%")
    private BigDecimal usedRate;

    /**
     * 封禁数量
     */
    @Schema(description = "封禁数量")
    @ExcelProperty(value = "封禁数量")
    private Integer todayBanAccount;

    /**
     * 下户数
     */
    @Schema(description = "下户数")
    @ExcelProperty(value = "下户数")
    private Integer todayOpenAccount;

    /**
     * 卡台消耗
     */
    @Schema(description = "卡台消耗")
    @ExcelProperty(value = "卡台消耗")
    private BigDecimal todayCardSpent;
}