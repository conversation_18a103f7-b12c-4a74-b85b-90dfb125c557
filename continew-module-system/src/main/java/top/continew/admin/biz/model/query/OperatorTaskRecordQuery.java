package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 运营人员工作记录查询条件
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
@Data
@Schema(description = "运营人员工作记录查询条件")
public class OperatorTaskRecordQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    @Query(type = QueryType.EQ)
    private String platformAdId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @Query(type = QueryType.EQ)
    private Integer type;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Query(type = QueryType.EQ)
    private Long createUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;
}