package top.continew.admin.biz.model.entity;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CustomerActivationReq {

    @NotEmpty(message = "客户ID不能为空")
    private List<Long> ids;

    @NotNull(message = "合作时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cooperateTime;

}
