package top.continew.admin.biz.mapper.analyze;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.BannedAnalyzeQuery;
import top.continew.admin.biz.model.resp.CustomerBanStatsResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户广告户封禁分析业务MAPPER
 *
 * <AUTHOR>
 * @since 2025/1/19 15:30
 */
public interface CustomerAdAccountBannedStatMapper extends BaseMapper<CustomerDO> {

    IPage<CustomerBanStatsResp> selectPageCustomerBase(@Param("page") Page<Object> objectPage, @Param("query") BannedAnalyzeQuery query);

    CustomerBanStatsResp selectInfoData(@Param("customerId") Long customerId, @Param("adAccountIds") List<String> adAccountIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("banType") List<Integer> banType, @Param("oneDollar") Boolean oneDollar, @Param("cardHard") String cardHard, @Param("timezone") String timezone);

    CustomerBanStatsResp getCustomerDashboardStats(@Param("customerIds")List<Long> customerIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("banType") List<Integer> banType);

    CustomerBanStatsResp getAdAccountDashboardStats(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("banType") List<Integer> banType);
}
