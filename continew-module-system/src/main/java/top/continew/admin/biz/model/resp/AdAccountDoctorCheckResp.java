/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "广告户诊断响应")
public class AdAccountDoctorCheckResp {
    private String adAccountId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "时区")
    private String timezone;

    @Schema(description = "是否一刀流")
    private Boolean isOneDollar;

    @Schema(description = "FB余额")
    private BigDecimal fbBalance;

    @Schema(description = "卡台余额")
    private BigDecimal cardBalance;

    @Schema(description = "最后一笔交易流水")
    private CardTransactionResp lastTrans;

    @Schema(description = "浏览器编号")
    private String browserNo;

    @Schema(description = "广告总数")
    private Integer adCount;

    @Schema(description = "广告系列数量")
    private Integer campaignCount;

    @Schema(description = "广告组数量")
    private Integer adSetCount;

    @Schema(description = "正常广告数量")
    private Integer activeAdCount;

    @Schema(description = "被拒广告数量")
    private Integer rejectAdCount;

    @Schema(description = "准备中广告数量")
    private Integer prepareAdCount;

    @Schema(description = "审核中广告数量")
    private Integer previewAdCount;

    @Schema(description = "学习中广告数量")
    private Integer learningAdCount;

    @Schema(description = "平均预算")
    private BigDecimal averageBudget;

    @Schema(description = "单次成效目标广告数量")
    private Integer costCapCount;

}