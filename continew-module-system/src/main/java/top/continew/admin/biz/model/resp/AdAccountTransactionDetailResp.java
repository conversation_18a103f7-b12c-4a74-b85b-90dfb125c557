package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 广告户交易记录详情信息
 *
 * <AUTHOR>
 * @since 2025/03/10 14:15
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "广告户交易记录详情信息")
public class AdAccountTransactionDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @ExcelProperty(value = "广告户ID")
    private String platformAdId;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @ExcelProperty(value = "交易时间")
    private LocalDateTime transTime;

    /**
     * 交易ID
     */
    @Schema(description = "交易ID")
    @ExcelProperty(value = "交易ID")
    private String transactionId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @ExcelProperty(value = "交易类型")
    private String transactionType;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    @ExcelProperty(value = "交易金额")
    private BigDecimal amount;

    /**
     * 交易状态
     */
    @Schema(description = "交易状态")
    @ExcelProperty(value = "交易状态")
    private String status;
}