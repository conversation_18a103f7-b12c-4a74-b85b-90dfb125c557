package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.CustomerDailyStatDO;
import top.continew.admin.biz.model.resp.CustomerDailyStatByDateResp;
import top.continew.admin.biz.model.resp.CustomerDailyStatDetailResp;
import top.continew.admin.biz.model.resp.CustomerDailyStatResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.util.List;

/**
 * 客户每日统计 Mapper
 *
 * <AUTHOR>
 * @since 2025/07/17 10:11
 */
public interface CustomerDailyStatMapper extends BaseMapper<CustomerDailyStatDO> {

    IPage<CustomerDailyStatResp> selectCustomPage(@Param("page") IPage<CustomerDailyStatDO> page,
                                                  @Param(Constants.WRAPPER) QueryWrapper<CustomerDailyStatDO> queryWrapper);

    List<CustomerDailyStatDetailResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<CustomerDailyStatDO> queryWrapper);


    IPage<CustomerDailyStatByDateResp> selectDailyPage(@Param("page") IPage<CustomerDailyStatDO> page,
                                                       @Param(Constants.WRAPPER) QueryWrapper<CustomerDailyStatDO> queryWrapper);

    List<CustomerDailyStatByDateResp> selectDailyList(@Param(Constants.WRAPPER) QueryWrapper<CustomerDailyStatDO> queryWrapper);
}