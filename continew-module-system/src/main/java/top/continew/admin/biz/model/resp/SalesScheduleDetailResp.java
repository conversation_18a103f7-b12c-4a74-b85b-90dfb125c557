package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.ScheduleTypeEnum;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.extension.crud.constant.ContainerPool;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

/**
 * 商务人员排班/请假记录详情信息
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "商务人员排班/请假记录详情信息")
public class SalesScheduleDetailResp extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商务人员ID
     */
    @Schema(description = "商务人员ID")
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "salesName"))
    private Long salesId;

    @ExcelProperty(value = "商务人员")
    private String salesName;


    /**
     * 排班/请假的具体日期
     */
    @Schema(description = "排班/请假的具体日期")
    @ExcelProperty(value = "日期")
    private LocalDate scheduleDate;

    /**
     * 类型: 1=请假, 2=排班
     */
    @Schema(description = "类型: 1=请假, 2=排班")
    @ExcelProperty(value = "类型", converter = ExcelBaseEnumConverter.class)
    private ScheduleTypeEnum scheduleType;

}