package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.extension.crud.constant.ContainerPool;

/**
 * 商务日报详情信息
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "商务日报详情信息")
public class SalesDailySummaryDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日报记录的日期
     */
    @Schema(description = "日期")
    @ExcelProperty(value = "日期")
    private LocalDate recordDate;

    @Schema(description = "创建人", example = "超级管理员")
    @ExcelProperty(value = "商务")
    private String createUserString;

    /**
     * 日报具体内容，支持长文本
     */
    @Schema(description = "日报具体内容")
    @ExcelProperty(value = "日报具体内容")
    private String content;

    @JsonIgnore
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "createUserString"))
    private Long createUser;


    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}