package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.AdProductStatDO;
import top.continew.admin.biz.model.resp.*;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 产品日报 Mapper
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
public interface AdProductStatMapper extends BaseMapper<AdProductStatDO> {

    IPage<AdProductStatResp> selectCustomPage(@Param("page") IPage<AdProductStatDO> page,
                                              @Param(Constants.WRAPPER) QueryWrapper<AdProductStatDO> queryWrapper);

    List<AdProductStatDetailResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<AdProductStatDO> queryWrapper);

    List<CampaignPerformanceResp> selectCampaignData(@Param("adAccountId") String adAccountId,
                                                     @Param("customerId") Long customerId);

    List<CampaignInsightDataResp> selectCampaignInsight(@Param("platformId") String platformId,
                                                        @Param("statDate") LocalDate statDate);

    List<ToufangCustomerDailyReportResp> getCustomerDailyReport(@Param("customerId") Long customerId);
}