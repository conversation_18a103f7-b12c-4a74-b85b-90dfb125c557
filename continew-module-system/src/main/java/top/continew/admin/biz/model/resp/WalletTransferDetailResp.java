package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.extension.crud.model.resp.BaseResp;

/**
 * 钱包流水详情信息
 *
 * <AUTHOR>
 * @since 2025/07/22 15:59
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "钱包流水详情信息")
public class WalletTransferDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易号
     */
    @Schema(description = "交易号")
    @ExcelProperty(value = "交易号")
    private String transactionId;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @ExcelProperty(value = "交易时间")
    private LocalDateTime transTime;

    /**
     * 金额
     */
    @Schema(description = "金额")
    @ExcelProperty(value = "金额")
    private BigDecimal amount;

    @Schema(description = "关联客户")
    @ExcelProperty(value = "关联客户")
    private String customerName;

    /**
     * 来源地址
     */
    @Schema(description = "打款地址")
    @ExcelProperty(value = "打款地址")
    private String fromAddress;

    /**
     * to_address
     */
    @Schema(description = "收款地址")
    @ExcelProperty(value = "收款地址")
    private String toAddress;
}