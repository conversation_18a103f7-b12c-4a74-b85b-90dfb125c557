package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.admin.biz.model.entity.PurchaseOrderDO;
import top.continew.admin.biz.model.query.PurchaseOrderQuery;
import top.continew.admin.biz.model.resp.PurchaseOrderResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.PurchaseReceiveOrderMapper;
import top.continew.admin.biz.model.entity.PurchaseReceiveOrderDO;
import top.continew.admin.biz.model.query.PurchaseReceiveOrderQuery;
import top.continew.admin.biz.model.req.PurchaseReceiveOrderReq;
import top.continew.admin.biz.model.resp.PurchaseReceiveOrderDetailResp;
import top.continew.admin.biz.model.resp.PurchaseReceiveOrderResp;
import top.continew.admin.biz.service.PurchaseReceiveOrderService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购验收单业务实现
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Service
@RequiredArgsConstructor
public class PurchaseReceiveOrderServiceImpl extends BaseServiceImpl<PurchaseReceiveOrderMapper, PurchaseReceiveOrderDO, PurchaseReceiveOrderResp, PurchaseReceiveOrderDetailResp, PurchaseReceiveOrderQuery, PurchaseReceiveOrderReq> implements PurchaseReceiveOrderService {

    @Override
    public PageResp<PurchaseReceiveOrderResp> page(PurchaseReceiveOrderQuery query, PageQuery pageQuery) {
        QueryWrapper<PurchaseReceiveOrderDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, pageQuery);
        IPage<PurchaseReceiveOrderResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        PageResp<PurchaseReceiveOrderResp> pageResp = PageResp.build(page, this.getListClass());
        pageResp.getList().forEach(this::fill);
        return pageResp;
    }

    @Override
    protected <E> List<E> list(PurchaseReceiveOrderQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<PurchaseReceiveOrderDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        List<PurchaseReceiveOrderResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        entityList.forEach(this::fill);
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    public List<PurchaseReceiveOrderResp> selectPurchaseTotal(LocalDateTime[] createTime) {
        QueryWrapper<PurchaseReceiveOrderDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("ro.receive_time", createTime[0], createTime[1]);
        queryWrapper.groupBy("o.type", "o.channel_id");
        return this.baseMapper.selectPurchaseTotal(queryWrapper);
    }
}