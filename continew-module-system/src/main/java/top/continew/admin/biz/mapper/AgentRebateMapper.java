package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.AgentRebateDO;
import top.continew.admin.biz.model.resp.AgentRebateDetailResp;
import top.continew.admin.biz.model.resp.AgentRebateResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.util.List;

/**
 * 中介返点 Mapper
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
public interface AgentRebateMapper extends BaseMapper<AgentRebateDO> {

    IPage<AgentRebateResp> selectCustomPage(@Param("page") IPage<AgentRebateDO> page,
                                            @Param(Constants.WRAPPER) QueryWrapper<AgentRebateDO> queryWrapper);

    List<AgentRebateDetailResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<AgentRebateDO> queryWrapper);
}