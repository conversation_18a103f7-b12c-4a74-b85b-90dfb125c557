package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 客户钱包查询条件
 *
 * <AUTHOR>
 * @since 2025/07/22 16:58
 */
@Data
@Schema(description = "客户钱包查询条件")
public class CustomerWalletQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 钱包地址
     */
    @Schema(description = "钱包地址")
    @Query(type = QueryType.EQ)
    private String walletAddress;
}