package top.continew.admin.biz.model.query.crm;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 社交账号查询条件
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@Schema(description = "社交账号查询条件")
public class SocialAccountQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账号类型：telegram、wechat、line、phone
     */
    @Schema(description = "账号类型：telegram、wechat、line、phone")
    @Query(type = QueryType.EQ)
    private Integer accountType;

    /**
     * 账号
     */
    @Schema(description = "账号")
    @Query(type = QueryType.EQ)
    private String account;

    /**
     * 分配人
     */
    @Schema(description = "分配人")
    @Query(type = QueryType.EQ)
    private Long assigneeId;

    /**
     * 账号状态：1-启用、2-禁用
     */
    @Schema(description = "账号状态：1-启用、2-禁用")
    @Query(type = QueryType.EQ)
    private Integer status;
}