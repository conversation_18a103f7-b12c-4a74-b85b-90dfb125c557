package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.CustomerQuestionMapper;
import top.continew.admin.biz.model.entity.CustomerQuestionDO;
import top.continew.admin.biz.model.query.CustomerQuestionQuery;
import top.continew.admin.biz.model.req.CustomerQuestionReq;
import top.continew.admin.biz.model.resp.CustomerQuestionDetailResp;
import top.continew.admin.biz.model.resp.CustomerQuestionResp;
import top.continew.admin.biz.service.CustomerQuestionService;

import java.util.List;

/**
 * 客户问题业务实现
 *
 * <AUTHOR>
 * @since 2025/04/30 14:35
 */
@Service
@RequiredArgsConstructor
public class CustomerQuestionServiceImpl extends BaseServiceImpl<CustomerQuestionMapper, CustomerQuestionDO, CustomerQuestionResp, CustomerQuestionDetailResp, CustomerQuestionQuery, CustomerQuestionReq> implements CustomerQuestionService {
    @Override
    public Integer getQuestionNum(CustomerQuestionQuery query) {
        List<CustomerQuestionDO> list = list(this.buildQueryWrapper(query));
        return list.stream().map(CustomerQuestionDO::getNum).reduce(0, Integer::sum);
    }
}