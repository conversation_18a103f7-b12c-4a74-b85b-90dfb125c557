package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.TransactionUserMapper;
import top.continew.admin.biz.model.entity.TransactionUserDO;
import top.continew.admin.biz.model.query.TransactionUserQuery;
import top.continew.admin.biz.model.req.TransactionUserReq;
import top.continew.admin.biz.model.resp.TransactionUserDetailResp;
import top.continew.admin.biz.model.resp.TransactionUserResp;
import top.continew.admin.biz.service.TransactionUserService;

import java.util.List;

/**
 * 交易对象业务实现
 *
 * <AUTHOR>
 * @since 2025/08/17 14:22
 */
@Service
@RequiredArgsConstructor
public class TransactionUserServiceImpl extends BaseServiceImpl<TransactionUserMapper, TransactionUserDO, TransactionUserResp, TransactionUserDetailResp, TransactionUserQuery, TransactionUserReq> implements TransactionUserService {

    @Override
    protected void beforeAdd(TransactionUserReq req) {
        List<TransactionUserTypeEnum> validList = List.of(TransactionUserTypeEnum.TOULIU_TEAM, TransactionUserTypeEnum.MATERIAL_SHOP_USER);
        CheckUtils.throwIf(!validList.contains(req.getTransactionType()), "该类型的用户不允许自主添加");
        TransactionUserDO exist = this.getOne(Wrappers.<TransactionUserDO>lambdaQuery()
            .eq(TransactionUserDO::getTransactionType, req.getTransactionType())
            .eq(TransactionUserDO::getName, req.getName()));
        CheckUtils.throwIfNotNull(exist, "请勿重复添加客户");
    }

    @Override
    public Long getUser(Long referId, TransactionUserTypeEnum type, String name) {
        TransactionUserDO exist = this.getOne(Wrappers.<TransactionUserDO>lambdaQuery()
            .eq(TransactionUserDO::getTransactionType, type)
            .eq(TransactionUserDO::getName, name));
        if (exist != null) {
            return exist.getId();
        } else {
            TransactionUserDO user = new TransactionUserDO();
            user.setTransactionType(type);
            user.setName(name);
            user.setReferId(referId);
            this.save(user);
            return user.getId();
        }
    }
}