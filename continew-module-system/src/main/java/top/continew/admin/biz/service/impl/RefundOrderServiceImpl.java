/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.FbLimitCheckStatusEnum;
import top.continew.admin.biz.enums.RefundOrderStatusEnum;
import top.continew.admin.biz.event.RefundOrderCancelEvent;
import top.continew.admin.biz.event.RefundOrderFinishEvent;
import top.continew.admin.biz.event.RefundOrderHandleEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.mapper.RefundOrderMapper;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.RefundOrderDO;
import top.continew.admin.biz.model.query.RefundOrderQuery;
import top.continew.admin.biz.model.req.RefundOrderByCustomerReq;
import top.continew.admin.biz.model.req.RefundOrderFinishReq;
import top.continew.admin.biz.model.req.RefundOrderReq;
import top.continew.admin.biz.model.resp.RefundOrderDetailResp;
import top.continew.admin.biz.model.resp.RefundOrderResp;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.RefundOrderService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.admin.system.service.FileService;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

/**
 * 退款订单业务实现
 *
 * <AUTHOR>
 * @since 2025/01/10 15:39
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefundOrderServiceImpl extends BaseServiceImpl<RefundOrderMapper, RefundOrderDO, RefundOrderResp, RefundOrderDetailResp, RefundOrderQuery, RefundOrderReq> implements RefundOrderService {

    private final FileService fileService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    @Override
    public PageResp<RefundOrderResp> page(RefundOrderQuery query, PageQuery pageQuery) {
        QueryWrapper<RefundOrderDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, pageQuery);
        IPage<RefundOrderResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        return PageResp.build(page);
    }

    @Override
    public List<RefundOrderResp> list(RefundOrderQuery query, SortQuery sortQuery) {
        QueryWrapper<RefundOrderDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, sortQuery);
        return baseMapper.selectCustomList(queryWrapper);
    }

    @Override
    public synchronized void handleOrder(Long id) {
        RefundOrderDO order = this.getById(id);
        CheckUtils.throwIf(!order.getStatus().equals(RefundOrderStatusEnum.WAIT), "订单已被其他人处理");
        this.update(Wrappers.<RefundOrderDO>lambdaUpdate()
                .set(RefundOrderDO::getHandleUser, UserContextHolder.getUserId())
                .set(RefundOrderDO::getStatus, RefundOrderStatusEnum.HANDLING)
                .set(RefundOrderDO::getHandleTime, LocalDateTime.now())
                .eq(RefundOrderDO::getId, id));
        SpringUtil.publishEvent(new RefundOrderHandleEvent(order.getId()));
    }

    @Override
    public synchronized void finishOrder(RefundOrderFinishReq req) {
        RefundOrderDO order = this.getById(req.getId());
        CheckUtils.throwIf(!order.getStatus().equals(RefundOrderStatusEnum.HANDLING), "订单未在处理中");
        CheckUtils.throwIf(!order.getHandleUser().equals(UserContextHolder.getUserId()), "仅限处理人进行操作");
        SpringUtil.publishEvent(new RefundOrderFinishEvent(req));
    }

    @Override
    public synchronized void cancelOrder(Long id) {
        RefundOrderDO order = this.getById(id);
        CheckUtils.throwIf(order.getHandleUser() != null && !order.getHandleUser()
                .equals(UserContextHolder.getUserId()), "仅限处理人进行操作");
        CheckUtils.throwIf(order.getStatus().equals(RefundOrderStatusEnum.FINISH) || order.getStatus()
                .equals(RefundOrderStatusEnum.CANCEL), "订单状态错误，无法取消");
        SpringUtil.publishEvent(new RefundOrderCancelEvent(order.getId()));
    }

    @Override
    public void checkModifyLimit(String platformAdId,
                                 BigDecimal newLimitAmount,
                                 BigDecimal oldLimitAmount,
                                 String fbOpsId) {
        BigDecimal reduceLimitAmount = oldLimitAmount.subtract(newLimitAmount);
        log.info("【广告户限额检测】广告户{}当前限额：{}，减少限额：{}", platformAdId, oldLimitAmount, reduceLimitAmount);
        RefundOrderDO order = this.getOne(Wrappers.<RefundOrderDO>lambdaQuery()
                .eq(RefundOrderDO::getPlatformAdId, platformAdId)
                .eq(RefundOrderDO::getStatus, RefundOrderStatusEnum.HANDLING));
        CheckUtils.throwIfNull(order, "当前广告户不存在进行中的减款订单");
        CheckUtils.throwIf(order.getFbCheckStatus()
                .equals(FbLimitCheckStatusEnum.SUCCESS), "当前广告户不存在未检测的减款订单");
        CheckUtils.throwIf(reduceLimitAmount.compareTo(order.getAmount()) != 0, "减少限额与后台减款金额不匹配，减款金额为：%s".formatted(NumberUtil.toStr(order.getAmount())));
        this.update(Wrappers.<RefundOrderDO>lambdaUpdate()
                .set(RefundOrderDO::getFbCheckStatus, FbLimitCheckStatusEnum.SUCCESS)
                .set(RefundOrderDO::getFbOpsId, fbOpsId)
                .eq(RefundOrderDO::getId, order.getId()));
    }

    @Override
    public void uploadModifyLimitCertificate(String platformAdId, String fbOpsId, String imageBase64) {
        RefundOrderDO order = this.getOne(Wrappers.<RefundOrderDO>lambdaQuery()
                .eq(RefundOrderDO::getPlatformAdId, platformAdId)
                .eq(RefundOrderDO::getFbOpsId, fbOpsId)
                .eq(RefundOrderDO::getStatus, RefundOrderStatusEnum.HANDLING));
        CheckUtils.throwIfNull(order, "减款订单不存在");
        String certificate = "";
        if (StringUtils.isNotBlank(imageBase64)) {
            byte[] imageBytes = Base64.getDecoder().decode(imageBase64);
            FileInfo fileInfo = fileService.uploadImage(imageBytes, "");
            certificate = fileInfo.getUrl();
        }
        this.update(Wrappers.<RefundOrderDO>lambdaUpdate()
                .set(RefundOrderDO::getFbCheckStatus, FbLimitCheckStatusEnum.FINISH)
                .set(StringUtils.isNotBlank(certificate), RefundOrderDO::getCertificate, certificate)
                .eq(RefundOrderDO::getId, order.getId()));
    }

    @Override
    public void refundApply(RefundOrderByCustomerReq req) {
        CustomerService customerService = SpringUtil.getBean(CustomerService.class);
        AdAccountService adAccountService = SpringUtil.getBean(AdAccountService.class);
        AdAccountOrderService adAccountOrderService = SpringUtil.getBean(AdAccountOrderService.class);

        CustomerDO customer = customerService.getById(req.getCustomerId());
        CheckUtils.throwIfNull(customer, "未找到相关客户");
        AdAccountDO adAccount = adAccountService.getOne(Wrappers.<AdAccountDO>lambdaQuery()
                .eq(AdAccountDO::getPlatformAdId, req.getAdAccountId()));
        CheckUtils.throwIfNull(adAccount, "未找到相关广告户");
        adAccountOrderService.checkExistOrder(customer.getId(), req.getAdAccountId());
        // 一个广告户只允许一条进行中的退款订单
        RefundOrderDO existOrder = this.getOne(Wrappers.<RefundOrderDO>lambdaQuery()
                .eq(RefundOrderDO::getPlatformAdId, req.getAdAccountId())
                .in(RefundOrderDO::getStatus, List.of(RefundOrderStatusEnum.WAIT, RefundOrderStatusEnum.HANDLING)));
        CheckUtils.throwIfNotNull(existOrder, "请先处理之前的减款订单");
        RefundOrderDO order = new RefundOrderDO();
        order.setOrderNo(CommonUtils.randomOrderNo("JK"));
        order.setCustomerId(customer.getId());
        order.setPlatformAdId(req.getAdAccountId());
        order.setAmount(req.getAmount());
        order.setStatus(RefundOrderStatusEnum.WAIT);
        save(order);

        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getClientNotifyChatId())
                .text(String.format("""
                        【待确认的减款操作】
                        客户：%s
                        广告户ID：%s
                        金额：%s
                        """, customer.getName(), req.getAdAccountId(), req.getAmount()))
                .build()));
    }
}