package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 交易对象查询条件
 *
 * <AUTHOR>
 * @since 2025/08/17 14:22
 */
@Data
@Schema(description = "交易对象查询条件")
public class TransactionUserQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @Query(type = QueryType.EQ)
    private Integer transactionType;

    /**
     * 名字
     */
    @Schema(description = "名字")
    @Query(type = QueryType.LIKE)
    private String name;
}