package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 采购验收单查询条件
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@Schema(description = "采购验收单查询条件")
public class PurchaseReceiveOrderQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联采购单
     */
    @Schema(description = "关联采购单")
    @Query(type = QueryType.EQ, columns = "ro.purchase_order_id")
    private Long purchaseOrderId;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    @Query(type = QueryType.EQ, columns = "o.channel_id")
    private Long channelId;

    /**
     * 物料类型
     */
    @Schema(description = "物料类型")
    @Query(type = QueryType.EQ, columns = "o.type")
    private Integer type;

    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN, columns = "ro.receive_time")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] receiveTime;
}