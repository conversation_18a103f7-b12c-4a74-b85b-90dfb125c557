package top.continew.admin.biz.model.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ClearAccountAnalyzeQuery extends PageQuery {
    
    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 广告户ID
     */
    private String platformAdId;

    /**
     * 最小沉寂天数
     */
    private Integer minSleepDays;

    /**
     * 最大沉寂天数
     */
    private Integer maxSleepDays;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 账号状态列表
     */
    private List<Integer> accountStatusList;
}