package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "客户统计响应")
@ExcelIgnoreUnannotated
public class CustomerStatisticsResp {

    @Schema(description = "客户ID")
    private Long id;

    @ExcelProperty("客户")
    @Schema(description = "客户名称")
    private String name;

    @Schema(description = "对接商务")
    @ExcelProperty("商务")
    private String businessUserName;

    @Schema(description = "总户数")
    @ExcelProperty("总户数")
    private Integer totalAccount;

    @Schema(description = "死户数量")
    @ExcelProperty("死户数")
    private Integer deadAccount;

    @Schema(description = "死户率")
    @ExcelProperty("死户率")
    private BigDecimal deadRate;

    @Schema(description = "总充值")
    @ExcelProperty("总充值")
    private BigDecimal totalRecharge;

    @Schema(description = "卡台余额")
    @ExcelProperty("卡台余额")
    private BigDecimal cardBalance;

    @Schema(description = "总消耗")
    @ExcelProperty("FB消耗")
    private BigDecimal totalSpend;

    @Schema(description = "卡台消耗")
    @ExcelProperty("卡台消耗")
    private BigDecimal cardSpent;

    @Schema(description = "占用金额")
    @ExcelProperty("占用资金")
    private BigDecimal occupiedAmount;

    @Schema(description = "FB余额")
    @ExcelProperty("FB余额")
    private BigDecimal fbBalance;

    @Schema(description = "平均FB消耗")
    @ExcelProperty("平均FB消耗")
    private BigDecimal averageFbSpend;

    @Schema(description = "平均卡台消耗")
    @ExcelProperty("平均卡台消耗")
    private BigDecimal averageCardSpend;

    @Schema(description = "平均充值")
    @ExcelProperty("平均充值")
    private BigDecimal averageRecharge;

    @Schema(description = "开户费")
    @ExcelProperty("开户费")
    private BigDecimal payAmount;
}
