package top.continew.admin.biz.service.impl.crm;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import top.continew.admin.biz.enums.VisitTaskStatusEnum;
import top.continew.admin.biz.mapper.crm.CustomerVisitTaskDetailMapper;
import top.continew.admin.biz.mapper.crm.CustomerVisitTaskMapper;
import top.continew.admin.biz.model.entity.crm.CustomerVisitTaskDetailDO;
import top.continew.admin.biz.model.entity.crm.LeadDO;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskDetailReq;
import top.continew.admin.biz.model.resp.CustomerConditionResp;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.crm.CustomerVisitTaskDO;
import top.continew.admin.biz.model.query.crm.CustomerVisitTaskQuery;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskReq;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskUpdateReq;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskCloseReq;
import top.continew.admin.biz.model.resp.crm.CustomerVisitTaskDetailResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitTaskDetailsResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitTaskResp;
import top.continew.admin.biz.model.resp.crm.LeadResp;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.crm.CustomerVisitTaskService;
import top.continew.admin.system.service.UserService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.core.exception.BusinessException;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 客户回访任务业务实现
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerVisitTaskServiceImpl extends BaseServiceImpl<CustomerVisitTaskMapper, CustomerVisitTaskDO, CustomerVisitTaskResp, CustomerVisitTaskDetailResp, CustomerVisitTaskQuery, CustomerVisitTaskReq> implements CustomerVisitTaskService {
    
    private final CustomerService customerService;
    private final UserService userService;
    private final CustomerVisitTaskDetailMapper visitTaskDetailMapper;

    @Override
    public void update(CustomerVisitTaskReq req, Long id) {
        super.update(req, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(CustomerVisitTaskReq req) {
        List<Long> taskIds = new ArrayList<>();
        
        // 遍历客户ID列表，为每个客户创建回访任务
        for (Long customerId : req.getCustomerIds()) {
            // 获取客户详情
            CustomerDO customer = customerService.getById(customerId);
            if (customer == null) {
                log.warn("客户不存在，跳过创建任务，客户ID: {}", customerId);
                continue;
            }

            if(hasValidVisitTask(customerId)){
                log.warn("客户存在未完成的回访任务，跳过创建任务，客户ID: {}", customerId);
                throw new BusinessException(String.format("客户[%s]已存在回访任务", customer.getName()));
            }


            // 创建回访任务
            CustomerVisitTaskDO entity = new CustomerVisitTaskDO();
            entity.setCustomerId(customerId);
            entity.setStrategyId(req.getStrategyId());
            entity.setStrategyName(req.getStrategyName());
            entity.setReminderTime(LocalDateTime.now().plusDays(1));
            entity.setTaskStatus(VisitTaskStatusEnum.TO_DO); // 1-待处理
            entity.setAssigneeId(StpUtil.getLoginIdAsLong()); // 设置当前用户为客户的关联商务
            entity.setRequiredFinishTime(req.getRequiredFinishTime());
            entity.setVisitGoal(req.getVisitGoal());
            entity.setTriggerInfo(req.getTriggerInfo());
            entity.setCreateTime(LocalDateTime.now());
            
            baseMapper.insert(entity);
            taskIds.add(entity.getId());
        }
        
        // 返回第一个任务ID（兼容原有接口）
        return taskIds.isEmpty() ? null : taskIds.get(0);
    }

    @Override
    public PageResp<CustomerVisitTaskResp> page(CustomerVisitTaskQuery query, PageQuery pageQuery) {
        Page<CustomerVisitTaskDO> pageReq = new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize());

        IPage<CustomerVisitTaskDO> page = baseMapper.selectTaskPage(pageReq, query);
        PageResp<CustomerVisitTaskResp> resp = PageResp.build(page, CustomerVisitTaskResp.class);

        for (CustomerVisitTaskResp row : resp.getList()) {
            row.setCustomer(customerService.get(row.getCustomerId()));
            row.setAssigneeUserName(userService.getNickName(row.getAssigneeId()));
        }

        return resp;
    }
    

    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeTask(CustomerVisitTaskCloseReq req, Long id) {

        CustomerVisitTaskDO entity = baseMapper.selectById(id);
        if (entity == null) {
            throw new BusinessException("客户回访任务不存在");
        }
        
        // 关闭任务
        entity.setCloseReason(req.getCloseReason());
        entity.setTaskStatus(VisitTaskStatusEnum.CLOSE); // 4-已关闭
        entity.setUpdateTime(LocalDateTime.now());
        
        baseMapper.updateById(entity);
    }
    
    @Override
    public boolean hasValidVisitTask(Long customerId) {
        // 查询该客户的所有回访任务
        List<CustomerVisitTaskDO> tasks = baseMapper.selectList(
            new LambdaQueryWrapper<CustomerVisitTaskDO>()
                .eq(CustomerVisitTaskDO::getCustomerId, customerId)
                .in(CustomerVisitTaskDO::getTaskStatus, 
                    VisitTaskStatusEnum.TO_DO,
                    VisitTaskStatusEnum.PENDING,
                    VisitTaskStatusEnum.FINISH)
                .orderByDesc(CustomerVisitTaskDO::getCreateTime)
        );
        
        if (tasks.isEmpty()) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime sevenDaysAgo = now.minusDays(7);
        
        for (CustomerVisitTaskDO task : tasks) {
            // 如果存在待处理或处理中的任务，直接返回true
            if (task.getTaskStatus().equals(VisitTaskStatusEnum.TO_DO) ||
                task.getTaskStatus().equals(VisitTaskStatusEnum.PENDING)) {
                log.info("客户 {} 存在待处理/处理中的回访任务，任务ID: {}", customerId, task.getId());
                return true;
            }
            
            // 如果是已完成的任务，检查是否在7天内
            if (task.getTaskStatus().equals(VisitTaskStatusEnum.FINISH)) {
                if (task.getCreateTime().isAfter(sevenDaysAgo)) {
                    log.info("客户 {} 存在7天内已完成的回访任务，任务ID: {}，创建时间: {}", 
                            customerId, task.getId(), task.getCreateTime());
                    return true;
                }
            }
        }
        
        return false;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateTasksWithTriggerInfo(List<CustomerConditionResp> customerResults,
                                              Long strategyId, 
                                              String strategyName, 
                                              LocalDateTime requiredFinishTime) {
        if (customerResults.isEmpty()) {
            log.info("客户结果列表为空，跳过批量创建回访任务");
            return 0;
        }
        

        List<CustomerVisitTaskDO> tasksToCreate = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 过滤并构建任务实体
        for (CustomerConditionResp result : customerResults) {
            Long customerId = result.getCustomerId();
            
            // 检查是否存在有效的回访任务
            if (hasValidVisitTask(customerId)) {
                log.debug("客户 {} 已存在有效的回访任务，跳过创建", customerId);
                continue;
            }
            
            // 获取客户详情
            CustomerDO customer = customerService.getById(customerId);
            if (customer == null) {
                log.warn("客户不存在，跳过创建任务，客户ID: {}", customerId);
                continue;
            }
            
            // 创建回访任务实体
            CustomerVisitTaskDO entity = new CustomerVisitTaskDO();
            entity.setCustomerId(customerId);
            entity.setStrategyId(strategyId);
            entity.setStrategyName(strategyName);
            entity.setTaskStatus(VisitTaskStatusEnum.TO_DO); // 1-待处理
            entity.setReminderTime(now.plusDays(1)); // 提醒时间为当前时间后一天
            entity.setAssigneeId(customer.getBusinessUserId()); // 设置为客户的关联商务
            entity.setRequiredFinishTime(requiredFinishTime);
            entity.setTriggerInfo(result.getConditionDescription()); // 设置触发条件信息
            entity.setCreateTime(now);
            
            tasksToCreate.add(entity);
        }
        
        if (tasksToCreate.isEmpty()) {
            log.info("所有候选客户都已存在有效回访任务或客户不存在，无需创建新任务");
            return 0;
        }
        
        // 批量插入任务
        boolean success = this.saveBatch(tasksToCreate);
        
        if (success) {
            return tasksToCreate.size();
        } else {
            throw new BusinessException("批量创建回访任务失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addVisitDetail(CustomerVisitTaskDetailReq req) {
        // 检查任务是否存在
        CustomerVisitTaskDO task = baseMapper.selectById(req.getTaskId());
        if (task == null) {
            throw new BusinessException("回访任务不存在");
        }
        
        CustomerVisitTaskDetailDO entity = new CustomerVisitTaskDetailDO();
        entity.setTaskId(req.getTaskId());
        entity.setVisitMethod(req.getVisitMethod());
        entity.setVisitTime(req.getVisitTime());
        entity.setVisitSummary(req.getVisitSummary());
        entity.setAttachmentUrls(req.getAttachmentUrls());
        entity.setCreateTime(LocalDateTime.now());
        
        visitTaskDetailMapper.insert(entity);
        

        task.setTaskStatus(req.getTaskStatus());
        task.setReminderTime(req.getNextReminderTime());
        task.setVisitResult(req.getVisitResult());
        task.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(task);
        return entity.getId();
    }

    @Override
    public void updateVisitDetail(CustomerVisitTaskDetailReq req, Long id) {
        CustomerVisitTaskDetailDO entity = visitTaskDetailMapper.selectById(id);
        if (entity == null) {
            throw new BusinessException("回访记录不存在");
        }

        CustomerVisitTaskDetailDO update = new CustomerVisitTaskDetailDO();
        BeanUtil.copyProperties(req, update);
        update.setId(id);
        update.setUpdateTime(LocalDateTime.now());
        visitTaskDetailMapper.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVisitDetail(Long id) {
        CustomerVisitTaskDetailDO entity = visitTaskDetailMapper.selectById(id);
        if (entity == null) {
            throw new BusinessException("回访明细不存在");
        }
        
        visitTaskDetailMapper.deleteById(id);
    }
    
    @Override
    public List<CustomerVisitTaskDetailsResp> getVisitDetailsByTaskId(Long taskId) {
        List<CustomerVisitTaskDetailDO> details = visitTaskDetailMapper.selectByTaskId(taskId);
        return details.stream()
                .map(detail -> {
                    CustomerVisitTaskDetailsResp resp = new CustomerVisitTaskDetailsResp();
                    BeanUtil.copyProperties(detail, resp);
                    return resp;
                })
                .collect(Collectors.toList());
    }
    

}