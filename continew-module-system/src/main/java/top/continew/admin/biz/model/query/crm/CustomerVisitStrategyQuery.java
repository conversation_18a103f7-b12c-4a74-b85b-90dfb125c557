package top.continew.admin.biz.model.query.crm;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 客户回访策略查询条件
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Data
@Schema(description = "客户回访策略查询条件")
public class CustomerVisitStrategyQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 策略名称
     */
    @Schema(description = "策略名称")
    @Query(type = QueryType.LIKE)
    private String strategyName;

    /**
     * 策略状态：0-禁用，1-启用
     */
    @Schema(description = "策略状态：0-禁用，1-启用")
    @Query(type = QueryType.EQ)
    private Integer strategyStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.EQ)
    private LocalDateTime createTime;
}