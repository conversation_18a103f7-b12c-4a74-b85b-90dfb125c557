package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.ProfitTypeDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.ProfitTypeQuery;
import top.continew.admin.biz.model.req.ProfitTypeReq;
import top.continew.admin.biz.model.resp.ProfitTypeDetailResp;
import top.continew.admin.biz.model.resp.ProfitTypeResp;

/**
 * 利润类型业务接口
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
public interface ProfitTypeService extends BaseService<ProfitTypeResp, ProfitTypeDetailResp, ProfitTypeQuery, ProfitTypeReq>, IService<ProfitTypeDO> {}