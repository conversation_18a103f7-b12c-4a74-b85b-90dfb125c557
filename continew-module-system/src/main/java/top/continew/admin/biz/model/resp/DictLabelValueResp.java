package top.continew.admin.biz.model.resp;

import lombok.Data;
import org.apache.poi.ss.formula.functions.T;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;

/**
 * 字典的响应
 * @version: 1.00.00
 * @description:
 * @date: 2025/5/22 16:13
 */
@Data
public class DictLabelValueResp<T> extends LabelValueResp<T> {
    private Long id;

    public DictLabelValueResp(String label, T value, Object extra) {
        super(label, value, extra);
    }
}
