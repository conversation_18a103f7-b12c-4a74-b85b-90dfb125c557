/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.enums.CustomerStatusEnum;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.admin.biz.enums.OpportunityStatusEnum;
import top.continew.admin.biz.event.CustomerBalanceChangeEvent;
import top.continew.admin.biz.event.CustomerBalanceChangeModel;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.crm.OpportunityDO;
import top.continew.admin.biz.model.req.crm.OpportunityFollowReq;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.crm.OpportunityService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
@RequiredArgsConstructor
public class CustomerListener {
    private final OpportunityService opportunityService;
    private final CustomerService customerService;

    @Async
    @EventListener
    public void balanceChange(CustomerBalanceChangeEvent event) {
        CustomerBalanceChangeModel model = (CustomerBalanceChangeModel)event.getSource();
        String message = model.getCustomer().getName() + "\n";
        message += BotUtils.formatStatAmountMsg(model.getTransferBefore(), model.getChangeTransfer(), model.getBalanceBefore(), model.getChangeBalance());
        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
            .chatId(model.getCustomer().getTelegramChatId())
            .text(message)
            .build()));

        updateCustomerWinStatus(model);

    }



    /**
     * 判断客户关联的商机 是否未赢单，如果是则将商机状态改为已跟进
     *
     * @param model
     */
    private void updateCustomerWinStatus(CustomerBalanceChangeModel model) {
        //如果是客户打款
        if (Objects.equals(model.getAction(), CustomerBalanceTypeEnum.SYSTEM.getValue())) {
            //客户是潜在客户，则需要更新相关的商机以及客户类型
            if(CustomerTypeEnum.POTENTIAL.equals(model.getCustomer().getType())) {
                List<OpportunityDO> opportunityList = opportunityService.listCustomerOpportunity(model.getCustomer().getId());

                if (CollUtil.isEmpty(opportunityList)) {
                    CustomerDO updateCustomer = new CustomerDO();
                    updateCustomer.setId(model.getCustomer().getId());
                    updateCustomer.setType(CustomerTypeEnum.FORMAL);
                    updateCustomer.setStatus(CustomerStatusEnum.NORMAL);
                    updateCustomer.setCooperateTime(null != model.getTransferTime() ? model.getTransferTime() : LocalDateTime.now());
                    customerService.updateById(updateCustomer);

                }else {
                    // 获取最后一个非赢单的商机
                    OpportunityDO lastOpportunity = opportunityList.stream()
                            .filter(opportunity -> !OpportunityStatusEnum.WON.equals(opportunity.getStatus()))
                            .reduce((first, second) -> second)
                            .orElse(null);
                    if (null != lastOpportunity) {
                        // 更新商机状态为已跟进
                        OpportunityFollowReq req = new OpportunityFollowReq();
                        req.setStatus(OpportunityStatusEnum.WON);
                        req.setFollowTime(null != model.getTransferTime() ? model.getTransferTime() : LocalDateTime.now());
                        req.setContent("客户已下户，商机赢单");
                        req.setFollowUserId(0L);
                        req.setOpportunityId(lastOpportunity.getId());
                        opportunityService.addFollow(req);
                    }
                }

            }else {
                if(CustomerStatusEnum.TERMINATED.equals(model.getCustomer().getStatus())) {
                    //如果客户是正式客户，且状态是终止，则要进行激活为正常
                    CustomerDO updateCustomer = new CustomerDO();
                    updateCustomer.setId(model.getCustomer().getId());
                    updateCustomer.setStatus(CustomerStatusEnum.NORMAL);
                    //TODO  激活时间
                    customerService.updateById(updateCustomer);
                }
            }
        }

    }


}
