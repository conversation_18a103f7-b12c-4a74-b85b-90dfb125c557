package top.continew.admin.biz.service.impl;

import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.MappingType;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.MaterialMapper;
import top.continew.admin.biz.model.entity.MaterialDO;
import top.continew.admin.biz.model.query.MaterialQuery;
import top.continew.admin.biz.model.query.MaterialStatQuery;
import top.continew.admin.biz.model.req.MaterialReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.MaterialService;
import top.continew.admin.common.constant.ContainerConstants;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物料业务实现
 *
 * <AUTHOR>
 * @since 2025/01/21 16:20
 */
@Service
@RequiredArgsConstructor
public class MaterialServiceImpl extends BaseServiceImpl<MaterialMapper, MaterialDO, MaterialResp, MaterialDetailResp, MaterialQuery, MaterialReq> implements MaterialService {

    @Override
    public PageResp<MaterialResp> page(MaterialQuery query, PageQuery pageQuery) {
        QueryWrapper<MaterialDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, pageQuery);
        IPage<MaterialResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        page.getRecords().forEach(this::fill);
        return PageResp.build(page);
    }

    @Override
    protected <E> List<E> list(MaterialQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<MaterialDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        List<MaterialResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        entityList.forEach(this::fill);
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    public Long add(MaterialReq req) {
        MaterialDO materialDO = new MaterialDO();
        BeanUtil.copyProperties(req, materialDO);
        materialDO.setPayDate(req.getPayDate() == null ? LocalDateTime.now() : req.getPayDate());
        save(materialDO);
        return materialDO.getId();
    }

    @Override
    @ContainerMethod(namespace = ContainerConstants.MATERIAL_UNIT_PRICE_CAL, type = MappingType.ORDER_OF_KEYS)
    public BigDecimal calculateUnitPrice(MaterialResp materialResp) {
        if (materialResp.getNum() != null && materialResp.getNum() != 0 && materialResp.getPayPrice() != null) {
            return materialResp.getPayPrice().divide(new BigDecimal(materialResp.getNum()), 2, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    @Override
    public PageResp<MaterialStatByDateResp> selectStatByDatePage(MaterialStatQuery query, PageQuery pageQuery) {
        IPage<MaterialStatByDateResp> page = this.baseMapper.selectStatByDatePage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<MaterialStatByDateResp> selectStatByDateList(MaterialStatQuery query) {
        return this.baseMapper.selectStatByDateList(query);
    }

    @Override
    public List<MaterialStatByTypeResp> selectStatByTypeList(MaterialStatQuery query, String[] sort) {
        return this.baseMapper.selectStatByTypeList(query, sort);
    }

}