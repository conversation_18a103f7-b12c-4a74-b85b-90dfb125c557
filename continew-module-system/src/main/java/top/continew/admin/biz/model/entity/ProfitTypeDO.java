package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.annotation.DictField;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 利润类型实体
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Data
@TableName("biz_profit_type")
@DictField
public class ProfitTypeDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 媒体平台
     */
    private Integer adPlatform;
    /**
     * 名称
     */
    private String name;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;
}