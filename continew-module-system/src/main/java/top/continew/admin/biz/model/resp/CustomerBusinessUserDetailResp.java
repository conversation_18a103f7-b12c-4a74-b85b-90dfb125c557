package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 客户商务对接详情信息
 *
 * <AUTHOR>
 * @since 2025/08/05 15:35
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "客户商务对接详情信息")
public class CustomerBusinessUserDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID，用于关联客户信息表
     */
    @Schema(description = "客户ID，用于关联客户信息表")
    @ExcelProperty(value = "客户ID，用于关联客户信息表")
    private Long customerId;

    /**
     * 关联的商务用户ID
     */
    @Schema(description = "关联的商务用户ID")
    @ExcelProperty(value = "关联的商务用户ID")
    private Long userId;
}