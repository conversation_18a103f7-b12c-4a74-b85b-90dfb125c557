/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.CampaignInsightDO;

import java.time.LocalDate;
import java.util.List;

/**
 * 广告组成效数据 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/03 14:26
 */
public interface CampaignInsightMapper extends BaseMapper<CampaignInsightDO> {

    /**
     * 批量插入或更新广告组成效数据
     *
     * @param insights 成效数据列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("insights") List<CampaignInsightDO> insights);

    /**
     * 根据广告户ID和统计日期删除过期数据
     *
     * @param adAccountId 广告户ID
     * @param statDate    统计日期
     * @return 删除行数
     */
    int deleteByAdAccountIdAndStatDateNot(@Param("adAccountId") String adAccountId, @Param("statDate") LocalDate statDate);
}