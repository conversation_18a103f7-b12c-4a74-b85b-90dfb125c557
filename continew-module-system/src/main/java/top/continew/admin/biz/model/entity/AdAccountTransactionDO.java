package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 广告户交易记录实体
 *
 * <AUTHOR>
 * @since 2025/03/10 14:15
 */
@Data
@TableName("biz_ad_account_transaction")
public class AdAccountTransactionDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户ID
     */
    private String platformAdId;

    /**
     * 交易时间
     */
    private LocalDateTime transTime;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易状态
     */
    private String status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}