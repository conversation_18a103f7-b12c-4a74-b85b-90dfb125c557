package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.query.CustomerWithdrawOrderQuery;
import top.continew.admin.biz.model.resp.CustomerWithdrawOrderResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.CustomerWithdrawOrderDO;

import java.math.BigDecimal;

/**
* 客户余额提现订单 Mapper
*
* <AUTHOR>
* @since 2025/01/22 14:23
*/
public interface CustomerWithdrawOrderMapper extends BaseMapper<CustomerWithdrawOrderDO> {
    IPage<CustomerWithdrawOrderResp> selectCustomPage(Page page, @Param("query") CustomerWithdrawOrderQuery query);

    BigDecimal getBalance(@Param("id")Long id);
}