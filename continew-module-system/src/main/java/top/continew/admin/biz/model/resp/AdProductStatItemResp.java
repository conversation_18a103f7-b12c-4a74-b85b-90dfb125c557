package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 投放日报明细信息
 *
 * <AUTHOR>
 * @since 2025/08/13 21:02
 */
@Data
@Schema(description = "投放日报明细信息")
public class AdProductStatItemResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    private String platformAdId;

    /**
     * 消耗
     */
    @Schema(description = "消耗")
    private BigDecimal spend;

    /**
     * 回流
     */
    @Schema(description = "回流")
    private BigDecimal reflowSpend;
}