package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.SettleOrderItemMapper;
import top.continew.admin.biz.model.entity.SettleOrderItemDO;
import top.continew.admin.biz.model.query.SettleOrderItemQuery;
import top.continew.admin.biz.model.req.SettleOrderItemReq;
import top.continew.admin.biz.model.resp.SettleOrderItemDetailResp;
import top.continew.admin.biz.model.resp.SettleOrderItemResp;
import top.continew.admin.biz.service.SettleOrderItemService;

/**
 * 结算订单详情业务实现
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Service
@RequiredArgsConstructor
public class SettleOrderItemServiceImpl extends BaseServiceImpl<SettleOrderItemMapper, SettleOrderItemDO, SettleOrderItemResp, SettleOrderItemDetailResp, SettleOrderItemQuery, SettleOrderItemReq> implements SettleOrderItemService {}