/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 广告户每日消耗实体
 *
 * <AUTHOR>
 * @since 2025/01/14 14:43
 */
@Data
@TableName("biz_ad_account_insight")
public class AdAccountInsightDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long customerId;

    /**
     * 广告户
     */
    private String adAccountId;

    /**
     * 统计时间
     */
    private LocalDate statDate;

    /**
     * 消耗
     */
    private BigDecimal spend;

    /**
     * 关联商务用户ID
     */
    private Long businessUserId;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}