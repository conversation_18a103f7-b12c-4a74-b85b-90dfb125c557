/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import jakarta.validation.ValidationException;
import org.apache.commons.lang3.StringUtils;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.admin.biz.model.entity.SalesDailySummaryDO;
import top.continew.admin.biz.model.req.SalesDailySummaryReq;
import top.continew.admin.biz.robot.annotation.TelegramBotFormItem;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.core.exception.BusinessException;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class BotUtils {

    public static <T> T parseBotMsgToObject(String text, String lineSeparator, String valueSeparator, Class<T> clazz) {
        text = text.replace("：", ":").replace("\u00A0", "");
        String[] lines = StringUtils.split(text, lineSeparator);
        Field[] fields = clazz.getDeclaredFields();
        Map<String, String> valueMap = new HashMap<>();
        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            String label = StringUtils.substringBefore(line, valueSeparator).trim();
            if (StringUtils.isBlank(label)) {
                continue;
            }
            String value = StringUtils.substringAfter(line, valueSeparator).trim();
            valueMap.put(label, value);
        }

        try {
            T object = clazz.getDeclaredConstructor().newInstance();
            for (Field field : fields) {
                TelegramBotFormItem annotation = field.getAnnotation(TelegramBotFormItem.class);
                if (annotation != null && valueMap.containsKey(annotation.label())) {
                    field.setAccessible(true);
                    if (annotation.required() && StringUtils.isBlank(valueMap.get(annotation.label()))) {
                        throw new ValidationException("请输入" + annotation.label());
                    }
                    if (field.getType() == Long.class) {
                        field.set(object, Long.parseLong(valueMap.get(annotation.label())));
                    } else if (field.getType() == Integer.class) {
                        field.set(object, Integer.parseInt(valueMap.get(annotation.label())));
                    } else if (field.getType() == Double.class) {
                        field.set(object, Double.parseDouble(valueMap.get(annotation.label())));
                    } else if (field.getType() == Float.class) {
                        field.set(object, Float.parseFloat(valueMap.get(annotation.label())));
                    } else if (field.getType() == Boolean.class) {
                        field.set(object, Boolean.parseBoolean(valueMap.get(annotation.label())));
                    } else if (field.getType() == BigDecimal.class) {
                        field.set(object, new BigDecimal(valueMap.get(annotation.label())));
                    } else {
                        field.set(object, valueMap.get(annotation.label()));
                    }
                }
            }
            return object;
        } catch (InvocationTargetException | InstantiationException | IllegalAccessException |
            NoSuchMethodException e) {
            throw new BusinessException("数据解析失败，请检查数据格式是否正确");
        }
    }

    public static String parseObjToMsg(String title, Class<?> clazz, boolean ignore) {
        Field[] fields = clazz.getDeclaredFields();
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(title)) {
            stringBuilder.append(title).append("\n");
        }
        for (Field field : fields) {
            TelegramBotFormItem annotation = field.getAnnotation(TelegramBotFormItem.class);
            if (annotation != null) {
                if (!ignore || !annotation.ignored()) {
                    stringBuilder.append(annotation.label()).append(":");
                    if (StringUtils.isNotBlank(annotation.desc())) {
                        stringBuilder.append(annotation.desc());
                    }
                    stringBuilder.append("\n");
                }
            }
        }
        return stringBuilder.toString();
    }

    public static String formatStatAmountMsg(BigDecimal transferBefore,
                                             BigDecimal changeTransfer,
                                             BigDecimal balanceBefore,
                                             BigDecimal changeBalance) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("总打款：").append(NumberUtil.toStr(transferBefore));
        if (changeTransfer != null) {
            stringBuilder.append(changeTransfer.compareTo(BigDecimal.ZERO) < 0 ? "-" : "+")
                .append(NumberUtil.toStr(changeTransfer.abs()))
                .append("=")
                .append(NumberUtil.toStr(transferBefore.add(changeTransfer)));
        }
        stringBuilder.append("\n");
        stringBuilder.append("余额：").append(NumberUtil.toStr(balanceBefore));
        if (changeBalance != null) {
            stringBuilder.append(changeBalance.compareTo(BigDecimal.ZERO) < 0 ? "-" : "+")
                .append(NumberUtil.toStr(changeBalance.abs()))
                .append("=")
                .append(NumberUtil.toStr(balanceBefore.add(changeBalance)));
        }
        return stringBuilder.toString();
    }

    public static boolean isFinancialUser(String userId) {
        List<String> FinancialUser = List.of("hans0102", "Reallyee", "agony9445");
        return FinancialUser.contains(userId);
    }

    public static String createSaltAdAccountPayFailedMessage(String adId,
                                                             String customerName,
                                                             String cardNumber,
                                                             String failureReason,
                                                             String deductionAmount,
                                                             String fbBalance,
                                                             String cardBalance) {
        return "🚨 *扣款失败提醒* 🚨\n\n" + "🔍 **广告户 ID**: `" + adId + "`\n" + "👤 **所属客户**: *" + CommonUtils.escapeMarkdown(customerName) + "*\n" + "🏦 **扣款卡号**: `" + cardNumber + "`\n" + "💲 **扣款金额**: *$" + CommonUtils.escapeMarkdown(deductionAmount) + "*\n" + "💵 **FB 余额**: *$" + CommonUtils.escapeMarkdown(fbBalance) + "*\n" + "💳 **卡片余额**: *$" + CommonUtils.escapeMarkdown(cardBalance) + "*\n" + "⚠️ **失败原因**: _" + CommonUtils.escapeMarkdown(failureReason) + "_\n\n" + "请及时处理以避免广告暂停！";
    }

    public static String createUnSaltAdAccountTransAmountRevokeTooFastMessage(String adId,
                                                                           String customerName,
                                                                           BigDecimal transAmount) {
        return "🚨 *交易金额异常提醒* 🚨\n\n" + "🔍 **广告户 ID**: `" + adId + "`\n" + "👤 **所属客户**: *" + CommonUtils.escapeMarkdown(customerName) + "*\n" + "💲 **退回金额**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(transAmount)) + "*\n" + "⚠️ **异常原因**: _" + CommonUtils.escapeMarkdown("短时间内交易发生退回") + "_\n\n" + "请及时检查广告户是否出现资金冻结问题！";
    }

    public static String createUnSaltAdAccountTransAmountUnusualMessage(String adId,
                                                                        String failureReason,
                                                                        BigDecimal transAmount,
                                                                        BigDecimal billingThreshold) {
        return "🚨 *交易金额异常提醒* 🚨\n\n" + "🔍 **广告户 ID**: `" + adId + "`\n" + "💲 **扣款金额**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(transAmount)) + "*\n" + "💵 **付费门槛**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(billingThreshold)) + "*\n" + "⚠️ **异常原因**: _" + CommonUtils.escapeMarkdown(failureReason) + "_\n\n" + "请及时处理以避免盗刷损失！如果误判，请前往广告户列表恢复可用。";
    }

    public static String createSaltAdAccountTransAmountUnusualMessage(String adId,
                                                                      String customerName,
                                                                      String failureReason,
                                                                      BigDecimal transAmount,
                                                                      BigDecimal billingThreshold) {
        return "🚨 *交易金额异常提醒* 🚨\n\n" + "🔍 **广告户 ID**: `" + adId + "`\n" + "👤 **所属客户**: *" + CommonUtils.escapeMarkdown(customerName) + "*\n" + "💲 **扣款金额**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(transAmount)) + "*\n" + "💵 **付费门槛**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(billingThreshold)) + "*\n" + "⚠️ **异常原因**: _" + CommonUtils.escapeMarkdown(failureReason) + "_\n\n" + "请及时处理以避免盗刷损失！";
    }

    public static String createSaltAdAccountThresholdAmountTooLargeMessage(String adId,
                                                                           String customerName,
                                                                           String failureReason,
                                                                           BigDecimal transAmount,
                                                                           BigDecimal billingThreshold) {
        return "🚨 *交易金额异常提醒* 🚨\n\n" + "🔍 **广告户 ID**: `" + adId + "`\n" + "👤 **所属客户**: *" + CommonUtils.escapeMarkdown(customerName) + "*\n" + "💲 **扣款金额**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(transAmount)) + "*\n" + "💵 **付费门槛**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(billingThreshold)) + "*\n" + "⚠️ **异常原因**: _" + CommonUtils.escapeMarkdown(failureReason) + "_\n\n" + "请及时调低广告户付费门槛！";
    }

    public static String createInsufficientBalanceMessage(String adId, String customerName, String fbBalance) {
        return "🚨 *余额不足提醒* 🚨\n\n" + "🔍 **广告户 ID**: `" + adId + "`\n" + "👤 **所属客户**: *" + customerName + "*\n" + "💵 **FB 余额**: *$" + fbBalance + "*\n\n" + "请及时提醒客户充值！";
    }

    public static String createPrepayBalanceMessage(String adId,
                                                    String customerName,
                                                    BigDecimal recentSpent,
                                                    BigDecimal remainingRecharge,
                                                    BigDecimal remainingBalance,
                                                    String text) {
        return "🚨 *充值金不足提醒* 🚨\n\n" + "🔍 **广告户 ID**: `" + adId + "`\n" + "👤 **所属客户**: *" + CommonUtils.escapeMarkdown(customerName) + "*\n" + "⏳ **近期消耗**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(recentSpent)) + "*\n" + "💰 **剩余充值金**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(remainingRecharge)) + "*\n" + "💵 **剩余余额**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(remainingBalance)) + "*\n\n" + CommonUtils.escapeMarkdown(text);
    }

    public static String createBMAdminAddMessage(String bmId,
                                                 String userName,
                                                 String userEmail,
                                                 String channelName,
                                                 String accessTime) {
        return "📣 *BM新增管理员提醒* 📣\n\n" + "🔍 **BM ID**: `" + bmId + "`\n" + "📺 **渠道名称**: *" + CommonUtils.escapeMarkdown(channelName) + "*\n" + "🕒 **接入时间**: *" + CommonUtils.escapeMarkdown(accessTime) + "*\n" + "👤 **用户名称**: *" + CommonUtils.escapeMarkdown(userName) + "*\n" + "📧 **用户邮箱**: *" + CommonUtils.escapeMarkdown(userEmail) + "*\n\n" + "请注意新管理员的权限设置！如果误判，请前往广告户列表恢复可用。";
    }

    public static String createBMPermissionLostMessage(String bmId, String channelName, String accessTime) {
        return "🚫 *BM权限移除提醒* 🚫\n\n" + "🔍 **BM ID**: `" + bmId + "`\n" + "📺 **渠道名称**: *" + CommonUtils.escapeMarkdown(channelName) + "*\n" + "🕒 **接入时间**: *" + CommonUtils.escapeMarkdown(accessTime) + "*\n\n" + "该BM权限已被移除，请及时检查！如果误判，请前往广告户列表恢复可用。";
    }

    public static String createAdPermissionLostMessage(String bmId, String customerName) {
        return "🚫 *广告户权限移除提醒* 🚫\n\n" + "🔍 **广告户 ID**: `" + bmId + "`\n" + "👤 **所属客户**: *" + CommonUtils.escapeMarkdown(customerName) + "*\n\n" + "广告户权限已被移除，请及时检查！如果误判，请前往广告户列表恢复可用。";
    }

    public static String createAdSpentCapLostMessage(String bmId, String customerName) {
        return "🚫 *广告户限额移除提醒* 🚫\n\n" + "🔍 **广告户 ID**: `" + bmId + "`\n" + "👤 **所属客户**: *" + CommonUtils.escapeMarkdown(customerName) + "*\n\n" + "广告户限额已被移除，请及时检查！如果误判，请前往广告户列表恢复可用。";
    }

    public static String createAdAccountTransExcessiveMessage(String adId,
                                                              String customerName,
                                                              String exceptionSituation,
                                                              List<CardTransactionDO> transactionRecords) {
        StringBuilder message = new StringBuilder();
        message.append("🚨 *交易频繁提醒* 🚨\n\n")
            .append("🔍 **广告户 ID**: `")
            .append(adId)
            .append("`\n")
            .append("👤 **所属客户**: *")
            .append(CommonUtils.escapeMarkdown(customerName))
            .append("*\n")
            .append("⚠️ **异常情况**: _")
            .append(CommonUtils.escapeMarkdown(exceptionSituation))
            .append("_\n\n")
            .append("➖➖➖➖➖➖➖➖➖➖➖➖➖\n\n")
            .append("📊 **交易记录**:\n");

        for (CardTransactionDO record : transactionRecords) {
            String transTime = LocalDateTimeUtil.format(record.getChinaTime(), "HH:mm:ss");
            message.append(" \\- ")
                .append("时间: %s, 金额: $%s, 状态: %s".formatted(CommonUtils.escapeMarkdown(transTime), CommonUtils.escapeMarkdown(NumberUtil.toStr(record.getTransAmount()
                    .abs())), record.getTransStatus().getDescription()))
                .append("\n");
        }

        message.append("\n请及时检查账户以避免损失！");
        return message.toString();
    }

    public static String createSpentCapMismatchMessage(String platformAdId,
                                                       BigDecimal spentCap,
                                                       BigDecimal rechargeAmount) {
        return "⚠️ *限额不匹配提醒* ⚠️\n\n" + "🔍 **广告户 ID**: `" + platformAdId + "`\n" + "💼 **FB 限额**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(spentCap)) + "*\n" + "💰 **充值金额**: *$" + CommonUtils.escapeMarkdown(NumberUtil.toStr(rechargeAmount)) + "*\n\n" + "请及时检测广告户限额是否设置正确";
    }

    /**
     * 解析商务日报文本为对象
     * 支持多行内容字段
     */
    public static SalesDailySummaryDO parseDailyReportToObject(String text) {
        text = text.replace("：", ":").replace("\u00A0", "");
        String[] lines = text.split("\n");

        SalesDailySummaryDO report = new SalesDailySummaryDO();
        String currentField = null;
        StringBuilder contentBuilder = new StringBuilder();

        for (String line : lines) {
            line = line.trim();
            if (StringUtils.isBlank(line)) {
                if ("内容".equals(currentField)) {
                    contentBuilder.append("\n");
                }
                continue;
            }

            if (line.startsWith("日期:")) {
                currentField = "日期";
                String dateStr = line.substring(3).trim();
                report.setRecordDate(LocalDate.parse(dateStr));
            } else if (line.startsWith("内容:")) {
                currentField = "内容";
                contentBuilder = new StringBuilder(line.substring(3).trim());
            } else if ("内容".equals(currentField)) {
                // 继续添加内容
                if (!contentBuilder.isEmpty()) {
                    contentBuilder.append("\n");
                }
                contentBuilder.append(line);
            }
        }

        if (!contentBuilder.isEmpty()) {
            report.setContent(contentBuilder.toString());
        }

        return report;
    }
}
