package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.CustomerWithdrawOrderStatusEnum;
import top.continew.admin.common.base.BaseResp;
import top.continew.starter.extension.crud.constant.ContainerPool;

/**
 * 客户余额提现订单信息
 *
 * <AUTHOR>
 * @since 2025/01/22 14:23
 */
@Data
@Schema(description = "客户余额提现订单信息")
public class CustomerWithdrawOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 订单状态（1:审核中, 2:审核通过, 3:审核拒绝, 4:退款完成, 5:已取消）
     */
    @Schema(description = "订单状态（1:审核中, 2:审核通过, 3:审核拒绝, 4:退款完成, 5:已取消）")
    private Integer status;

    /**
     * 关联客户ID
     */
    @Schema(description = "关联客户ID")
    private Long customerId;

    private String customerName;

    /**
     * 预计退款时间
     */
    @Schema(description = "预计退款时间")
    private LocalDateTime expectedRefundTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 审核人
     */
    @Schema(description = "审核人")
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "auditorName"))
    private Long auditor;

    @Schema(description = "审核人")
    private String auditorName;

    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    private String auditRemark;

    /**
     * 实际退款时间
     */
    @Schema(description = "实际退款时间")
    private LocalDateTime actualRefundTime;

    /**
     * 实际退款金额
     */
    @Schema(description = "实际退款金额")
    private BigDecimal actualRefundAmount;

    private BigDecimal customerBalance;
}