/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import java.time.*;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 广告户系列消耗查询条件
 *
 * <AUTHOR>
 * @since 2025/01/03 15:08
 */
@Data
public class CampaignInsightQuery {

    @NotNull(message = "广告账户ID不能为空")
    private Long adAccountId;

    private Long current;

    private Long pageSize;

}