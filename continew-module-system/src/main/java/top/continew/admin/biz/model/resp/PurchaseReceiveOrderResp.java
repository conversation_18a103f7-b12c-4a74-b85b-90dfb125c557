package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.PurchaseOrderTypeEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 采购验收单信息
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@Schema(description = "采购验收单信息")
public class PurchaseReceiveOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联采购单
     */
    @Schema(description = "关联采购单")
    private Long purchaseOrderId;

    private Long channelId;

    private String channelName;

    private PurchaseOrderTypeEnum type;

    /**
     * 验收数量
     */
    @Schema(description = "验收数量")
    private Integer receiveNum;

    private BigDecimal receivePrice;

    private LocalDateTime receiveTime;

    private Integer purchaseNum;

    private BigDecimal purchasePrice;
}