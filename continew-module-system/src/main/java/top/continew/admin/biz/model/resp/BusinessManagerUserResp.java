package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.LocalDateTime;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.common.base.BaseResp;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * bm管理员信息
 *
 * <AUTHOR>
 * @since 2025/04/22 16:45
 */
@Data
@Schema(description = "bm管理员信息")
public class BusinessManagerUserResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * BM ID
     */
    @Schema(description = "BM ID")
    private String bmId;

    /**
     * USER ID
     */
    @Schema(description = "USER ID")
    private String userId;


    private String typeName;


    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String userEmail;

    /**
     * 用户权限
     */
    @Schema(description = "用户权限")
    private String userRole;

    /**
     * 是否移除
     */
    @Schema(description = "是否移除")
    private Boolean isRemove;

    private String bmBrowser;

    private BusinessManagerStatusEnum bmStatus;

    private Boolean isSelfUser;

    private LocalDateTime bmCreateTime;

    private String channelName;
}