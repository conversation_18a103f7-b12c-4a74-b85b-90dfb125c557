package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.CustomerRequirementStatusEnum;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.req.AdAccountOrderAddReq;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.ProfitTypeService;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.CustomerRequirementMapper;
import top.continew.admin.biz.model.query.CustomerRequirementQuery;
import top.continew.admin.biz.model.req.CustomerRequirementReq;
import top.continew.admin.biz.model.resp.CustomerRequirementDetailResp;
import top.continew.admin.biz.model.resp.CustomerRequirementResp;
import top.continew.admin.biz.service.CustomerRequirementService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 客户需求业务实现
 *
 * <AUTHOR>
 * @since 2025/02/20 14:03
 */
@Service
@RequiredArgsConstructor
public class CustomerRequirementServiceImpl extends BaseServiceImpl<CustomerRequirementMapper, CustomerRequirementDO, CustomerRequirementResp, CustomerRequirementDetailResp, CustomerRequirementQuery, CustomerRequirementReq> implements CustomerRequirementService {
    private final UserService userService;
    private final AdAccountOrderService adAccountOrderService;
    private final TelegramChatIdConfig telegramChatIdConfig;
    private final CustomerService customerService;
    private final ProfitTypeService profitTypeService;

    @Override
    public List<Map<String, Object>> getSummary(CustomerRequirementQuery query) {
        return baseMapper.getSummary(query);
    }


    @Override
    public PageResp<CustomerRequirementResp> page(CustomerRequirementQuery query, PageQuery pageQuery) {
        QueryWrapper<CustomerRequirementDO> queryWrapper = this.buildQueryWrapper(query);
        this.sort(queryWrapper, pageQuery);
        List<ProfitTypeDO> profitTypeDOList = profitTypeService.list();
        IPage<CustomerRequirementResp> page = baseMapper.selectPage(new Page((long) pageQuery.getPage(), (long) pageQuery.getSize()), queryWrapper);
        PageResp<CustomerRequirementResp> pageResp = PageResp.build(page, this.getListClass());
        pageResp.getList().forEach(row -> {
            fill(row);
            if (row.getHandleUser() > 0L) {
                userService.getOptById(row.getHandleUser()).ifPresent(user -> row.setHandleUserString(user.getNickname()));
            }

            List<AdAccountOrderDO> orders = adAccountOrderService.list(new LambdaQueryWrapper<AdAccountOrderDO>().eq(AdAccountOrderDO::getCustomerRequirementId, row.getId()).select(AdAccountOrderDO::getOrderNo));

            if (CollUtil.isNotEmpty(orders)) {
                row.setOrderNos(orders.stream().map(AdAccountOrderDO::getOrderNo).toList());
            }

            CustomerDO customer = customerService.getById(row.getCustomerId());
            if(customer != null) {
                row.setCustomerBalance(customer.getBalance());
            }
            profitTypeDOList.stream()
                .filter(v -> v.getId().equals(row.getBmType()))
                .findFirst()
                .ifPresent(typeDO -> row.setTypeName(typeDO.getName()));
        });


        return pageResp;
    }

    @Override
    public void accept(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        for (Long id : ids) {
            CustomerRequirementDO requirement = baseMapper.selectById(id);
            if (requirement == null) {
                throw new BusinessException("客户需求不存在");
            }
            if (!CustomerRequirementStatusEnum.WAIT.equals(requirement.getStatus())) {
                throw new BusinessException("客户需求不是待处理状态");
            }
        }

        // 批量更新处理人
        LambdaUpdateWrapper<CustomerRequirementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CustomerRequirementDO::getId, ids)
                .set(CustomerRequirementDO::getStatus, CustomerRequirementStatusEnum.HANDLING)
                .set(CustomerRequirementDO::getAcceptTime, LocalDateTime.now())
                .set(CustomerRequirementDO::getHandleUser, UserContextHolder.getUserId());

        baseMapper.update(null, wrapper);
    }

    @Override
    public void finish(Long id, Integer finishQuantity) {
        CustomerRequirementDO requirement = baseMapper.selectById(id);
        if (requirement == null) {
            throw new BusinessException("客户需求不存在");
        }
        if (!CustomerRequirementStatusEnum.INVITED.equals(requirement.getStatus())) {
            throw new BusinessException("客户需求不是已邀请状态");
        }
        if (finishQuantity == null) {
            throw new BusinessException("请填写完成数量");
        }

        LambdaUpdateWrapper<CustomerRequirementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CustomerRequirementDO::getId, id)
                .set(CustomerRequirementDO::getStatus, CustomerRequirementStatusEnum.FINISH)
                .set(CustomerRequirementDO::getHandleTime, LocalDateTime.now())
                .set(CustomerRequirementDO::getFinishQuantity, finishQuantity);

        baseMapper.update(null, wrapper);
    }

    @Override
    public void cancel(Long id, String cancelReason) {
        CustomerRequirementDO requirement = baseMapper.selectById(id);
        if (requirement == null) {
            throw new BusinessException("客户需求不存在");
        }
        if (!CustomerRequirementStatusEnum.INVITED.equals(requirement.getStatus()) && !CustomerRequirementStatusEnum.HANDLING.equals(requirement.getStatus()) && !CustomerRequirementStatusEnum.PENDING.equals(requirement.getStatus())) {
            throw new BusinessException("客户需求不是已邀请、处理中、待接单状态");
        }

        cancelReason = null == cancelReason ? StrUtil.EMPTY : cancelReason;

        LambdaUpdateWrapper<CustomerRequirementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CustomerRequirementDO::getId, id)
                .set(requirement.getStatus() == CustomerRequirementStatusEnum.INVITED || requirement.getStatus() == CustomerRequirementStatusEnum.HANDLING, CustomerRequirementDO::getStatus, CustomerRequirementStatusEnum.WAIT)
                .set(requirement.getStatus() == CustomerRequirementStatusEnum.PENDING, CustomerRequirementDO::getStatus, CustomerRequirementStatusEnum.CANCEL)
                .set(CustomerRequirementDO::getHandleUser, 0L)
                .set(CustomerRequirementDO::getCancelReason, cancelReason);

        baseMapper.update(null, wrapper);
    }

    @Override
    protected void afterUpdate(CustomerRequirementReq req, CustomerRequirementDO entity) {
        if (entity.getStatus() == CustomerRequirementStatusEnum.PENDING) {
            lambdaUpdate().set(CustomerRequirementDO::getStatus, CustomerRequirementStatusEnum.WAIT)
                    .eq(CustomerRequirementDO::getId, entity.getId())
                    .update();
        }
    }

    @Override
    public void invited(Long id) {
        CustomerRequirementDO requirement = baseMapper.selectById(id);
        if (requirement == null) {
            throw new BusinessException("客户需求不存在");
        }
        if (!CustomerRequirementStatusEnum.HANDLING.equals(requirement.getStatus())) {
            throw new BusinessException("客户需求不是处理中状态");
        }

        LambdaUpdateWrapper<CustomerRequirementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CustomerRequirementDO::getId, id)
                .set(CustomerRequirementDO::getStatus, CustomerRequirementStatusEnum.INVITED);

        baseMapper.update(null, wrapper);
    }

    @Override
    public void wait(Long id) {
        CustomerRequirementDO requirement = baseMapper.selectById(id);
        if (requirement == null) {
            throw new BusinessException("客户需求不存在");
        }
        if (!CustomerRequirementStatusEnum.DRAFT.equals(requirement.getStatus())) {
            throw new BusinessException("客户需求不是无需处理状态");
        }

        LambdaUpdateWrapper<CustomerRequirementDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CustomerRequirementDO::getId, id)
                .set(CustomerRequirementDO::getStatus, CustomerRequirementStatusEnum.WAIT);

        baseMapper.update(null, wrapper);
    }

    @Override
    public Integer getRequiredNum(CustomerRequirementQuery query) {
        List<CustomerRequirementDO> list = list(this.buildQueryWrapper(query));
        return list.stream().map(CustomerRequirementDO::getQuantity).reduce(0, Integer::sum);
    }

    @Override
    public Long customerApply(AdAccountOrderAddReq req) {
        CustomerService customerService = SpringUtil.getBean(CustomerService.class);

        CustomerDO customer = customerService.getById(req.getCustomerId());
        CheckUtils.throwIfNull(customer, "未找到相关客户");

        CustomerRequirementDO customerRequirementDO = new CustomerRequirementDO();
        customerRequirementDO.setCustomerId(req.getCustomerId());
        customerRequirementDO.setTimezone(req.getTimezone());
        customerRequirementDO.setCustomerBmId(req.getCustomerBmId());
        customerRequirementDO.setQuantity(req.getQuantity());
        customerRequirementDO.setStatus(CustomerRequirementStatusEnum.WAIT);
        customerRequirementDO.setAdAccountName(req.getAdAccountName());
        customerRequirementDO.setCustomerEmail(req.getCustomerEmail());
        customerRequirementDO.setCreateUser(req.getCustomerId());
        customerRequirementDO.setRequirementTime(LocalDateTime.now());
        save(customerRequirementDO);

        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getClientNotifyChatId())
                .text(String.format("""
                         【待确认的客户需求】
                        客户：%s
                        邮箱：%s
                        需求数量：%s
                        时区：%s
                        """, customer.getName(), req.getCustomerEmail(), req.getQuantity(), req.getTimezone()))
                .build()));

        return customerRequirementDO.getId();
    }
}