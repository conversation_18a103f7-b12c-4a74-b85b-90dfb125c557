package top.continew.admin.biz.mapper.crm;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.resp.CustomerConditionResp;
import top.continew.admin.biz.model.entity.crm.CustomerVisitStrategyDO;
import top.continew.admin.biz.model.req.crm.CustomerVisitStragegyBasicConditionReq;
import top.continew.starter.data.mp.base.BaseMapper;

import java.util.List;

/**
 * 客户回访策略 Mapper
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
public interface CustomerVisitStrategyMapper extends BaseMapper<CustomerVisitStrategyDO> {

    /**
     * 根据基础条件查询客户ID列表
     */
    List<Long> findCustomerIdsByBasicConditions(@Param("basicCondition") CustomerVisitStragegyBasicConditionReq basicCondition);


    /**
     * 根据空置户数量查询客户ID列表（支持基础条件合并）
     */
    List<CustomerConditionResp> findCustomerIdsByEmptyAccountCountWithBasicConditions(@Param("minCount") Integer minCount,
                                                                                      @Param("maxCount") Integer maxCount,
                                                                                      @Param("basicCondition") CustomerVisitStragegyBasicConditionReq basicCondition,
                                                                                      @Param("filterCustomerIds") List<Long> filterCustomerIds);


    /**
     * 根据广告户数查询客户ID和户数（支持基础条件合并）
     */
    List<CustomerConditionResp> findCustomersByAdAccountCountWithBasicConditions(@Param("minCount") Integer minCount,
                                                                                 @Param("maxCount") Integer maxCount,
                                                                                 @Param("basicCondition") CustomerVisitStragegyBasicConditionReq basicCondition,
                                                                                 @Param("filterCustomerIds") List<Long> filterCustomerIds);

    /**
     * 根据正常户数量查询客户ID和户数（支持基础条件合并）
     */
    List<CustomerConditionResp> findCustomersByNormalAccountCountWithBasicConditions(@Param("minCount") Integer minCount,
                                                                                     @Param("maxCount") Integer maxCount,
                                                                                     @Param("basicCondition") CustomerVisitStragegyBasicConditionReq basicCondition,
                                                                                     @Param("filterCustomerIds") List<Long> filterCustomerIds);

    /**
     * 根据消耗金额查询客户ID和消耗额（支持基础条件合并）
     */
    List<CustomerConditionResp> findCustomersByConsumptionAmountWithBasicConditions(@Param("minAmount") Double minAmount,
                                                                                    @Param("maxAmount") Double maxAmount,
                                                                                    @Param("startDate") String startDate,
                                                                                    @Param("endDate") String endDate,
                                                                                    @Param("basicCondition") CustomerVisitStragegyBasicConditionReq basicCondition,
                                                                                    @Param("filterCustomerIds") List<Long> filterCustomerIds);


    /**
     * 获取指定时间段内客户的消耗数据（用于趋势分析）
     */
    List<CustomerConditionResp> findCustomersConsumptionByPeriod(@Param("startDate") String startDate,
                                                                 @Param("endDate") String endDate,
                                                                 @Param("basicCondition") CustomerVisitStragegyBasicConditionReq basicCondition,
                                                                 @Param("filterCustomerIds") List<Long> filterCustomerIds);
}