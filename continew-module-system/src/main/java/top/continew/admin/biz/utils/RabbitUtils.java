/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.utils;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import top.continew.katai.RuntimeOptions;
import top.continew.katai.okhttp.ClientHelper;
import top.continew.starter.core.exception.BusinessException;

import java.io.IOException;

@Slf4j
public class RabbitUtils {

    private static final String HOST = "http://***************:3005";

    private static final RuntimeOptions runtimeOptions = new RuntimeOptions();

    private static final OkHttpClient okHttpClient;

    static {
        runtimeOptions.setConnectTimeout(600000);
        runtimeOptions.setReadTimeout(600000);
        try {
            okHttpClient = ClientHelper.getOkHttpClient("***************", 3005, runtimeOptions);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String getAdAccountsInfoByTimeRange(String envId, String start, String end, boolean isLite) {
        log.info("【广告户时间段消耗同步】[{}]开始同步{}~{}之间的数据...", envId, start, end);
        String url = String.format("%s/getAdAccountsInfoByTimeRange?envId=%s&since=%s&until=%s&lite=%s", HOST, envId, start, end,
            isLite
                ? "1"
                : "");
        try {
            String result = sendRequest(url);
            log.info("【广告户时间段消耗同步】[{}]数据同步完成", envId);
            return result;
        } catch (Exception e) {
            log.error("【广告户时间段消耗同步】请求广告户数据异常：{}", e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String getAdAccountsInfoByTimeRangeV2(String serialNumber,
                                                        boolean isLite,
                                                        String start,
                                                        String end,
                                                        String proxy,
                                                        String headers) {
        if (StringUtils.isBlank(proxy)) {
            return formatErrorMsg("代理IP未填写");
        }
        if (StringUtils.isBlank(headers)) {
            return formatErrorMsg("headers数据为空");
        }
        log.info("【广告户时间段消耗同步】[{}]开始同步数据...", serialNumber);
        String url = String.format("%s/v2/getAdAccountsInfoByTimeRange", HOST);
        JSONObject body = new JSONObject();
        body.put("proxy", proxy);
        body.put("headers", JSONObject.parseObject(headers));
        body.put("since", start);
        body.put("until", end);
        body.put("lite", isLite ? "1" : "");
        try {
            String result = sendPostRequest(url, body);
            log.info("【广告户时间段消耗同步】[{}]数据同步完成", serialNumber);
            return result;
        } catch (Exception e) {
            log.error("【广告户时间段消耗同步】[{}]请求广告户数据异常：{}", serialNumber, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }


    public static String getAdInfosByTimeRangeV2(String serialNumber,
                                                        boolean isLite,
                                                        String start,
                                                        String end,
                                                        String proxy,
                                                        String headers) {
        if (StringUtils.isBlank(proxy)) {
            return formatErrorMsg("代理IP未填写");
        }
        if (StringUtils.isBlank(headers)) {
            return formatErrorMsg("headers数据为空");
        }
        log.info("【广告系列时间段消耗同步】[{}]开始同步数据...", serialNumber);
        String url = String.format("%s/v2/getAdInfosByTimeRange", HOST);
        JSONObject body = new JSONObject();
        body.put("proxy", proxy);
        body.put("headers", JSONObject.parseObject(headers));
        body.put("since", start);
        body.put("until", end);
        body.put("lite", isLite ? "1" : "");
        try {
            String result = sendPostRequest(url, body);
            log.info("【广告系列时间段消耗同步】[{}]数据同步完成", serialNumber);
            return result;
        } catch (Exception e) {
            log.error("【广告系列时间段消耗同步】[{}]请求数据异常：{}", serialNumber, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String getAllAdSpent(String envId, boolean isLite) {
        log.info("【广告户总消耗同步】[{}]开始同步数据...", envId);
        String url = String.format("%s/getAllAdSpent?envId=%s&lite=%s", HOST, envId, isLite ? "1" : "");
        try {
            String result = sendRequest(url);
            log.info("【广告户总消耗同步】[{}]数据同步完成", envId);
            return result;
        } catch (Exception e) {
            log.error("【广告户总消耗同步】[{}]请求广告户数据异常：{}", envId, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String getAllAdSpentV2(String serialNumber, boolean isLite, String proxy, String headers) {
        if (StringUtils.isBlank(proxy)) {
            return formatErrorMsg("代理IP未填写");
        }
        if (StringUtils.isBlank(headers)) {
            return formatErrorMsg("headers数据为空");
        }
        log.info("【广告户总消耗同步】[{}]开始同步数据...", serialNumber);
        String url = String.format("%s/v2/getAllAdSpent", HOST);
        JSONObject body = new JSONObject();
        body.put("proxy", proxy);
        body.put("headers", JSONObject.parseObject(headers));
        body.put("lite", isLite ? "1" : "");
        try {
            String result = sendPostRequest(url, body);
            log.info("【广告户总消耗同步】[{}]数据同步完成", serialNumber);
            return result;
        } catch (Exception e) {
            log.error("【广告户总消耗同步】[{}]请求广告户数据异常：{}", serialNumber, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String getAllAdAccountDetails(String envId, boolean isLite) {
        log.info("【广告户详情同步】[{}]开始同步数据...", envId);
        String url = String.format("%s/getAllAdAccountDetails?envId=%s&lite=%s", HOST, envId, isLite ? "1" : "");
        try {
            String result = sendRequest(url);
            log.info("【广告户详情同步】[{}]数据同步完成", envId);
            return result;
        } catch (Exception e) {
            log.error("【广告户详情同步】[{}]请求广告户数据异常：{}", envId, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String getPaymentTransactions(String envId, boolean isLite) {
        log.info("【广告户账单同步】[{}]开始同步数据...", envId);
        String url = String.format("%s/getPaymentTransactions?envId=%s&lite=%s", HOST, envId, isLite ? "1" : "");
        try {
            String result = sendRequest(url);
            log.info("【广告户账单同步】[{}]数据同步完成", envId);
            return result;
        } catch (Exception e) {
            log.error("【广告户账单同步】[{}]请求广告户数据异常：{}", envId, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }

    }

    public static String getPaymentTransactionsV2(String serialNumber, boolean isLite, String proxy, String headers) {
        if (StringUtils.isBlank(proxy)) {
            return formatErrorMsg("代理IP未填写");
        }
        if (StringUtils.isBlank(headers)) {
            return formatErrorMsg("headers数据为空");
        }
        log.info("【广告户账单同步】[{}]开始同步数据...", serialNumber);
        String url = String.format("%s/v2/getPaymentTransactions", HOST);
        JSONObject body = new JSONObject();
        body.put("proxy", proxy);
        body.put("headers", JSONObject.parseObject(headers));
        body.put("lite", isLite ? "1" : "");
        try {
            String result = sendPostRequest(url, body);
            log.info("【广告户账单同步】[{}]数据同步完成", serialNumber);
            return result;
        } catch (Exception e) {
            log.error("【广告户账单同步】[{}]请求广告户数据异常：{}", serialNumber, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String getAllBusinessManagers(String envId) {
        log.info("【BM账号同步】[{}]开始同步数据...", envId);
        String url = String.format("%s/getAllBusinessManagers?envId=%s", HOST, envId);
        try {
            String result = sendRequest(url);
            log.info("【BM账号同步】[{}]数据同步完成", envId);
            return result;
        } catch (Exception e) {
            log.error("【BM账号同步】[{}]请求广告户数据异常：{}", envId, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String getAllBusinessManagersV2(String serialNumber, String proxy, String headers) {
        if (StringUtils.isBlank(proxy)) {
            return formatErrorMsg("代理IP未填写");
        }
        if (StringUtils.isBlank(headers)) {
            return formatErrorMsg("headers数据为空");
        }
        log.info("【BM账号同步】[{}]开始同步数据...", serialNumber);
        String url = String.format("%s/v2/getAllBusinessManagers", HOST);
        JSONObject body = new JSONObject();
        body.put("proxy", proxy);
        body.put("headers", JSONObject.parseObject(headers));
        try {
            String result = sendPostRequest(url, body);
            log.info("【BM账号同步】[{}]数据同步完成", serialNumber);
            return result;
        } catch (Exception e) {
            log.error("【BM账号同步】[{}]请求广告户数据异常：{}", serialNumber, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String getAllCampaigns(String envId, boolean isLite) {
        log.info("【广告户系列同步】[{}]开始同步数据...", envId);
        String url = String.format("%s/getAllCampaigns?envId=%s&lite=%s", HOST, envId, isLite ? "1" : "");
        try {
            String result = sendRequest(url);
            log.info("【广告户系列同步】[{}]数据同步完成", envId);
            return result;
        } catch (Exception e) {
            log.error("【广告户系列同步】[{}]请求广告户数据异常：{}", envId, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String getAllCampaignsV2(String serialNumber, boolean isLite, String proxy, String headers) {
        if (StringUtils.isBlank(proxy)) {
            return formatErrorMsg("代理IP未填写");
        }
        if (StringUtils.isBlank(headers)) {
            return formatErrorMsg("headers数据为空");
        }
        log.info("【广告户系列同步】[{}]开始同步数据...", serialNumber);
        String url = String.format("%s/v2/getAllCampaigns", HOST);
        JSONObject body = new JSONObject();
        body.put("proxy", proxy);
        body.put("headers", JSONObject.parseObject(headers));
        body.put("lite", isLite ? "1" : "");
        try {
            String result = sendPostRequest(url, body);
            log.info("【广告户系列同步】[{}]数据同步完成", serialNumber);
            return result;
        } catch (Exception e) {
            log.error("【广告户系列同步】[{}]请求广告户数据异常：{}", serialNumber, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }


    public static String getAllAdInfos(String serialNumber, boolean isLite, String proxy, String headers) {
        if (StringUtils.isBlank(proxy)) {
            return formatErrorMsg("代理IP未填写");
        }
        if (StringUtils.isBlank(headers)) {
            return formatErrorMsg("headers数据为空");
        }
        log.info("【所有广告户信息同步】[{}]开始同步数据...", serialNumber);
        String url = String.format("%s/v2/getAllAdInfos", HOST);
        log.info(url);
        JSONObject body = new JSONObject();
        body.put("proxy", proxy);
        body.put("headers", JSONObject.parseObject(headers));
        body.put("lite", isLite ? "1" : "");
        try {
            String result = sendPostRequest(url, body);
            log.info("【所有广告户信息同步】[{}]数据同步完成", serialNumber);
            return result;
        } catch (Exception e) {
            log.error("【所有广告户信息同步】[{}]请求广告户数据异常：{}", serialNumber, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

    public static String checkAdAccountNurturingStatus(String platformAdId,
                                                       String proxy,
                                                       Integer minSpent,
                                                       Integer maxSpent,
                                                       String headers) {
        log.info("【广告户养户状态同步】[{}]开始同步数据...", platformAdId);
        String url = String.format("%s/checkAdAccountNurturingStatus".formatted(HOST));
        JSONObject body = new JSONObject();
        body.put("adId", platformAdId);
        body.put("proxy", proxy);
        body.put("min", minSpent);
        body.put("max", maxSpent);
        body.put("headers", JSONObject.parseObject(headers));
        int retryTime = 0;
        while (retryTime < 3) {
            try {
                return HttpRequest.post(url).body(body.toJSONString()).timeout(20000).execute().body();
            } catch (HttpException e) {
                log.info("【广告户养户状态同步】[{}]数据同步网络错误:{}", platformAdId, e.getMessage());
                retryTime++;
            } catch (Exception e) {
                log.info("【广告户养户状态同步】[{}]数据同步错误:{}", platformAdId, e.getMessage());
                return "";
            }
        }
        return "";
    }

    private static String formatErrorMsg(String errorMsg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 1);
        jsonObject.put("msg", errorMsg);
        return jsonObject.toString();
    }

    private static String sendRequest(String url) {
        int retryTime = 0;
        while (retryTime < 3) {
            try {
                Request request = new Request.Builder().url(url).build();
                try (Response response = okHttpClient.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        return response.body().string();
                    }
                    throw new BusinessException("statusCode error");
                } catch (IOException e) {
                    retryTime++;
                }
            } catch (Exception e) {
                throw new BusinessException(e);
            }
        }
        throw new BusinessException("网络错误");
    }

    private static String sendPostRequest(String url, JSONObject body) {
        int retryTime = 0;
        while (retryTime < 3) {
            try {
                Request request = new Request.Builder().url(url)
                    .post(RequestBody.create(JSON.toJSONString(body), MediaType.get("application/json")))
                    .build();
                try (Response response = okHttpClient.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        return response.body().string();
                    }
                    throw new BusinessException("statusCode error");
                } catch (IOException e) {
                    retryTime++;
                }
            } catch (Exception e) {
                throw new BusinessException(e);
            }
        }
        throw new BusinessException("网络错误");
    }

    public static String getAllAdSets(String serialNumber, boolean isLite, String proxy, String headers) {
        if (StringUtils.isBlank(proxy)) {
            return formatErrorMsg("代理IP未填写");
        }
        if (StringUtils.isBlank(headers)) {
            return formatErrorMsg("headers数据为空");
        }
        log.info("【广告户广告组同步】[{}]开始同步数据...", serialNumber);
        String url = String.format("%s/getAllAdSets", HOST);
        JSONObject body = new JSONObject();
        body.put("proxy", proxy);
        body.put("headers", JSONObject.parseObject(headers));
        body.put("lite", isLite ? "1" : "");
        try {
            String result = sendPostRequest(url, body);
            log.info("【广告户广告组同步】[{}]数据同步完成", serialNumber);
            return result;
        } catch (Exception e) {
            log.error("【广告户广告组同步】[{}]请求广告户数据异常：{}", serialNumber, e.getMessage(), e);
            return formatErrorMsg(e.getMessage());
        }
    }

}
