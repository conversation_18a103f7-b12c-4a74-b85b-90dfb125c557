package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class BannedAnalyzeFactorQuery {

    @Schema(description = "查询日期范围的开始时间。格式：yyyy-MM-dd HH:mm:ss",
            example = "2024-01-01 00:00:00",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @Schema(description = "查询日期范围的结束时间。格式：yyyy-MM-dd HH:mm:ss",
            example = "2024-12-31 23:59:59",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    private Integer type;

    private List<String> cardHeader;

    private List<String> timeZone;

    private Boolean oneKnifeFlow;

    private Long customerId;

    private String adAccountIds;

    private List<Integer> banType;

}
