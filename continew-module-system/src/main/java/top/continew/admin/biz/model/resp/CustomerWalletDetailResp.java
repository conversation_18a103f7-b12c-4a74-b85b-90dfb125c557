package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 客户钱包详情信息
 *
 * <AUTHOR>
 * @since 2025/07/22 16:58
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "客户钱包详情信息")
public class CustomerWalletDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @ExcelProperty(value = "关联客户")
    private Long customerId;

    /**
     * 钱包地址
     */
    @Schema(description = "钱包地址")
    @ExcelProperty(value = "钱包地址")
    private String walletAddress;
}