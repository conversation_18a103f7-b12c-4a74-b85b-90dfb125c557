/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotClearReq;
import top.continew.admin.biz.robot.req.RobotCustomerRechargeReq;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;

@Service
public class RobotCustomerRechargeTempStrategyImpl implements RobotCommandStrategy {
    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.CUSTOMER_RECHARGE_TEMP;
    }

    @Override
    public String execute(Update update) {
        if (!BotUtils.isFinancialUser(update.getMessage().getFrom().getUserName())) {
            return "";
        }
        return BotUtils.parseObjToMsg(RobotCommandEnum.CUSTOMER_RECHARGE_PREVIEW.getDescription(), RobotCustomerRechargeReq.class, false);
    }
}
