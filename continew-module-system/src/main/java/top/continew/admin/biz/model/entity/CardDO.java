/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.AdPlatformEnum;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡片实体
 *
 * <AUTHOR>
 * @since 2024/12/28 10:43
 */
@Data
@TableName("biz_card")
public class CardDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 所属平台
     */
    private CardPlatformEnum platform;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 状态(1=正常，2=锁定，3=冻结）
     */
    private CardStatusEnum status;

    /**
     * 开卡日期
     */
    private LocalDateTime openTime;

    /**
     * 第三方平台ID
     */
    private String platformCardId;

    /**
     * 过期时间
     */
    private String expireDate;

    /**
     * 卡组织
     */
    private String association;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private Long createUser;

    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    private String remark;

    private BigDecimal usedAmount;

    private String cardName;

    private String cvv;

    private String platformAdId;

    /**
     * 第三方平台的用卡人ID
     */
    private String platformCardHolderId;

    private AdPlatformEnum adPlatform;

    /**
     * 是否已使用
     */
    private Boolean hasUsed;
}