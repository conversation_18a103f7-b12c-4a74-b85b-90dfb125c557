/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 广告户系列消耗详情信息
 *
 * <AUTHOR>
 * @since 2025/01/03 15:08
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "广告户系列消耗详情信息")
public class CampaignInsightDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 平台活动ID
     */
    @Schema(description = "平台活动ID")
    @ExcelProperty(value = "平台活动ID")
    private String platformCampaignId;

    /**
     * 平台广告ID
     */
    @Schema(description = "平台广告ID")
    @ExcelProperty(value = "平台广告ID")
    private Long platformAdId;

    /**
     * 统计时间
     */
    @Schema(description = "统计时间")
    @ExcelProperty(value = "统计时间")
    private LocalDate statDate;

    /**
     * 消耗
     */
    @Schema(description = "消耗")
    @ExcelProperty(value = "消耗")
    private BigDecimal spend;
}