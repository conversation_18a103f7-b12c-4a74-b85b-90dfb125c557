/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.ExcelTemplateEnum;
import top.continew.admin.biz.enums.FbAccountStatusEnum;
import top.continew.admin.biz.event.FbAccountDisabledEvent;
import top.continew.admin.biz.excel.FbAccountBitExcelData;
import top.continew.admin.biz.excel.FbAccountExcelData;
import top.continew.admin.biz.mapper.FbAccountMapper;
import top.continew.admin.biz.model.entity.FbAccountDO;
import top.continew.admin.biz.model.entity.IpDO;
import top.continew.admin.biz.model.query.FbAccountQuery;
import top.continew.admin.biz.model.req.FbAccountBatchUpdateRemarkReq;
import top.continew.admin.biz.model.req.FbAccountBatchUpdateStatusReq;
import top.continew.admin.biz.model.req.FbAccountBatchVerifyReq;
import top.continew.admin.biz.model.req.FbAccountReq;
import top.continew.admin.biz.model.resp.FbAccountDetailResp;
import top.continew.admin.biz.model.resp.FbAccountResp;
import top.continew.admin.biz.service.FbAccountService;
import top.continew.admin.biz.service.IpService;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * fb账号业务实现
 *
 * <AUTHOR>
 * @since 2025/01/03 16:32
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FbAccountServiceImpl extends BaseServiceImpl<FbAccountMapper, FbAccountDO, FbAccountResp, FbAccountDetailResp, FbAccountQuery, FbAccountReq> implements FbAccountService {

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private final IpService ipService;

    @Override
    protected QueryWrapper<FbAccountDO> buildQueryWrapper(FbAccountQuery query) {
        QueryWrapper<FbAccountDO> fbAccountDOQueryWrapper = super.buildQueryWrapper(query);
        if (query.getHasPassword() != null) {
            if (query.getHasPassword()) {
                fbAccountDOQueryWrapper.like("remark", "-").or().like("remark", "—");
            } else {
                fbAccountDOQueryWrapper.notLike("remark", "-").notLike("remark", "—");
            }
        }

        return fbAccountDOQueryWrapper;
    }

    @Override
    public FbAccountDO getFbAccountByFbAccountId(String fbAccountId) {
        return getOne(new LambdaQueryWrapper<FbAccountDO>().eq(FbAccountDO::getPlatformAccountId, fbAccountId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized List<String> importExcelBySystemIp(MultipartFile file,
                                                           Long channelId,
                                                           String tag,
                                                           BigDecimal price,
                                                           ExcelTemplateEnum type) {

        CheckUtils.throwIf(channelId == null, "渠道不能为空");
        CheckUtils.throwIf(StringUtils.isBlank(tag), "标签不能为空");
        CheckUtils.throwIf(type == null, "模板不能为空");
        List<FbAccountExcelData> importRowList;
        try {
            if (type == ExcelTemplateEnum.BIT) {
                List<FbAccountBitExcelData> bitList = EasyExcel.read(file.getInputStream()).head(FbAccountBitExcelData.class).sheet("导入窗口模版").doReadSync();
                bitList.subList(0, 2).clear();
                importRowList = convert(bitList);
            } else if (type == ExcelTemplateEnum.ADS) {
                importRowList = EasyExcel.read(file.getInputStream()).head(FbAccountExcelData.class).sheet().doReadSync();
            } else {
                throw new BusinessException("未知模板");
            }
        } catch (IOException e) {
            log.error("用户导入数据文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("数据文件解析异常");
        }
        for (int i = 0; i < importRowList.size(); i++) {
            if (StringUtils.isBlank(importRowList.get(i).getCountryCode())) {
                throw new BusinessException("第" + (i + 1) + "行未填写IP国家");
            }
        }
        BigDecimal unitPrice = NumberUtil.div(price, importRowList.size());
        List<String> error = new ArrayList<>();
        Set<String> existPlatformAccountIdList = this.list()
                .stream()
                .map(FbAccountDO::getPlatformAccountId)
                .collect(Collectors.toSet());
        List<FbAccountExcelData> distinctList = new ArrayList<>();
        for (FbAccountExcelData fbAccountExcelData : importRowList) {
            String platformAccountId = getPlatformAccountId(fbAccountExcelData.getCookie());
            if (StringUtils.isBlank(platformAccountId) || existPlatformAccountIdList.contains(platformAccountId)) {
                error.add(platformAccountId);
                continue;
            }
            existPlatformAccountIdList.add(platformAccountId);
            distinctList.add(fbAccountExcelData);
        }
        List<FbAccountDO> saveList = new ArrayList<>();
        Map<String, List<FbAccountExcelData>> countryMap = distinctList.stream()
                .collect(Collectors.groupingBy(FbAccountExcelData::getCountryCode));
        Map<Long, Integer> ipUseCountMap = new HashMap<>();
        countryMap.forEach((countryCode, excelDataList) -> {
            List<IpDO> availableIps = ipService.getAvailableIp(countryCode.toUpperCase());
            Queue<IpDO> ipQueue = new LinkedList<>();
            // 计算出可用的IP数
            int totalValidCount = 0;
            for (IpDO availableIp : availableIps) {
                int count = 0;
                if (availableIp.getSuccessTimes() == 0) {
                    count = Math.min(10 - availableIp.getUseTimes(), 5);
                } else {
                    count = Math.min(10 - availableIp.getUseTimes(), (3 - availableIp.getSuccessTimes()) * 2);
                }
                totalValidCount += count;
                for (int i = 0; i < count; i++) {
                    ipQueue.add(availableIp);
                }
            }
            if (totalValidCount < excelDataList.size()) {
                throw new BusinessException(countryCode + " IP数量不足，请先导入");
            }
            for (FbAccountExcelData fbAccountExcelData : excelDataList) {
                IpDO ip = ipQueue.poll();
                FbAccountDO fbAccountDO = new FbAccountDO();
                BeanUtil.copyProperties(fbAccountExcelData, fbAccountDO);
                String platformAccountId = getPlatformAccountId(fbAccountExcelData.getCookie());
                fbAccountDO.setPlatformAccountId(platformAccountId);
                fbAccountDO.setRemark(fbAccountExcelData.getRemark());
                fbAccountDO.setName(platformAccountId);
                fbAccountDO.setChannelId(channelId);
                fbAccountDO.setTag(tag);
                fbAccountDO.setPrice(unitPrice);
                fbAccountDO.setProxy(ip.getProxy());
                fbAccountDO.setIpId(ip.getId());
                saveList.add(fbAccountDO);
                ipUseCountMap.put(ip.getId(), ipUseCountMap.getOrDefault(ip.getId(), 0) + 1);
            }
        });
        this.saveBatch(saveList);
        ipUseCountMap.forEach(ipService::addUseCount);
        for (FbAccountDO fbAccountDO : saveList) {
            threadPoolTaskExecutor.execute(() -> this.checkFbAccountStatus(fbAccountDO));
        }
        return error;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importExcelByCustomIp(MultipartFile file, Long channelId, String tag, BigDecimal price, ExcelTemplateEnum type) {

        CheckUtils.throwIf(channelId == null, "渠道不能为空");
        CheckUtils.throwIf(StringUtils.isBlank(tag), "标签不能为空");
        CheckUtils.throwIf(type == null, "模板不能为空");
        List<FbAccountExcelData> importRowList;
        try {
            if (type == ExcelTemplateEnum.BIT) {
                List<FbAccountBitExcelData> bitList = EasyExcel.read(file.getInputStream()).head(FbAccountBitExcelData.class).sheet("导入窗口模版").doReadSync();
                bitList.subList(0, 2).clear();
                importRowList = convert(bitList);
            } else if (type == ExcelTemplateEnum.ADS) {
                importRowList = EasyExcel.read(file.getInputStream()).head(FbAccountExcelData.class).sheet().doReadSync();
            } else {
                throw new BusinessException("未知模板");
            }
        } catch (IOException e) {
            log.error("用户导入数据文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("数据文件解析异常");
        }
        BigDecimal unitPrice = NumberUtil.div(price, importRowList.size());
        List<FbAccountDO> saveList = new ArrayList<>();
        List<String> error = new ArrayList<>();
        for (FbAccountExcelData fbAccountExcelData : importRowList) {
            FbAccountDO fbAccountDO = new FbAccountDO();
            BeanUtil.copyProperties(fbAccountExcelData, fbAccountDO);
            String platformAccountId = getPlatformAccountId(fbAccountExcelData.getCookie());
            fbAccountDO.setPlatformAccountId(platformAccountId);
            fbAccountDO.setRemark(fbAccountExcelData.getRemark());
            fbAccountDO.setName(platformAccountId);
            fbAccountDO.setChannelId(channelId);
            fbAccountDO.setTag(tag);
            fbAccountDO.setPrice(unitPrice);
            try {
                save(fbAccountDO);
                saveList.add(fbAccountDO);
            } catch (DuplicateKeyException e) {
                error.add(platformAccountId);
            }
        }
        for (FbAccountDO fbAccountDO : saveList) {
            threadPoolTaskExecutor.execute(() -> {
                this.checkFbAccountStatus(fbAccountDO);
            });
        }
        return error;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(FbAccountBatchUpdateStatusReq req) {
        this.update(new LambdaUpdateWrapper<FbAccountDO>().in(FbAccountDO::getId, req.getIds())
                .set(FbAccountDO::getStatus, req.getStatus()));
        if (req.getStatus().equals(FbAccountStatusEnum.BANNED) || req.getStatus().equals(FbAccountStatusEnum.LOCKED)) {
            // fb账号绑定的广告户id列表
            List<String> platformAdIds = listByIds(req.getIds()).stream()
                    .map(FbAccountDO::getPlatformAccountId)
                    .filter(StringUtils::isNotBlank)
                    .toList();
            if (!platformAdIds.isEmpty()) {
                for (String platformAdId : platformAdIds) {
                    FbAccountDisabledEvent event = new FbAccountDisabledEvent(platformAdId);
                    SpringUtil.publishEvent(event);
                }
            }
        }
    }

    @Override
    public void batchVerify(FbAccountBatchVerifyReq req) {

        List<FbAccountDO> fbAccountDOS = list(new LambdaQueryWrapper<FbAccountDO>().in(FbAccountDO::getId, req.getIds())
                .ne(FbAccountDO::getStatus, FbAccountStatusEnum.NORMAL));
        for (FbAccountDO fbAccountDO : fbAccountDOS) {
            CompletableFuture.runAsync(() -> this.checkFbAccountStatus(fbAccountDO), threadPoolTaskExecutor);
        }
    }

    @Override
    public void batchUpdateRemark(FbAccountBatchUpdateRemarkReq req) {
        update(new LambdaUpdateWrapper<FbAccountDO>().in(FbAccountDO::getId, req.getIds())
                .set(FbAccountDO::getCustomRemark, req.getRemark()));
    }

    private void checkFbAccountStatus(FbAccountDO accountDO) {
        try {
            FbAccountStatusEnum statusEnum = FacebookUtils.check(accountDO.getCookie(), accountDO.getProxy(), accountDO.getUa());
            FbAccountDO update = new FbAccountDO();
            update.setId(accountDO.getId());
            update.setStatus(statusEnum);
            this.updateById(update);
            if (statusEnum.equals(FbAccountStatusEnum.NORMAL)) {
                if (accountDO.getIpId() != null) {
                    ipService.addSuccess(accountDO.getIpId());
                }
            }
        } catch (Exception ignored) {

        }
    }

    private String getPlatformAccountId(String cookie) {
        JSONArray jsonArray = JSONArray.parse(cookie);
        for (Object o : jsonArray) {
            if (JSONObject.from(o).getString("name").equals("c_user")) {
                return JSONObject.from(o).getString("value");
            }
        }
        return "platformAccountId";
    }

    @Override
    public void export(FbAccountQuery query, SortQuery sortQuery, HttpServletResponse response) {
        List<FbAccountDetailResp> list = list(query, sortQuery, this.getDetailClass());

        List<FbAccountDetailResp> newList = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            newList.add(list.get(i));
            if ((i + 1) % 10 == 0 && i + 1 < list.size()) {
                newList.add(new FbAccountDetailResp());
            }
        }

        ExcelUtils.export(newList, "导出数据", this.getDetailClass(), response);
    }

    private List<FbAccountExcelData> convert(List<FbAccountBitExcelData> bitExcelDataList) {
        return bitExcelDataList.stream().map(bitExcelData -> {
            FbAccountExcelData fbAccountExcelData = new FbAccountExcelData();
            fbAccountExcelData.setName(bitExcelData.getWindowName());
            fbAccountExcelData.setRemark(bitExcelData.getWindowNote());
            fbAccountExcelData.setUsername(bitExcelData.getUsername());
            fbAccountExcelData.setPassword(bitExcelData.getPassword());
            fbAccountExcelData.setCookie(bitExcelData.getCookie());
            fbAccountExcelData.setFakey(bitExcelData.getTwoFactorSecret());
            fbAccountExcelData.setProxyType(bitExcelData.getProxyType());
            fbAccountExcelData.setIpChecker(bitExcelData.getIpQueryChannel());
            fbAccountExcelData.setProxy(bitExcelData.getProxyInfo());
            fbAccountExcelData.setCountryCode(bitExcelData.getCountry());
            fbAccountExcelData.setRegionCode(bitExcelData.getState());
            fbAccountExcelData.setCityCode(bitExcelData.getCity());
            fbAccountExcelData.setUa(bitExcelData.getUserAgent());
            return fbAccountExcelData;
        }).toList();
    }
}
