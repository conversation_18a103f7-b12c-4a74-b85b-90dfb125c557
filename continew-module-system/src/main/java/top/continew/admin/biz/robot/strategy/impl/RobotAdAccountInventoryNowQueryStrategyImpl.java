/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.model.resp.AdAccountInventoryResp;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.service.AdAccountService;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RobotAdAccountInventoryNowQueryStrategyImpl implements RobotCommandStrategy {

    private final AdAccountService adAccountService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.AD_ACCOUNT_INVENTORY_NOW;
    }

    @Override
    public String execute(Update update) {
        if (!telegramChatIdConfig.isValidChatId(update.getMessage().getChatId())) {
            return null;
        }
        List<AdAccountInventoryResp> list = adAccountService.getInventory(false);
        long total = list.stream().mapToLong(AdAccountInventoryResp::getNum).sum();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("总库存：").append(total).append("\n");
        stringBuilder.append("所有--破额--vo").append("\n");
        for (AdAccountInventoryResp inventoryResp : list) {
            stringBuilder.append(inventoryResp.getTimezone())
                .append("：")
                .append(inventoryResp.getNum())
                .append("--")
                .append(inventoryResp.getHighLimitNum())
                .append("--")
                .append(inventoryResp.getVoNum())
                .append("\n");
        }
        return stringBuilder.toString();
    }
}
