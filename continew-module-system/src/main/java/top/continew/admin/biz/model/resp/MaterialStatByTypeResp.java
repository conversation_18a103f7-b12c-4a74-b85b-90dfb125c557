package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import top.continew.admin.biz.enums.MaterialTypeEnum;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.math.BigDecimal;

@Data
public class MaterialStatByTypeResp {

    @ExcelProperty(value = "类型", converter = ExcelBaseEnumConverter.class)
    private MaterialTypeEnum type;

    @ExcelProperty("数量")
    private Integer num;

    @ExcelProperty("金额")
    private BigDecimal amount;
}
