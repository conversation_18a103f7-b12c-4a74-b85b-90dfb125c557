package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.CustomerCycleComparePeriodQuery;
import top.continew.admin.biz.model.query.CustomerCycleCompareSummaryQuery;
import top.continew.admin.biz.model.resp.CustomerCycleCompareSummaryResp;
import top.continew.admin.biz.model.resp.CustomerCycleCompareBatchResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 客户周期对比分析Mapper
 *
 * <AUTHOR>
 * @since 2025/1/19 15:30
 */
public interface CustomerCycleCompareMapper extends BaseMapper<CustomerDO> {

    /**
     * 获取客户基础统计数据（支持全量和周期查询）
     *
     * @param query 查询条件
     * @return 客户基础统计数据列表
     */
    List<CustomerCycleCompareSummaryResp> selectCustomerBasicData(@Param("query") CustomerCycleCompareSummaryQuery query);


    /**
     * 获取客户空置户数据
     *
     * @param query 查询条件
     * @return 客户空置户数据列表
     */
    List<CustomerCycleCompareSummaryResp> selectCustomerIdleData(@Param("query") CustomerCycleCompareSummaryQuery query);

    /**
     * 获取客户消耗数据
     *
     * @param query 查询条件
     * @return 客户消耗数据列表
     */
    List<CustomerCycleCompareSummaryResp> selectCustomerSpentData(
        @Param("query") CustomerCycleCompareSummaryQuery query
    );

    /**
     * 获取客户智能近七天平均单天消耗数据
     * 基于每个客户最后一笔消耗时间倒推7天计算平均值
     *
     * @param customerIds 客户ID列表
     * @return 客户平均单天消耗数据列表
     */
    List<CustomerCycleCompareSummaryResp> selectCustomerSmartRecentSpentData(
        @Param("customerIds") List<Long> customerIds
    );


    


    /**
     * 按消耗区间统计广告户数量
     *
     * @param customerId 客户ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param spentRanges 消耗区间配置
     * @return 消耗区间分布统计
     */
    List<Map<String, Object>> selectAccountSpentRangeDistribution(
         @Param("customerId") Long customerId,
         @Param("startDate") LocalDate startDate,
         @Param("endDate") LocalDate endDate,
         @Param("spentRanges") List<CustomerCycleComparePeriodQuery.SpentRangeConfig> spentRanges
     );

    /**
     * 批量获取客户基础统计数据
     *
     * @param customerIds 客户ID列表
     * @param period 周期标识
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 客户基础统计数据列表
     */
    List<CustomerCycleCompareBatchResp> selectBatchCustomerBasicData(
        @Param("customerIds") List<Long> customerIds,
        @Param("period") String period,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * 批量获取客户消耗数据
     *
     * @param customerIds 客户ID列表
     * @param period 周期标识
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 客户消耗数据列表
     */
    List<CustomerCycleCompareBatchResp> selectBatchCustomerSpentData(
        @Param("customerIds") List<Long> customerIds,
        @Param("period") String period,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * 批量获取账户消耗区间分布
     *
     * @param customerIds 客户ID列表
     * @param period 周期标识
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param spentRanges 消耗区间配置
     * @return 消耗区间分布统计
     */
    List<Map<String, Object>> selectBatchAccountSpentRangeDistribution(
        @Param("customerIds") List<Long> customerIds,
        @Param("period") String period,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        @Param("spentRanges") List<CustomerCycleComparePeriodQuery.SpentRangeConfig> spentRanges
    );
}
