/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.pinnedmessages.PinChatMessage;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.methods.send.SendPhoto;
import org.telegram.telegrambots.meta.api.objects.Message;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.meta.exceptions.TelegramApiRequestException;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.robot.business.BusinessTelegramBot;

import java.util.Map;
import java.util.Queue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.LinkedBlockingQueue;

@Component
@RequiredArgsConstructor
@Slf4j
public class TelegramBotMessageProcessor implements CommandLineRunner {
    private static final BlockingQueue<Object> messageQueue = new LinkedBlockingQueue<>();
    private static final Map<String, SlidingWindowRateLimiter> rateLimiterMap = new ConcurrentHashMap<>();
    private final MyTelegramBot telegramBot;
    private final TelegramChatIdConfig telegramChatIdConfig;

    @Override
    public void run(String... args) {
        Thread processorThread = new Thread(this::processMessages);
        processorThread.start();
    }

    public static void pushTextMessage(SendMessage message) {
        if (StringUtils.isNotBlank(message.getChatId())) {
            messageQueue.add(message);
        }
    }

    public static void pushPinMessage(PinChatMessage message) {
        if (StringUtils.isNotBlank(message.getChatId())) {
            messageQueue.add(message);
        }
    }

    public static void pushPhotoMessage(SendPhoto message) {
        if (StringUtils.isNotBlank(message.getChatId())) {
            messageQueue.add(message);
        }
    }

    private static synchronized void initRateLimiter(String chatId) {
        if (rateLimiterMap.containsKey(chatId)) {
            return;
        }
        rateLimiterMap.put(chatId, new SlidingWindowRateLimiter());
    }

    private void processMessages() {
        log.info("telegram机器人消息队列启动成功");
        while (true) {
            try {
                Object message = messageQueue.take();
                if (sendMessage(message)) {
                    log.info("telegram机器人消息发送成功");
                } else {
                    messageQueue.put(message);
                }
            } catch (InterruptedException e) {
                log.info("机器人消息队列错误：{}", e.getMessage());
            }
        }
    }

    private boolean sendMessage(Object message) {
        try {
            if (message instanceof SendMessage sendMessage) {
                initRateLimiter(sendMessage.getChatId());
                if (rateLimiterMap.get(sendMessage.getChatId()).tryAcquire()) {
                    Message result = telegramBot.execute(sendMessage);
                    if ((sendMessage.getText().contains("总打款") || sendMessage.getText().contains("更新公告"))  && !sendMessage.getChatId()
                        .equals(telegramChatIdConfig.getRechargeChatId().toString()) && !sendMessage.getChatId()
                        .equals(telegramChatIdConfig.getTouliuChatId().toString())) {
                        PinChatMessage pinChatMessage = PinChatMessage.builder()
                            .chatId(sendMessage.getChatId())
                            .messageId(result.getMessageId())
                            .build();
                        pushPinMessage(pinChatMessage);
                    }
                    return true;
                }
            } else if (message instanceof SendPhoto sendPhoto) {
                initRateLimiter(sendPhoto.getChatId());
                if (rateLimiterMap.get(sendPhoto.getChatId()).tryAcquire()) {
                    telegramBot.execute(sendPhoto);
                    return true;
                }
            } else if (message instanceof PinChatMessage pinChatMessage) {
                initRateLimiter(pinChatMessage.getChatId());
                if (rateLimiterMap.get(pinChatMessage.getChatId()).tryAcquire()) {
                    telegramBot.execute(pinChatMessage);
                    return true;
                }
            }
            return false;
        } catch (TelegramApiException e) {
            log.info("telegram机器人消息发送失败：{}", e.getMessage(), e);
            if (e.getCause() instanceof TelegramApiRequestException telegramApiRequestException) {
                return telegramApiRequestException.getErrorCode() != 429;
            }
            return true;
        }
    }


    public static class SlidingWindowRateLimiter {
        private static final int MAX_REQUESTS = 20; // 最大请求数
        private static final long TIME_WINDOW = 60000; // 时间窗口，单位为毫秒（60秒）
        private final Queue<Long> timestamps = new ConcurrentLinkedQueue<>();

        public synchronized boolean tryAcquire() {
            long now = System.currentTimeMillis();

            // 移除超出时间窗口的时间戳
            while (!timestamps.isEmpty() && now - timestamps.peek() > TIME_WINDOW) {
                timestamps.poll();
            }

            // 检查当前请求数量
            if (timestamps.size() < MAX_REQUESTS) {
                timestamps.add(now); // 记录当前请求时间
                return true; // 允许请求
            }

            return false; // 超出请求限制
        }
    }
}
