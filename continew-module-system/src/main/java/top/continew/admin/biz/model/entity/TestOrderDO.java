package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 测试任务实体
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Data
@TableName("biz_test_order")
public class TestOrderDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 测试标题
     */
    private String title;

    /**
     * trello链接
     */
    private String trelloUrl;

    /**
     * 状态(1=待进行，2=进行中，3=已完成）
     */
    private Integer status;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT)
    private Long createUser;
}