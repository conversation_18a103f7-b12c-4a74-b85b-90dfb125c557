package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.AdProductDO;
import top.continew.admin.biz.model.resp.AdProductDetailResp;
import top.continew.admin.biz.model.resp.AdProductResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.util.List;

/**
 * 投放产品 Mapper
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
public interface AdProductMapper extends BaseMapper<AdProductDO> {

    IPage<AdProductResp> selectCustomPage(@Param("page") IPage<AdProductDO> page,
                                          @Param(Constants.WRAPPER) QueryWrapper<AdProductDO> queryWrapper);

    List<AdProductDetailResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<AdProductDO> queryWrapper);
}