package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class ToufangCustomerDailyReportResp {

    @ExcelProperty("日期")
    private LocalDate statDate;

    @ExcelProperty("代理线")
    private String productName;

    @ExcelProperty("产品")
    private Integer productType;

    @ExcelProperty("打款")
    private BigDecimal totalTransfer;

    @ExcelProperty("日花费")
    private BigDecimal spend;

    @ExcelProperty("日回流")
    private BigDecimal reflowSpend;

    @ExcelProperty("FB消耗")
    private BigDecimal fbSpend;

    @ExcelProperty("服务费")
    private BigDecimal fee;

    @ExcelProperty("实际花费")
    private BigDecimal totalSpend;

    private BigDecimal bearCost;

    private String effectData;

    private String extraData;
}
