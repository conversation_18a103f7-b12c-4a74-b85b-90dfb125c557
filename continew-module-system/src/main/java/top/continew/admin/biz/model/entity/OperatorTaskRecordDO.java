package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 运营人员工作记录实体
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
@Data
@TableName("biz_operator_task_record")
public class OperatorTaskRecordDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 广告户
     */
    private String platformAdId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 备注
     */
    private String remark;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;
}