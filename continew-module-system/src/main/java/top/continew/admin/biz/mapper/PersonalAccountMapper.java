package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.PersonalAccountDO;
import top.continew.admin.biz.model.resp.PersonalAccountResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.util.List;

/**
 * 个号 Mapper
 *
 * <AUTHOR>
 * @since 2025/02/27 14:48
 */
public interface PersonalAccountMapper extends BaseMapper<PersonalAccountDO> {

    IPage<PersonalAccountResp> selectCustomPage(@Param("page") IPage<PersonalAccountDO> page, @Param(Constants.WRAPPER) QueryWrapper<PersonalAccountDO> wrapper);


    List<PersonalAccountResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<PersonalAccountDO> wrapper);
}