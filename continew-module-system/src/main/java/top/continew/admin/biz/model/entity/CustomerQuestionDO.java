package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 客户问题实体
 *
 * <AUTHOR>
 * @since 2025/04/30 14:35
 */
@Data
@TableName("biz_customer_question")
public class CustomerQuestionDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 问题数量
     */
    private Integer num;

    /**
     * 问题账号
     */
    private String accounts;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    private LocalDateTime orderTime;

    private String remark;
}