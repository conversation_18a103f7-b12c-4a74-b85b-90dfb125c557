package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.OpportunityStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 商机实体
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@TableName("biz_opportunity")
public class OpportunityDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户ID
     */
    private Long customerId;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失
     */
    private OpportunityStatusEnum status;

    /**
     * 需求内容
     */
    private String requirement;

    /**
     * 提醒时间
     */
    private LocalDateTime remindTime;

    /**
     * 流失原因
     */
    private Integer lostReason;

    /**
     * 最近跟进时间
     */
    private LocalDateTime lastFollowTime;

    /**
     * 对接人
     */
    private Long handlerUserId;

    /**
     * 备注
     */
    private String remark;
}