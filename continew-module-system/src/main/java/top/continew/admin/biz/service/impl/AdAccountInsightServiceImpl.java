/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.mapper.AdAccountInsightMapper;
import top.continew.admin.biz.model.entity.AdAccountInsightDO;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.AdAccountInsightQuery;
import top.continew.admin.biz.model.req.AdAccountInsightReq;
import top.continew.admin.biz.model.resp.AdAccountInsightDetailResp;
import top.continew.admin.biz.model.resp.AdAccountInsightResp;
import top.continew.admin.biz.service.AdAccountInsightService;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.utils.RabbitUtils;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 广告户每日消耗业务实现
 *
 * <AUTHOR>
 * @since 2025/01/14 14:43
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdAccountInsightServiceImpl extends BaseServiceImpl<AdAccountInsightMapper, AdAccountInsightDO, AdAccountInsightResp, AdAccountInsightDetailResp, AdAccountInsightQuery, AdAccountInsightReq> implements AdAccountInsightService {
    private final AdAccountOrderService adAccountOrderService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    private final AdAccountService adAccountService;

    @Override
    public void syncAdAccountStatData(String envId,
                                      String serialNumber,
                                      String proxy,
                                      String headers,
                                      LocalDate startDate,
                                      LocalDate endDate) {
        String data;
        if (StringUtils.isNotBlank(serialNumber)) {
            data = RabbitUtils.getAdAccountsInfoByTimeRangeV2(serialNumber, false, startDate.toString(), endDate.toString(), proxy, headers);
        } else {
            data = RabbitUtils.getAdAccountsInfoByTimeRange(envId, startDate.toString(), endDate.toString(), false);
        }
        String browserKey = StringUtils.defaultIfBlank(envId, serialNumber);
        JSONObject jsonObject = JSONObject.parse(data);
        int code = jsonObject.getInteger("code");
        if (code != 0) {
            log.error("【{}】广告时间段消耗同步失败：{}", browserKey, jsonObject.getString("msg"));
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                    .chatId(telegramChatIdConfig.getAdAccountNotifyChatId())
                    .text(browserKey + "每日消耗数据同步失败：" + jsonObject.getString("msg"))
                    .build()));
            return;
        }
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        List<AdAccountInsightDO> saveList = new ArrayList<>();
        List<AdAccountInsightDO> updateList = new ArrayList<>();

        Map<String, AdAccountInsightDO> insightMap = new HashMap<>();
        List<AdAccountInsightDO> list = list(new LambdaQueryWrapper<AdAccountInsightDO>().between(AdAccountInsightDO::getStatDate, startDate, endDate));
        if (CollUtil.isNotEmpty(list)) {
            insightMap = list.stream()
                    .collect(Collectors.toMap(insight -> insight.getAdAccountId() + "_" + insight.getStatDate()
                            .toString(), Function.identity()));
        }
        List<AdAccountOrderDO> adAccountOrderList = adAccountOrderService.list(Wrappers.<AdAccountOrderDO>lambdaQuery()
                .in(AdAccountOrderDO::getStatus, List.of(AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.RECYCLE))
                .orderByAsc(AdAccountOrderDO::getFinishTime));
        for (Object o : jsonArray) {
            JSONObject item = JSONObject.from(o);
            int itemCode = item.getIntValue("code");
            if (itemCode != 200) {
                continue;
            }
            JSONObject json = item.getJSONObject("body");
            JSONObject insights = json.getJSONObject("insights");
            String adAccountId = json.getString("account_id");
            if (Objects.isNull(insights)) {
                continue;
            }
            for (Object object : insights.getJSONArray("data")) {
                JSONObject spendData = JSONObject.from(object);
                String key = adAccountId + "_" + spendData.getString("date_start");
                List<AdAccountOrderDO> existOrderList = adAccountOrderList.stream()
                        .filter(v -> v.getAdAccountId().equals(adAccountId))
                        .toList();
                Long customerId = null;
                Long businessUserId = null;
                if (!existOrderList.isEmpty()) {
                    for (AdAccountOrderDO adAccountOrderDO : existOrderList) {
                        LocalDateTime spendTime = LocalDateTimeUtil.parse(spendData.getString("date_start"), "yyyy-MM-dd");
                        LocalDateTime afterTime = LocalDateTimeUtil.beginOfDay(adAccountOrderDO.getFinishTime());
                        if (adAccountOrderDO.getStatus().equals(AdAccountOrderStatusEnum.RECYCLE)) {
                            LocalDateTime recycleDate = LocalDateTimeUtil.endOfDay(adAccountOrderDO.getRecycleTime());
                            if (LocalDateTimeUtil.isIn(spendTime, afterTime, recycleDate)) {
                                customerId = adAccountOrderDO.getCustomerId();
                                businessUserId = adAccountOrderDO.getBusinessUserId();
                            }
                        } else {
                            if (spendTime.isAfter(afterTime) || LocalDateTimeUtil.isSameDay(spendTime, afterTime)) {
                                customerId = adAccountOrderDO.getCustomerId();
                                businessUserId = adAccountOrderDO.getBusinessUserId();
                            }
                        }
                    }
                }
                if (insightMap.containsKey(key)) {
                    AdAccountInsightDO adAccountInsightDO = insightMap.get(key);
                    adAccountInsightDO.setSpend(spendData.getBigDecimal("spend"));
                    adAccountInsightDO.setCustomerId(customerId);
                    adAccountInsightDO.setBusinessUserId(businessUserId);
                    updateList.add(adAccountInsightDO);
                } else {
                    AdAccountInsightDO adAccountInsightDO = new AdAccountInsightDO();
                    adAccountInsightDO.setAdAccountId(adAccountId);
                    adAccountInsightDO.setStatDate(LocalDate.parse(spendData.getString("date_start")));
                    adAccountInsightDO.setSpend(spendData.getBigDecimal("spend"));
                    adAccountInsightDO.setCustomerId(customerId);
                    adAccountInsightDO.setBusinessUserId(businessUserId);
                    saveList.add(adAccountInsightDO);
                }
            }
        }
        saveBatch(saveList);
        updateBatchById(updateList);
    }

    @Override
    public BigDecimal sumSpendByDateRange(String platformAdId, String startDate, String endDate) {
        return baseMapper.sumSpendByDateRange(platformAdId, startDate, endDate);
    }
}