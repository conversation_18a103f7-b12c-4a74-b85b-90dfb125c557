/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import cn.crane4j.annotation.condition.ConditionOnPropertyNotNull;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.RechargeOrderStatusEnum;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.admin.common.constant.ContainerConstants;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

/**
 * 充值订单详情信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:09
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "充值订单详情信息")
public class RechargeOrderDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @ExcelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    private Long customerId;

    @ExcelProperty(value = "关联客户")
    private String customerName;

    @ExcelProperty(value = "关联广告户")
    private String platformAdId;

    /**
     * 充值金额
     */
    @Schema(description = "充值金额")
    @ExcelProperty(value = "充值金额")
    private BigDecimal amount;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @ExcelProperty(value = "状态", converter = ExcelBaseEnumConverter.class)
    private RechargeOrderStatusEnum status;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    @ConditionOnPropertyNotNull
    @Assemble(prop = ":handleUserName", container = ContainerConstants.USER_NICKNAME)
    private Long handleUser;

    @ExcelProperty(value = "处理人")
    private String handleUserName;

    /**
     * 接收时间
     */
    @Schema(description = "接收时间")
    @ExcelProperty(value = "处理时间")
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    @ExcelProperty(value = "完成时间")
    private LocalDateTime finishTime;

    /**
     * 凭证
     */
    @Schema(description = "凭证")
    private String certificate;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
}