/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.AdAccountKeepStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.BusinessManagerDO;
import top.continew.admin.biz.model.entity.BusinessManagerItemDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.BusinessManagerItemService;
import top.continew.admin.biz.service.BusinessManagerService;
import top.continew.admin.system.model.entity.UserDO;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class MoveAdAccountToBMStrategyImpl implements ReportOpsStrategy {

    private final AdAccountService adAccountService;

    private final BusinessManagerService businessManagerService;

    private final BusinessManagerItemService businessManagerItemService;

    @Override
    public ReportType getReport() {
        return ReportType.MOVE_AD_ACCOUNT_TO_BM;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
        JSONObject data = JSONObject.from(browserReq.getData());
        JSONArray successRes = data.getJSONArray("successRes");
        for (int i = 0; i < successRes.size(); i++) {
            JSONObject item = successRes.getJSONObject(i);
            String businessID = item.getString("bmId");
            String adAccountID = item.getString("adId");
            BusinessManagerDO businessManagerDO = businessManagerService.getOne(Wrappers.<BusinessManagerDO>lambdaQuery()
                .eq(BusinessManagerDO::getPlatformId, businessID));
            if (businessManagerDO != null) {
                AdAccountDO update = new AdAccountDO.builder().businessManagerId(businessManagerDO.getId())
                    .bmId(businessID)
                    .bmItemChannelId(businessManagerDO.getChannelId())
                    .bmItemType(businessManagerDO.getType())
                    .bmAuthTime(LocalDateTime.now())
                    .platformAdId(adAccountID)
                    .keepStatus(AdAccountKeepStatusEnum.SUCCESS)
                    .build();
                adAccountService.update(update, Wrappers.<AdAccountDO>lambdaUpdate()
                    .eq(AdAccountDO::getPlatformAdId, adAccountID));
                // 随机使用一个坑位
                BusinessManagerItemDO businessManagerItemDO = businessManagerItemService.getOne(Wrappers.<BusinessManagerItemDO>lambdaQuery()
                    .eq(BusinessManagerItemDO::getBusinessManagerId, businessManagerDO.getId())
                    .eq(BusinessManagerItemDO::getIsUse, false)
                    .eq(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.NORMAL)
                    .last("limit 1"));
                if (businessManagerItemDO != null) {
                    BusinessManagerItemDO itemUpdate = new BusinessManagerItemDO();
                    itemUpdate.setId(businessManagerItemDO.getId());
                    itemUpdate.setIsUse(true);
                    itemUpdate.setUseTime(LocalDateTime.now());
                    itemUpdate.setPlatformAdId(adAccountID);
                    businessManagerItemService.updateById(itemUpdate);
                }
            }
        }
        return data.toString();
    }
}
