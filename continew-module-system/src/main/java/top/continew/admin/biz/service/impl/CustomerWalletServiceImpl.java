package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.CustomerWalletMapper;
import top.continew.admin.biz.model.entity.CustomerWalletDO;
import top.continew.admin.biz.model.query.CustomerWalletQuery;
import top.continew.admin.biz.model.req.CustomerWalletReq;
import top.continew.admin.biz.model.resp.CustomerWalletDetailResp;
import top.continew.admin.biz.model.resp.CustomerWalletResp;
import top.continew.admin.biz.service.CustomerWalletService;

/**
 * 客户钱包业务实现
 *
 * <AUTHOR>
 * @since 2025/07/22 16:58
 */
@Service
@RequiredArgsConstructor
public class CustomerWalletServiceImpl extends BaseServiceImpl<CustomerWalletMapper, CustomerWalletDO, CustomerWalletResp, CustomerWalletDetailResp, CustomerWalletQuery, CustomerWalletReq> implements CustomerWalletService {

    @Override
    protected void beforeAdd(CustomerWalletReq req) {
        req.setWalletAddress(req.getWalletAddress().trim());
    }
}