/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 广告户关联卡查询条件
 *
 * <AUTHOR>
 * @since 2025/01/02 11:24
 */
@Data
@Schema(description = "广告户关联卡查询条件")
public class AdAccountCardQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联广告户
     */
    @Schema(description = "关联广告户")
    @Query(type = QueryType.EQ)
    private Long adAccountId;

    /**
     * 完整卡号
     */
    @Schema(description = "完整卡号")
    @Query(type = QueryType.EQ)
    private String fullCardNumber;

    /**
     * 模糊卡号
     */
    @Schema(description = "模糊卡号")
    @Query(type = QueryType.EQ)
    private String fuzzyCardNumber;

    /**
     * 是否默认
     */
    @Schema(description = "是否默认")
    @Query(type = QueryType.EQ)
    private Boolean isDefault;

    /**
     * 是否已移除
     */
    @Schema(description = "是否已移除")
    @Query(type = QueryType.EQ)
    private Boolean isRemove;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.EQ)
    private LocalDateTime createTime;
}