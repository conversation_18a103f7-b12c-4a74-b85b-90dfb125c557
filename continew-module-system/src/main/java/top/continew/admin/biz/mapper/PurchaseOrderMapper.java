package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.ClearOrderDO;
import top.continew.admin.biz.model.resp.ClearOrderResp;
import top.continew.admin.biz.model.resp.PurchaseOrderResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.PurchaseOrderDO;

import java.util.List;

/**
* 采购订单 Mapper
*
* <AUTHOR>
* @since 2025/05/21 14:38
*/
public interface PurchaseOrderMapper extends BaseMapper<PurchaseOrderDO> {

    IPage<PurchaseOrderResp> selectCustomPage(@Param("page") IPage<PurchaseOrderDO> page,
                                              @Param(Constants.WRAPPER) QueryWrapper<PurchaseOrderDO> queryWrapper);

    List<PurchaseOrderResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<PurchaseOrderDO> queryWrapper);
}