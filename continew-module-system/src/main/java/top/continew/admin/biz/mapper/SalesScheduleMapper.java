package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.SalesScheduleDO;

import java.util.List;

/**
* 商务人员排班/请假记录 Mapper
*
* <AUTHOR>
* @since 2025/08/04 11:56
*/
public interface SalesScheduleMapper extends BaseMapper<SalesScheduleDO> {

    List<UserDO> getUserListByTelegramId(@Param("list") List<String> telegramIds);
}