package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;

/**
 * 指纹浏览器实体
 *
 * <AUTHOR>
 * @since 2025/04/25 17:15
 */
@Data
@TableName("biz_ads_power")
public class AdsPowerDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * user_id
     */
    private String userId;

    /**
     * 编号
     */
    private String serialNumber;

    /**
     * 备注
     */
    private String remark;
}