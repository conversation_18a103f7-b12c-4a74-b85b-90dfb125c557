package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 指纹浏览器查询条件
 *
 * <AUTHOR>
 * @since 2025/04/25 17:15
 */
@Data
@Schema(description = "指纹浏览器查询条件")
public class AdsPowerQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * user_id
     */
    @Schema(description = "user_id")
    @Query(type = QueryType.EQ)
    private String userId;

    /**
     * 编号
     */
    @Schema(description = "编号")
    @Query(type = QueryType.EQ)
    private String serialNumber;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Query(type = QueryType.EQ)
    private String remark;
}