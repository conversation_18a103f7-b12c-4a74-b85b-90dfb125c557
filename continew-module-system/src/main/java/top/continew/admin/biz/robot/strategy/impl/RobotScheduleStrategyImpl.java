package top.continew.admin.biz.robot.strategy.impl;

import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotScheduleReq;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;

@Service
public class RobotScheduleStrategyImpl implements RobotCommandStrategy {
    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.SCHEDULE_TEMP;
    }

    @Override
    public String execute(Update update) {
        return BotUtils.parseObjToMsg(RobotCommandEnum.BUSINESS_SCHEDULE.getDescription(), RobotScheduleReq.class, true);
    }
}
