package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.Assemble;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.MaterialTypeEnum;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.admin.common.constant.ContainerConstants;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

/**
 * 物料详情信息
 *
 * <AUTHOR>
 * @since 2025/01/21 16:20
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "物料详情信息")
@Assemble(keyType = MaterialResp.class, keyDesc = "payPrice,num", prop = ":unitPrice", container = ContainerConstants.MATERIAL_UNIT_PRICE_CAL)
public class MaterialDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    @ExcelProperty(value = "支付时间")
    private LocalDateTime payDate;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @ExcelProperty(value = "类型", converter = ExcelBaseEnumConverter.class)
    private MaterialTypeEnum type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @ExcelProperty(value = "数量")
    private Integer num;


    @ExcelProperty(value = "单价")
    private BigDecimal unitPrice;
    /**
     * 渠道
     */
    @Schema(description = "渠道")
    @ExcelProperty(value = "渠道")
    private String channel;

    @ExcelProperty(value = "渠道")
    private String channelName;

    private Long channelId;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    @ExcelProperty(value = "支付金额")
    private BigDecimal payPrice;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
}