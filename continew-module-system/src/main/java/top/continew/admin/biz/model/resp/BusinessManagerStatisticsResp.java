package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.apache.commons.lang3.StringUtils;
import top.continew.admin.common.base.BaseResp;

/**
 * 成本分析信息
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
@Data
@Schema(description = "成本分析信息")
public class BusinessManagerStatisticsResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    private LocalDate statisticsDate;

    /**
     * 剩余正常库存
     */
    @Schema(description = "剩余正常库存")
    private Integer remainingNormalInventory;

    /**
     * 库存详情
     */
    private String inventoryDetail;

    /**
     * 当日回收数量
     */
    private Long dailyRecycleCount;

    /**
     * 当天剩余坑位
     */
    private Long dailyRemainingCount;
    /**
     * 备户存活率
     */
    private BigDecimal prepareSurvivalRate;


    /**
     * 当天接入坑位
     */
    @Schema(description = "当天接入坑位")
    private Integer dailyReceivedCount;

    /**
     * 总成本
     */
    @Schema(description = "总成本")
    private BigDecimal totalCost;

    /**
     * 已使用正常
     */
    @Schema(description = "已使用正常")
    private Integer usedNormal;

    /**
     * 未使用正常
     */
    @Schema(description = "未使用正常")
    private Integer unusedNormal;

    /**
     * 已使用封禁
     */
    @Schema(description = "已使用封禁")
    private Integer usedBanned;

    /**
     * 未使用封禁
     */
    @Schema(description = "未使用封禁")
    private Integer unusedBanned;

    /**
     * 剩余正常坑位
     */
    @Schema(description = "剩余正常坑位")
    private Integer remainingNormalCount;

    /**
     * 存活率
     */
    @Schema(description = "存活率")
    private BigDecimal survivalRate;

    /**
     * 平均备户成本
     */
    @Schema(description = "平均备户成本")
    private BigDecimal averagePrepareCost;

    /**
     * 当日出售
     */
    @Schema(description = "当日出售")
    private Integer dailySales;

    /**
     * 当日出售已绑定BM
     */
    private Long dailySalesForBm;

    /**
     * 当日封禁
     */
    @Schema(description = "当日封禁")
    private Integer dailyBanned;

    /**
     * 下户成本
     */
    @Schema(description = "下户成本")
    private BigDecimal orderCost;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    private JSONObject inventoryDetailJson;

    private BigDecimal purchaseCost;

    public JSONObject getInventoryDetailJson() {
        if (StringUtils.isBlank(this.getInventoryDetail())) {
            return new JSONObject();
        }
        return JSONObject.parseObject(this.getInventoryDetail());
    }
}