/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.FbAccountStatusEnum;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.event.FbAccountUpdateEvent;
import top.continew.admin.biz.model.entity.FbAccountDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.system.model.entity.UserDO;

@Service
public class LockStrategyImpl implements ReportOpsStrategy {

    @Override
    public ReportType getReport() {
        return ReportType.LOCK;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
        String platformAdId = browserReq.getRemarkFbAccountId();
        if (StringUtils.isNotBlank(platformAdId)) {
            FbAccountDO fbAccountDO = new FbAccountDO();
            fbAccountDO.setStatus(FbAccountStatusEnum.LOCKED);
            fbAccountDO.setBrowserSerialNo(browserReq.getBrowserNo());
            fbAccountDO.setBrowserId(browserReq.getBrowserId());
            fbAccountDO.setPlatformAccountId(platformAdId);
            SpringUtil.publishEvent(new FbAccountUpdateEvent(fbAccountDO));
        }
        return "";
    }
}
