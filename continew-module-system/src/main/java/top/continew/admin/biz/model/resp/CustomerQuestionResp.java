package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 客户问题信息
 *
 * <AUTHOR>
 * @since 2025/04/30 14:35
 */
@Data
@Schema(description = "客户问题信息")
public class CustomerQuestionResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long customerId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private Integer type;

    /**
     * 问题数量
     */
    @Schema(description = "问题数量")
    private Integer num;

    /**
     * 问题账号
     */
    @Schema(description = "问题账号")
    private String accounts;

    private LocalDateTime orderTime;

    private String remark;
}