package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.TestOrderItemMapper;
import top.continew.admin.biz.model.entity.TestOrderItemDO;
import top.continew.admin.biz.model.query.TestOrderItemQuery;
import top.continew.admin.biz.model.req.TestOrderItemReq;
import top.continew.admin.biz.model.resp.AdAccountTestOrderItemResp;
import top.continew.admin.biz.model.resp.TestOrderItemDetailResp;
import top.continew.admin.biz.model.resp.TestOrderItemResp;
import top.continew.admin.biz.service.TestOrderItemService;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.util.List;

/**
 * 测试任务详情业务实现
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Service
@RequiredArgsConstructor
public class TestOrderItemServiceImpl extends BaseServiceImpl<TestOrderItemMapper, TestOrderItemDO, TestOrderItemResp, TestOrderItemDetailResp, TestOrderItemQuery, TestOrderItemReq> implements TestOrderItemService {

    @Override
    public void batchUpdate(TestOrderItemReq req) {

        boolean flag = req.getStatus() != null || StringUtils.isNotBlank(req.getRemark());

        if (flag) {
            update(new LambdaUpdateWrapper<TestOrderItemDO>()
                    .in(TestOrderItemDO::getId, req.getIds())
                    .set(StringUtils.isNotBlank(req.getRemark()), TestOrderItemDO::getRemark, req.getRemark())
                    .set(req.getStatus() != null, TestOrderItemDO::getStatus, req.getStatus()));
        }


    }

    @Override
    public List<AdAccountTestOrderItemResp> listByAdAccountId(String adAccountId) {

        return baseMapper.listByAdAccountId(adAccountId);
    }

}