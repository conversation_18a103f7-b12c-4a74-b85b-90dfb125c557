package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商务日报信息
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
@Data
@Schema(description = "商务日报信息")
public class SalesDailySummaryResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日报记录的日期
     */
    @Schema(description = "日报记录的日期")
    private LocalDate recordDate;

    /**
     * 日报具体内容，支持长文本
     */
    @Schema(description = "日报具体内容，支持长文本")
    private String content;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}