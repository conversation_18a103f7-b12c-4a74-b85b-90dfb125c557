package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.mapper.AdAccountTransactionMapper;
import top.continew.admin.biz.model.entity.AdAccountTransactionDO;
import top.continew.admin.biz.model.query.AdAccountTransactionQuery;
import top.continew.admin.biz.model.req.AdAccountTransactionReq;
import top.continew.admin.biz.model.resp.AdAccountTransactionDetailResp;
import top.continew.admin.biz.model.resp.AdAccountTransactionResp;
import top.continew.admin.biz.service.AdAccountTransactionService;
import top.continew.admin.biz.utils.RabbitUtils;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 广告户交易记录业务实现
 *
 * <AUTHOR>
 * @since 2025/03/10 14:15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdAccountTransactionServiceImpl extends BaseServiceImpl<AdAccountTransactionMapper, AdAccountTransactionDO, AdAccountTransactionResp, AdAccountTransactionDetailResp, AdAccountTransactionQuery, AdAccountTransactionReq> implements AdAccountTransactionService {

    private final TelegramChatIdConfig telegramChatIdConfig;

    @Override
    public void syncAdAccountBill(String envId, String serialNumber, String proxy, String headers, boolean isLite) {
        String res;
        if (StringUtils.isNotBlank(serialNumber)) {
            res = RabbitUtils.getPaymentTransactionsV2(serialNumber, false, proxy, headers);
        } else {
            res = RabbitUtils.getPaymentTransactions(envId, false);
        }
        String browserKey = StringUtils.defaultIfBlank(envId, serialNumber);
        JSONObject jsonObject = JSONObject.parseObject(res);
        int code = jsonObject.getInteger("code");
        if (code != 0) {
            log.error("【{}】账单同步失败：{}", browserKey, jsonObject.getString("msg"));
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getAdAccountNotifyChatId())
                .text(browserKey + "账单数据同步失败：" + jsonObject.getString("msg"))
                .build()));
            return;
        }
        JSONArray adAccountBills = jsonObject.getJSONArray("data");
        List<AdAccountTransactionDO> saveList = new ArrayList<>();
        Set<String> adAccountIds = CollUtil.newHashSet();
        for (Object adAccountBill : adAccountBills) {
            JSONObject item = JSONObject.from(adAccountBill);
            int itemCode = item.getIntValue("code");
            if (itemCode != 200) {
                continue;
            }
            JSONObject data = item.getJSONObject("body");
            String adAccountId = data.getString("account_id");
            JSONArray billData = data.getJSONArray("data");
            if (CollUtil.isEmpty(billData)) {
                continue;
            }
            adAccountIds.add(adAccountId);
            for (Object bill : billData) {
                JSONObject billJson = JSONObject.from(bill);
                AdAccountTransactionDO adAccountTransactionDO = new AdAccountTransactionDO();
                adAccountTransactionDO.setPlatformAdId(adAccountId);
                adAccountTransactionDO.setTransactionId(billJson.getString("id"));
                adAccountTransactionDO.setTransactionType(billJson.getString("transaction_type"));
                adAccountTransactionDO.setAmount(new BigDecimal(billJson.getJSONObject("amount")
                    .getString("total_amount")
                    .replaceAll(",", "")));
                adAccountTransactionDO.setTransTime(LocalDateTimeUtil.of(Instant.ofEpochSecond(billJson.getLong("time"))));
                adAccountTransactionDO.setStatus(billJson.getString("status"));
                saveList.add(adAccountTransactionDO);
            }
        }
        if (!adAccountIds.isEmpty()) {
            remove(new LambdaQueryWrapper<AdAccountTransactionDO>().in(AdAccountTransactionDO::getPlatformAdId, adAccountIds));
            saveBatch(saveList);
        }
    }

    @Override
    public void insertAdAccountTransaction(String platformAdId, JSONArray transactions) {
        if (transactions == null || transactions.isEmpty()) {
            return;
        }
        this.remove(new LambdaQueryWrapper<AdAccountTransactionDO>().eq(AdAccountTransactionDO::getPlatformAdId, platformAdId));
        List<AdAccountTransactionDO> saveList = new ArrayList<>();
        for (int i = 0; i < transactions.size(); i++) {
            JSONObject item = transactions.getJSONObject(i);
            AdAccountTransactionDO adAccountTransactionDO = new AdAccountTransactionDO();
            adAccountTransactionDO.setPlatformAdId(platformAdId);
            adAccountTransactionDO.setTransactionId(item.getString("id"));
            adAccountTransactionDO.setTransactionType(item.getString("transaction_type"));
            adAccountTransactionDO.setAmount(new BigDecimal(item.getJSONObject("amount")
                .getString("total_amount")
                .replaceAll(",", "")));
            adAccountTransactionDO.setTransTime(LocalDateTimeUtil.of(Instant.ofEpochSecond(item.getLong("time"))));
            adAccountTransactionDO.setStatus(item.getString("status"));
            saveList.add(adAccountTransactionDO);
        }
        this.saveBatch(saveList);
    }

    @Override
    public void export(AdAccountTransactionQuery query, SortQuery sortQuery, HttpServletResponse response) {
        List<AdAccountTransactionDetailResp> list = this.list(query, sortQuery, this.getDetailClass());
        list.forEach(this::fill);
        for (AdAccountTransactionDetailResp adAccountTransactionDetailResp : list) {
            adAccountTransactionDetailResp.setAmount(adAccountTransactionDetailResp.getAmount().negate());
        }
        ExcelUtils.export(list, "导出数据", this.getDetailClass(), response);
    }
}