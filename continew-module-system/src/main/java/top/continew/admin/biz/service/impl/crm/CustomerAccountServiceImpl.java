package top.continew.admin.biz.service.impl.crm;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.mapper.crm.CustomerAccountMapper;
import top.continew.admin.biz.mapper.crm.CustomerAccountRelMapper;
import top.continew.admin.biz.model.entity.crm.CustomerAccountDO;
import top.continew.admin.biz.model.entity.crm.CustomerAccountRelDO;
import top.continew.admin.biz.model.req.crm.CustomerAccountAddReq;
import top.continew.admin.biz.model.resp.crm.CustomerAccountResp;
import top.continew.admin.biz.service.crm.CustomerAccountService;
import top.continew.starter.core.validation.ValidationUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * 客户账号信息业务实现
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Service
@RequiredArgsConstructor
public class CustomerAccountServiceImpl implements CustomerAccountService {
    private final CustomerAccountMapper customerAccountMapper;
    private final CustomerAccountRelMapper customerAccountRelMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCustomerAccount(List<CustomerAccountAddReq> accounts, Integer entityType, Long entityId) {
        // 先查询现有的账号列表
        List<CustomerAccountResp> existingAccounts = listCustomerAccounts(entityType, entityId);

        // 删除不在新列表中的账号
        for (CustomerAccountResp existingAccount : existingAccounts) {
            boolean found = false;
            for (CustomerAccountAddReq newAccount : accounts) {
                if (existingAccount.getAccount().equals(newAccount.getAccount())
                        && existingAccount.getAccountType().equals(newAccount.getAccountType())) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                deleteCustomerAccountRel(existingAccount.getRelId());
            }
        }

        // 添加或更新新的账号
        for (CustomerAccountAddReq account : accounts) {
            // 1. 检查账号是否已存在
            CustomerAccountDO existAccount = customerAccountMapper.selectByAccountAndType(account.getAccount(), account.getAccountType());

            Long accountId;
            // 如果账号不存在，则新增账号
            if (existAccount == null) {
                CustomerAccountDO accountDO = new CustomerAccountDO();
                accountDO.setAccountType(account.getAccountType());
                accountDO.setAccount(account.getAccount());
                accountDO.setSocialAccountId(account.getSocialAccountId());
                customerAccountMapper.insert(accountDO);
                accountId = accountDO.getId();
            } else {
                // 如果账号已存在，则更新账号信息
                existAccount.setSocialAccountId(account.getSocialAccountId());
                customerAccountMapper.updateById(existAccount);
                accountId = existAccount.getId();
            }

            // 2. 检查关联关系是否已存在
            CustomerAccountRelDO existRel = customerAccountRelMapper.selectByAccountIdAndEntityTypeAndEntityId(
                    accountId, entityType, entityId);

            // 如果关联关系不存在，则新增关联关系
            if (existRel == null) {
                CustomerAccountRelDO relDO = new CustomerAccountRelDO();
                relDO.setCustomerAccountId(accountId);
                relDO.setEntityType(entityType);
                relDO.setEntityId(entityId);
                customerAccountRelMapper.insert(relDO);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCustomerAccount(Integer entityType, Long entityId) {
        // 1. 查询该实体下的所有关联关系
        List<CustomerAccountRelDO> relList = customerAccountRelMapper.selectList(
                new LambdaQueryWrapper<CustomerAccountRelDO>().eq(CustomerAccountRelDO::getEntityType, entityType).eq(CustomerAccountRelDO::getEntityId, entityId));
        if (CollUtil.isEmpty(relList)) {
            return;
        }

        // 2. 删除关联关系
        customerAccountRelMapper.delete(new LambdaQueryWrapper<CustomerAccountRelDO>().eq(CustomerAccountRelDO::getEntityType, entityType).eq(CustomerAccountRelDO::getEntityId, entityId));

        // 3. 检查并删除没有其他关联的账号
        for (CustomerAccountRelDO rel : relList) {
            int count = customerAccountRelMapper.countByAccountId(rel.getCustomerAccountId());
            if (count == 0L) {
                customerAccountMapper.deleteById(rel.getCustomerAccountId());
            }
        }
    }

    private void deleteCustomerAccountRel(Long relId) {
        // 获取关联关系
        CustomerAccountRelDO relDO = customerAccountRelMapper.selectById(relId);
        ValidationUtils.throwIf(null == relDO, "客户社交账号关联不存在");

        // 删除关联关系
        customerAccountRelMapper.deleteById(relId);

        // 检查该账号是否还有其他关联，如果没有则删除账号
        int count = customerAccountRelMapper.countByAccountId(relDO.getCustomerAccountId());
        if (count == 0) {
            customerAccountMapper.deleteById(relDO.getCustomerAccountId());
        }
    }

    @Override
    public List<CustomerAccountResp> listCustomerAccounts(Integer entityType, Long entityId) {
        if (entityType == null || entityId == null) {
            return new ArrayList<>();
        }

        // 查询指定实体的所有社交账号关联
        return customerAccountRelMapper.selectAccountsByEntityTypeAndEntityId(
                entityType, entityId);
    }

    @Override
    public void addCustomerAccountRel(CustomerAccountRelDO rel) {
        customerAccountRelMapper.insert(rel);
    }


}