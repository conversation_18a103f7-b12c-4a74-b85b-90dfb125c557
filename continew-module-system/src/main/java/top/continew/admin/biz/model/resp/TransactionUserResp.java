package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 交易对象信息
 *
 * <AUTHOR>
 * @since 2025/08/17 14:22
 */
@Data
@Schema(description = "交易对象信息")
public class TransactionUserResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    private TransactionUserTypeEnum transactionType;

    /**
     * 关联ID
     */
    @Schema(description = "关联ID")
    private Long referId;

    /**
     * 名字
     */
    @Schema(description = "名字")
    private String name;
}