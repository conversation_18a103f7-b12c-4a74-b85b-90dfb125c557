/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.AdAccountSaleStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.event.AdAccountAddEvent;
import top.continew.admin.biz.event.AdAccountStatusChangeEvent;
import top.continew.admin.biz.event.AdAccountUpdateEvent;
import top.continew.admin.biz.event.FbAccountUpdateEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.FbAccountDO;
import top.continew.admin.biz.model.entity.PersonalAccountDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.service.PersonalAccountService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.admin.system.model.entity.UserDO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class BillingHubPaymentSettingsViewQueryStrategyImpl implements ReportOpsStrategy {

    private final PersonalAccountService personalAccountService;

    @Override
    public ReportType getReport() {
        return ReportType.BILLING_HUB_PAYMENT_SETTINGS_VIEW_QUERY;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
        FbAccountDO fbAccountDO = new FbAccountDO();
        fbAccountDO.setBrowserSerialNo(browserReq.getBrowserNo());
        fbAccountDO.setBrowserId(browserReq.getBrowserId());
        fbAccountDO.setPlatformAccountId(browserReq.getFbAccountId());
        SpringUtil.publishEvent(new FbAccountUpdateEvent(fbAccountDO));
        JSONObject data = JSONObject.from(browserReq.getData());
        JSONArray res = data.getJSONArray("res");
        JSONObject billing = res.getJSONObject(0);
        JSONObject billingData = billing.getJSONObject("data");
        JSONObject billableAccount = billingData.getJSONObject("billable_account_by_asset_id");
        if (billableAccount == null) {
            return "";
        }
        JSONObject billingPaymentAccount = billableAccount.getJSONObject("billing_payment_account");
        if (billingPaymentAccount == null) {
            return "";
        }
        String platformAdId = billingPaymentAccount.getString("payment_legacy_account_id");
        String accountStatus = billableAccount.getString("account_status");
        AdAccountStatusEnum accountStatusEnum = switch (accountStatus) {
            case "DISABLED" -> AdAccountStatusEnum.BANNED;
            case "UNSETTLED" -> AdAccountStatusEnum.ABNORMAL;
            case "PENDING_CLOSURE" -> AdAccountStatusEnum.PENDING_CLOSURE;
            case "CLOSED" -> AdAccountStatusEnum.CLOSED;
            default -> AdAccountStatusEnum.NORMAL;
        };
        String headers = data.getString("headers");
        // 更新三解号的headers数据
        if (StringUtils.isNotBlank(headers)) {
            personalAccountService.update(Wrappers.<PersonalAccountDO>lambdaUpdate()
                .set(PersonalAccountDO::getHeaders, headers)
                .eq(PersonalAccountDO::getPlatformAccountId, browserReq.getFbAccountId()));
        }
        AdAccountDO accountDO = new AdAccountDO.builder().platformAdId(platformAdId)
            .browserNo(browserReq.getBrowserNo())
            .browserId(browserReq.getBrowserId())
            .saleStatus(platformAdId.length() <= 9 ? AdAccountSaleStatusEnum.LOCK : AdAccountSaleStatusEnum.WAIT)
            .accountStatus(accountStatusEnum)
            .platformAccountId(browserReq.getFbAccountId())
            .createUser(userDO.getId())
            .build();
        SpringUtil.publishEvent(new AdAccountAddEvent(accountDO));
        JSONObject spendingInfo = billableAccount.getJSONObject("spending_info");
        BigDecimal spendCap = Optional.ofNullable(spendingInfo.getJSONObject("spend_limit_currency_amount"))
            .map(v -> v.getBigDecimal("amount_with_offset").divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP))
            .orElse(BigDecimal.valueOf(-1));
        BigDecimal amountSpend = spendingInfo.getJSONObject("amount_spent_currency_amount")
            .getBigDecimal("amount_with_offset")
            .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        JSONObject balanceInfo = billableAccount.getJSONObject("balance_info");
        BigDecimal balance = balanceInfo.getJSONObject("balance")
            .getBigDecimal("amount_with_offset")
            .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        String currency = billableAccount.getString("currency");
        String billCountry = null;
        JSONObject billableAccountTaxInfo = billableAccount.getJSONObject("billable_account_tax_info");
        if (billableAccountTaxInfo != null) {
            billCountry = billableAccountTaxInfo.getString("business_country_code");
        }
        String timezoneName = Optional.ofNullable(billableAccount.getJSONObject("timezone_info"))
            .map(v -> v.getString("timezone"))
            .orElse(null);
        String timezone = FacebookUtils.getTimezone(timezoneName);

        // 付费门槛
        BigDecimal thresholdAmount = BigDecimal.ZERO;
        JSONObject billingThresholdCurrencyAmount = billableAccount.getJSONObject("billing_threshold_currency_amount");
        if (billingThresholdCurrencyAmount != null) {
            thresholdAmount = CommonUtils.divide100(billingThresholdCurrencyAmount.getBigDecimal("amount_with_offset"), null);
        }

        // 预充户余额
        JSONObject prepayBalance = billableAccount.getJSONObject("prepay_balance");
        BigDecimal prepayAccountBalance = null;
        if (prepayBalance != null) {
            prepayAccountBalance = CommonUtils.divide100(prepayBalance.getBigDecimal("amount_with_offset"), null);
        }
        AdAccountDO update = new AdAccountDO.builder().platformAdId(platformAdId)
            .spendCap(spendCap)
            .amountSpent(amountSpend)
            .balance(balance)
            .headers(headers)
            .billCurrency(currency)
            .billCountry(billCountry)
            .billingThresholdCurrencyAmount(thresholdAmount)
            .prepayAccountBalance(prepayAccountBalance)
            .timezone(timezone)
            .build();
        JSONObject jsonObject = JSONObject.from(update);
        SpringUtil.publishEvent(new AdAccountUpdateEvent(update));
        AdAccountDO statusChange = new AdAccountDO.builder().platformAdId(platformAdId)
            .accountStatus(accountStatusEnum)
            .build();
        SpringUtil.publishEvent(new AdAccountStatusChangeEvent(statusChange));
        jsonObject.put("accountStatus", accountStatus);
        jsonObject.remove("headers");
        return jsonObject.toString();
    }
}
