package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.query.CustomerRequirementQuery;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.CustomerRequirementDO;

import java.util.List;
import java.util.Map;

/**
* 客户需求 Mapper
*
* <AUTHOR>
* @since 2025/02/20 14:03
*/
public interface CustomerRequirementMapper extends BaseMapper<CustomerRequirementDO> {
    List<Map<String, Object>> getSummary(@Param("query") CustomerRequirementQuery query);
}