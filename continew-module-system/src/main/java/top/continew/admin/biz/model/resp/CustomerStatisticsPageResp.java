package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.model.resp.CustomerStatisticsOverviewResp;
import top.continew.admin.biz.model.resp.CustomerStatisticsResp;
import top.continew.starter.extension.crud.model.resp.PageResp;

@Schema(description = "客户统计分页响应")
@Data
public class CustomerStatisticsPageResp {
    
    @Schema(description = "分页数据")
    private PageResp<CustomerStatisticsResp> pageData;
    
    @Schema(description = "统计总览")
    private CustomerStatisticsOverviewResp overview;
}
