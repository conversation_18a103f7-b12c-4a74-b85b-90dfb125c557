package top.continew.admin.biz.model.resp;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 客户条件查询结果DTO
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerConditionResp {
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 条件类型
     */
    private String conditionType;
    
    /**
     * 条件值（如户数、消耗金额等）
     */
    private Object conditionValue;
    
    /**
     * 条件描述（用于回访记录）
     */
    private String conditionDescription;
    
    /**
     * 构造广告户数结果
     */
    public static CustomerConditionResp ofAdAccountCount(Long customerId, Integer accountCount) {
        return new CustomerConditionResp(
            customerId, 
            "adAccountCount", 
            accountCount, 
            "广告户数量:" + accountCount
        );
    }
    
    /**
     * 构造正常户数结果
     */
    public static CustomerConditionResp ofNormalAccountCount(Long customerId, Integer accountCount) {
        return new CustomerConditionResp(
            customerId, 
            "normalAccountCount", 
            accountCount, 
            "正常户数量:" + accountCount
        );
    }
    
    /**
     * 构造空置户数结果
     */
    public static CustomerConditionResp ofEmptyAccountCount(Long customerId, Integer accountCount) {
        return new CustomerConditionResp(
            customerId, 
            "emptyAccountCount", 
            accountCount, 
            "空置户数量:" + accountCount
        );
    }
    
    /**
     * 构造消耗金额结果
     */
    public static CustomerConditionResp ofConsumptionAmount(Long customerId, Double amount) {
        return new CustomerConditionResp(
            customerId, 
            "consumptionAmount", 
            amount, 
            "总消耗额:" + amount
        );
    }
}