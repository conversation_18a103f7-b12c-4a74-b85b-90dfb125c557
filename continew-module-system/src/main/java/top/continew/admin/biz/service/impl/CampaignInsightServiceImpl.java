/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.mapper.CampaignInsightMapper;
import top.continew.admin.biz.model.entity.CampaignInsightDO;
import top.continew.admin.biz.service.CampaignInsightService;

import java.time.LocalDate;
import java.util.List;

/**
 * 广告组成效数据业务实现
 *
 * <AUTHOR>
 * @since 2025/01/03 15:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CampaignInsightServiceImpl extends ServiceImpl<CampaignInsightMapper, CampaignInsightDO> implements CampaignInsightService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveOrUpdate(List<CampaignInsightDO> insights) {
        if (insights == null || insights.isEmpty()) {
            log.warn("广告组成效数据列表为空，跳过保存操作");
            return;
        }
        
        log.info("开始批量保存或更新广告组成效数据，数据量: {}", insights.size());
        int affectedRows = baseMapper.batchInsertOrUpdate(insights);
        log.info("批量保存或更新广告组成效数据完成，影响行数: {}", affectedRows);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanupObsoleteData(String adAccountId, LocalDate statDate) {
        if (adAccountId == null || statDate == null) {
            log.warn("广告户ID或统计日期为空，跳过清理操作");
            return;
        }
        
        log.info("开始清理广告户 {} 在日期 {} 之外的过期成效数据", adAccountId, statDate);
        int deletedRows = baseMapper.deleteByAdAccountIdAndStatDateNot(adAccountId, statDate);
        log.info("清理过期成效数据完成，删除行数: {}", deletedRows);
    }
}