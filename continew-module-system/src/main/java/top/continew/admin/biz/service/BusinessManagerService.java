/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;
import top.continew.admin.biz.model.entity.BusinessManagerDO;
import top.continew.admin.biz.model.query.BusinessManagerQuery;
import top.continew.admin.biz.model.req.BusinessManagerReq;
import top.continew.admin.biz.model.resp.BusinessManagerDetailResp;
import top.continew.admin.biz.model.resp.BusinessManagerResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDate;

/**
 * BM5账号业务接口
 *
 * <AUTHOR>
 * @since 2024/12/30 17:48
 */
public interface BusinessManagerService extends BaseService<BusinessManagerResp, BusinessManagerDetailResp, BusinessManagerQuery, BusinessManagerReq>, IService<BusinessManagerDO> {

    void importExcel(MultipartFile file);

    /**
     * 已使用
     *
     * @param id
     */
    void used(Long id);

    /**
     * 增加坑位数量
     *
     * @param bmId
     */
    void addNum(String bmId);

    /**
     * 账号封禁
     *
     * @param id
     * @param reason 封禁原因
     */
    void ban(Long id, BusinessManagerBannedReasonEnum reason);

    /**
     * 账号正常
     *
     * @param id
     */
    void normal(Long id);

    /**
     * 计算备户成本
     */
    void calcAdAccountCost(LocalDate statDate);

    /**
     * 同步数据
     *
     * @param envId
     * @param serialNumber
     * @param proxy
     * @param headers
     */
    void syncData(String envId, String serialNumber, String proxy, String headers);
}