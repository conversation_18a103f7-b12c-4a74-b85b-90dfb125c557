package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.model.resp.crm.OperatorTaskSummaryStatResp;

import java.util.List;

@Data
@Schema(description = "工作记录统计")
public class operationTaskStatResp {
    private Long userId;
    private String username;
    private Integer total;
    private List<OperatorTaskSummaryStatResp> dateStatList;
}
