package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "广告账号养号状态统计响应")
public class AdAccountKeepStatusStatResp {
    
    @Schema(description = "养号状态")
    private Integer keepStatus;
    
    @Schema(description = "时区")
    private String timezone;
    
    @Schema(description = "数量")
    private Integer count;
}