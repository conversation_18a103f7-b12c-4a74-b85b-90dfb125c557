package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.ProfitMapper;
import top.continew.admin.biz.model.entity.ProfitDO;
import top.continew.admin.biz.model.query.ProfitQuery;
import top.continew.admin.biz.model.req.ProfitReq;
import top.continew.admin.biz.model.resp.ProfitDetailResp;
import top.continew.admin.biz.model.resp.ProfitResp;
import top.continew.admin.biz.service.ProfitService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.util.List;

/**
 * 利润业务实现
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Service
@RequiredArgsConstructor
public class ProfitServiceImpl extends BaseServiceImpl<ProfitMapper, ProfitDO, ProfitResp, ProfitDetailResp, ProfitQuery, ProfitReq> implements ProfitService {

    @Override
    public PageResp<ProfitResp> page(ProfitQuery query, PageQuery pageQuery) {
        QueryWrapper<ProfitDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, pageQuery);
        IPage<ProfitResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        page.getRecords().forEach(this::fill);
        return PageResp.build(page);
    }

    @Override
    protected <E> List<E> list(ProfitQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<ProfitDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        List<ProfitDetailResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        entityList.forEach(this::fill);
        return BeanUtil.copyToList(entityList, targetClass);
    }
}