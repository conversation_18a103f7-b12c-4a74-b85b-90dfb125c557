package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.CustomerWithdrawOrderStatusEnum;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.extension.crud.constant.ContainerPool;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

/**
 * 客户余额提现订单详情信息
 *
 * <AUTHOR>
 * @since 2025/01/22 14:23
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "客户余额提现订单详情信息")
public class CustomerWithdrawOrderDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @ExcelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 订单状态（1:审核中, 2:审核通过, 3:审核拒绝, 4:退款完成, 5:已取消）
     */
    @Schema(description = "订单状态（1:审核中, 2:审核通过, 3:审核拒绝, 4:退款完成, 5:已取消）")
    @ExcelProperty(value = "订单状态", converter = ExcelBaseEnumConverter.class)
    private CustomerWithdrawOrderStatusEnum status;

    /**
     * 关联客户ID
     */
    @Schema(description = "关联客户ID")
    @AssembleMethod(props = @Mapping(src = "name", ref = "customerName"), targetType = CustomerService.class, method = @ContainerMethod(bindMethod = "get", resultType = CustomerResp.class))
    private Long customerId;

    @ExcelProperty(value = "关联客户")
    private String customerName;

    /**
     * 预计退款时间
     */
    @Schema(description = "预计退款时间")
    @ExcelProperty(value = "预计退款时间")
    private LocalDateTime expectedRefundTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 审核人
     */
    @Schema(description = "审核人")
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "auditorName"))
    private Long auditor;

    @ExcelProperty(value = "审核人")
    private String auditorName;

    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    @ExcelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    @ExcelProperty(value = "审核备注")
    private String auditRemark;

    /**
     * 实际退款时间
     */
    @Schema(description = "实际退款时间")
    @ExcelProperty(value = "实际退款时间")
    private LocalDateTime actualRefundTime;

    /**
     * 实际退款金额
     */
    @Schema(description = "实际退款金额")
    @ExcelProperty(value = "实际退款金额")
    private BigDecimal actualRefundAmount;
}