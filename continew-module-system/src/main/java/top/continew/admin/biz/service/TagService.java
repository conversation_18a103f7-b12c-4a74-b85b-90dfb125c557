package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.TagDO;
import top.continew.admin.biz.model.req.DeleteTagReq;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.TagQuery;
import top.continew.admin.biz.model.req.TagReq;
import top.continew.admin.biz.model.resp.TagDetailResp;
import top.continew.admin.biz.model.resp.TagResp;

/**
 * 标签业务接口
 *
 * <AUTHOR>
 * @since 2025/05/07 18:02
 */
public interface TagService extends BaseService<TagResp, TagDetailResp, TagQuery, TagReq>, IService<TagDO> {

    void deleteTag(DeleteTagReq req);
}