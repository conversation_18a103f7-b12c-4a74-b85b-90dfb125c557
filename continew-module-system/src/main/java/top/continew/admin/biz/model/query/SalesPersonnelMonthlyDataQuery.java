package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 商务人员月度绩效数据查询条件
 *
 * <AUTHOR>
 * @since 2025/08/20 10:43
 */
@Data
@Schema(description = "商务人员月度绩效数据查询条件")
public class SalesPersonnelMonthlyDataQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @Query(type = QueryType.EQ)
    private Long businessUserId;

    /**
     * 岗位类型
     */
    @Schema(description = "岗位类型")
    @Query(type = QueryType.EQ)
    private Integer jobRank;

    /**
     * 统计月份
     */
    @Schema(description = "统计月份")
    @Query(type = QueryType.EQ)
    private LocalDate salesDate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.EQ)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Query(type = QueryType.EQ)
    private Long createUser;
}