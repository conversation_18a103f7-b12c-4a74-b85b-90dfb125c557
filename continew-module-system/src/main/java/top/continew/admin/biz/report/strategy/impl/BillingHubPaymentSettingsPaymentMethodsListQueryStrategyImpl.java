/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.event.AdAccountPaymentQueryEvent;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.model.BillingPaymentAccountModel;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.system.model.entity.UserDO;

@Service
public class BillingHubPaymentSettingsPaymentMethodsListQueryStrategyImpl implements ReportOpsStrategy {

    @Override
    public ReportType getReport() {
        return ReportType.BILLING_HUB_PAYMENT_SETTINGS_PAYMENT_METHODS_LIST_QUERY;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
        JSONObject data = JSONObject.from(browserReq.getData());
        String platformAdId = data.getJSONObject("body").getString("paymentAccountID");
        JSONObject billableAccountByPaymentAccount = data.getJSONObject("res")
            .getJSONObject("data")
            .getJSONObject("billable_account_by_payment_account");
        if (billableAccountByPaymentAccount == null) {
            return "";
        }
        JSONObject billingPaymentAccount = billableAccountByPaymentAccount.getJSONObject("billing_payment_account");
        if (billingPaymentAccount == null) {
            return "";
        }
        JSONArray billingPaymentMethods = billingPaymentAccount.getJSONArray("billing_payment_methods");
        if (billingPaymentMethods == null || billingPaymentMethods.isEmpty()) {
            return "";
        }
        BillingPaymentAccountModel paymentAccountModel = new BillingPaymentAccountModel();
        paymentAccountModel.setPlatformAdId(platformAdId);
        for (int i = 0; i < billingPaymentMethods.size(); i++) {
            JSONObject billingPaymentMethod = billingPaymentMethods.getJSONObject(i);
            JSONObject credential = billingPaymentMethod.getJSONObject("credential");
            String lastFourDigits = credential.getString("last_four_digits");
            if (StringUtils.isBlank(lastFourDigits)) {
                continue;
            }
            paymentAccountModel
                .addPaymentMethod(new BillingPaymentAccountModel.BillingPaymentMethod("", lastFourDigits, credential
                    .getString("expiry_year"), credential.getString("expiry_month"), credential
                        .getString("card_association"), billingPaymentMethod.getBooleanValue("is_primary")));
        }
        SpringUtil.publishEvent(new AdAccountPaymentQueryEvent(paymentAccountModel));
        return JSONObject.toJSONString(paymentAccountModel);
    }
}
