package top.continew.admin.biz.mapper.crm;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.crm.CustomerAccountDO;

/**
 * 客户账号信息 Mapper
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
public interface CustomerAccountMapper extends BaseMapper<CustomerAccountDO> {

    /**
     * 根据账号和类型查询账号信息
     *
     * @param account 账号
     * @param accountType 账号类型
     * @return 账号信息
     */
    CustomerAccountDO selectByAccountAndType(@Param("account") String account, @Param("accountType") Integer accountType);
}