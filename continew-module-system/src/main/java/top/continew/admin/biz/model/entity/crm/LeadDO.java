package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.LeadStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 线索实体
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@TableName("biz_lead")
public class LeadDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 状态：1-待跟进、2-跟进中、3-长期跟进、4-创建商机、5-无效
     */
    private LeadStatusEnum status;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户行业
     */
    private Integer customerIndustry;

    /**
     * 需求内容
     */
    private String requirement;

    /**
     * 提醒时间
     */
    private LocalDateTime remindTime;

    /**
     * 无效原因
     */
    private Integer invalidReason;

    /**
     * 关联商机ID
     */
    private Long opportunityId;

    /**
     * 最近跟进时间
     */
    private LocalDateTime lastFollowTime;

    /**
     * 对接人
     */
    private Long handlerUserId;

    /**
     * 备注
     */
    private String remark;

    private String companyName;

    private String customerPosition;

    private String city;

    private String teamSize;

    private String dailyTeamSpending;

    private String productName;
}