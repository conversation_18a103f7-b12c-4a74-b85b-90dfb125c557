/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.*;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 卡片查询条件
 *
 * <AUTHOR>
 * @since 2024/12/28 10:43
 */
@Data
@Schema(description = "卡片查询条件")
public class CardQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡号
     */
    @Schema(description = "卡号")
    @Query(type = QueryType.LIKE_LEFT, columns = "card.card_number")
    private String cardNumber;

    /**
     * 所属平台
     */
    @Schema(description = "所属平台")
    @Query(type = QueryType.EQ, columns = "card.platform")
    private CardPlatformEnum platform;
    /**
     * 状态(1=正常，2=锁定，3=冻结）
     */
    @Schema(description = "状态(1=正常，2=锁定，3=冻结）")
    @Query(type = QueryType.EQ, columns = "card.status")
    private CardStatusEnum status;

    /**
     * 第三方平台ID
     */
    @Schema(description = "第三方平台ID")
    @Query(type = QueryType.EQ)
    private String platformCardId;

    @Query(type = QueryType.LIKE, columns = "card.remark")
    private String remark;

    @Query(type = QueryType.EQ, columns = "a.keep_status")
    private AdAccountKeepStatusEnum keepStatus;

    @Query(type = QueryType.EQ, columns = "a.account_status")
    private AdAccountStatusEnum accountStatus;

    @Query(type = QueryType.EQ, columns = "a.sale_status")
    private AdAccountSaleStatusEnum saleStatus;

    @Query(type = QueryType.EQ, columns = "a.clear_status")
    private AdAccountClearStatusEnum clearStatus;

    @QueryIgnore
    private Long customerId;

    @Query(type = QueryType.EQ, columns = "card.ad_platform")
    private AdPlatformEnum adPlatform;

    @Query(type = QueryType.EQ, columns = "card.platform_ad_id")
    private String platformAdId;
}