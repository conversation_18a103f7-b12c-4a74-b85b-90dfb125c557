package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.AdProductTypeEnum;
import top.continew.admin.common.base.BaseDetailResp;

/**
 * 利润类型详情信息
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "利润类型详情信息")
public class ProfitTypeDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 媒体平台
     */
    @Schema(description = "媒体平台")
    @ExcelProperty(value = "媒体平台")
    private AdProductTypeEnum adPlatform;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @ExcelProperty(value = "名称")
    private String name;
}