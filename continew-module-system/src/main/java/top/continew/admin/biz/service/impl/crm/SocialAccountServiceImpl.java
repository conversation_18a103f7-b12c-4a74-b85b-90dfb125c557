package top.continew.admin.biz.service.impl.crm;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.admin.biz.mapper.crm.SocialAccountMapper;
import top.continew.admin.biz.model.entity.crm.SocialAccountDO;
import top.continew.admin.biz.model.req.crm.SocialAccountReq;
import top.continew.admin.biz.model.req.crm.SocialAccountUpdateStatusReq;
import top.continew.admin.biz.model.req.crm.SocialAccountUpdateAssigneeReq;
import top.continew.admin.biz.model.resp.crm.SocialAccountDetailResp;
import top.continew.admin.biz.model.resp.crm.SocialAccountResp;
import top.continew.admin.biz.service.crm.SocialAccountService;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import top.continew.admin.biz.model.query.crm.SocialAccountQuery;

/**
 * 社交账号业务实现
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Service
@RequiredArgsConstructor
public class SocialAccountServiceImpl extends BaseServiceImpl<SocialAccountMapper, SocialAccountDO, SocialAccountResp, SocialAccountDetailResp, SocialAccountQuery, SocialAccountReq> implements SocialAccountService {

    @Override
    public void batchUpdateStatus(SocialAccountUpdateStatusReq req) {
        LambdaUpdateWrapper<SocialAccountDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(SocialAccountDO::getId, req.getIds())
               .set(SocialAccountDO::getStatus, req.getStatus());
        baseMapper.update(null, wrapper);
    }

    @Override
    public void batchUpdateAssignee(SocialAccountUpdateAssigneeReq req) {
        LambdaUpdateWrapper<SocialAccountDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(SocialAccountDO::getId, req.getIds())
               .set(SocialAccountDO::getAssigneeId, req.getAssigneeId());
        baseMapper.update(null, wrapper);
    }
}