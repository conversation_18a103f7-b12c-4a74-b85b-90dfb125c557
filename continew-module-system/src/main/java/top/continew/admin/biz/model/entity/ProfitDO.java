package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.AdPlatformEnum;
import top.continew.admin.biz.enums.AdProjectEnum;
import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 利润实体
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Data
@TableName("biz_profit")
public class ProfitDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 媒体平台
     */
    private AdPlatformEnum adPlatform;

    /**
     * 项目
     */
    private AdProjectEnum project;

    /**
     * 类型
     */
    private Long type;

    private Long transactionUserId;

    private TransactionUserTypeEnum transactionUserType;

    private Integer num;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易时间
     */
    private LocalDateTime transTime;

    /**
     * 交易哈希
     */
    private String transactionHash;

    /**
     * 备注
     */
    private String remark;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;
}