/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.AdAccountSaleStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.enums.FbAccountStatusEnum;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.event.AdAccountAddEvent;
import top.continew.admin.biz.event.FbAccountUpdateEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.FbAccountDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.system.model.entity.UserDO;

@Service
public class CampaignsPageLoadedStrategyImpl implements ReportOpsStrategy {
    @Override
    public ReportType getReport() {
        return ReportType.CAMPAIGNS_PAGE_LOADED;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
        JSONObject data = JSONObject.from(browserReq.getData());
        JSONObject res = data.getJSONObject("res");
        String platformAdId = res.getString("account_id");
        int accountStatus = res.getIntValue("account_status");
//        AdAccountDO accountDO = new AdAccountDO.builder().platformAdId(platformAdId)
//            .browserNo(browserReq.getBrowserNo())
//            .saleStatus(platformAdId.length() <= 9 ? AdAccountSaleStatusEnum.LOCK : AdAccountSaleStatusEnum.WAIT)
//            .accountStatus(accountStatus == 1 ? AdAccountStatusEnum.NORMAL : AdAccountStatusEnum.BANNED)
//            .platformAccountId(browserReq.getFbAccountId())
//            .createUser(userDO.getId())
//            .build();
//        SpringUtil.publishEvent(new AdAccountAddEvent(accountDO));
        FbAccountDO fbAccountDO = new FbAccountDO();
        fbAccountDO.setBrowserSerialNo(browserReq.getBrowserNo());
        fbAccountDO.setBrowserId(browserReq.getBrowserId());
        fbAccountDO.setStatus(accountStatus == FbAccountStatusEnum.NORMAL.getValue()
            ? FbAccountStatusEnum.NORMAL
            : FbAccountStatusEnum.BANNED);
        fbAccountDO.setPlatformAccountId(browserReq.getFbAccountId());
        SpringUtil.publishEvent(new FbAccountUpdateEvent(fbAccountDO));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("platformAdId", platformAdId);
        jsonObject.put("accountStatus", accountStatus);
        return jsonObject.toString();
    }
}
