/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BillingPaymentAccountModel {

    private String platformAdId;

    private List<BillingPaymentMethod> paymentMethods;

    public void addPaymentMethod(BillingPaymentMethod paymentMethod) {
        if (paymentMethods == null) {
            paymentMethods = new ArrayList<>();
        }
        paymentMethods.add(paymentMethod);
    }

    @Data
    public static class BillingPaymentMethod {

        private String bin;

        private String last4;

        private String expiryYear;

        private String expiryMonth;

        private String association;

        private boolean isPrimary;

        public BillingPaymentMethod(String bin,
                                    String last4,
                                    String expiryYear,
                                    String expiryMonth,
                                    String association,
                                    boolean isPrimary) {
            this.bin = bin;
            this.last4 = last4;
            this.expiryYear = expiryYear;
            this.expiryMonth = expiryMonth;
            this.association = association;
            this.isPrimary = isPrimary;
        }

        public String getFuzzyCardNumber() {
            return "%s****%s".formatted(bin, last4);
        }

        public String getExpireDate() {
            String month = expiryMonth.length() == 1 ? "0" + expiryMonth : expiryMonth;
            String year = expiryYear.substring(expiryYear.length() - 2);
            return year + month;
        }
    }
}
