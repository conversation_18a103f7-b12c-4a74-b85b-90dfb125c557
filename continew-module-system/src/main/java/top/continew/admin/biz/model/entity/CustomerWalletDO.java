package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 客户钱包实体
 *
 * <AUTHOR>
 * @since 2025/07/22 16:58
 */
@Data
@TableName("biz_customer_wallet")
public class CustomerWalletDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 钱包地址
     */
    private String walletAddress;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Long createUser;
    @TableField(
        fill = FieldFill.INSERT
    )
    private LocalDateTime createTime;
}