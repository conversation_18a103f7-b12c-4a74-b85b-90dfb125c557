package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.MaterialTypeEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 物料实体
 *
 * <AUTHOR>
 * @since 2025/01/21 16:20
 */
@Data
@TableName("biz_material")
public class MaterialDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 支付时间
     */
    private LocalDateTime payDate;

    /**
     * 类型
     */
    private MaterialTypeEnum type;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 渠道
     */
    private String channel;

    private Long channelId;

    /**
     * 支付金额
     */
    private BigDecimal payPrice;

    /**
     * 备注
     */
    private String remark;


    @TableField(fill = FieldFill.INSERT)
    private Long createUser;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}