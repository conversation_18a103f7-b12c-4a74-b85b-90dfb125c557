/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.CardBalanceTypeEnum;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡片余额流水实体
 *
 * <AUTHOR>
 * @since 2024/12/29 13:45
 */
@Data
@TableName("biz_card_balance")
public class CardBalanceDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡台平台
     */
    private CardPlatformEnum platform;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 类型（1=充值，2=提现，3=开卡手续费）
     */
    private CardBalanceTypeEnum type;

    /**
     * 原始类型
     */
    private String originType;

    /**
     * 变更金额
     */
    private BigDecimal amount;

    /**
     * 交易后余额
     */
    private BigDecimal afterAmount;

    /**
     * 交易时间
     */
    private LocalDateTime transTime;

    /**
     * 交易编号
     */
    private String transactionId;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private String platformCardId;
}