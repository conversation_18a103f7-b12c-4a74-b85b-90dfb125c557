package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 客户每日统计信息
 *
 * <AUTHOR>
 * @since 2025/07/17 11:39
 */
@Data
@Schema(description = "客户每日统计信息")
public class CustomerDailyStatByDateResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计时间
     */
    @Schema(description = "统计时间")
    private LocalDate statDate;

    /**
     * 总户数
     */
    @Schema(description = "总户数")
    private Integer totalAccount;

    /**
     * 当前户数
     */
    @Schema(description = "当前户数")
    private Integer totalFinishAccount;

    /**
     * 正常户数
     */
    @Schema(description = "正常户数")
    private Integer totalNormalAccount;

    /**
     * 使用户数
     */
    @Schema(description = "使用户数")
    private Integer todayUsedAccount;

    @Schema(description = "使用率")
    private String usedRate;

    /**
     * 封禁数量
     */
    @Schema(description = "封禁数量")
    private Integer todayBanAccount;

    /**
     * 下户数
     */
    @Schema(description = "下户数")
    private Integer todayOpenAccount;

    /**
     * 卡台消耗
     */
    @Schema(description = "卡台消耗")
    private BigDecimal todayCardSpent;
}