package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 标签关联实体
 *
 * <AUTHOR>
 * @since 2025/05/08 16:37
 */
@Data
@TableName("biz_tag_relation")
public class TagRelationDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 类型(1=广告户 2=下户订单)
     */
    private Integer type;

    /**
     * 关联ID
     */
    private Long relationId;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}