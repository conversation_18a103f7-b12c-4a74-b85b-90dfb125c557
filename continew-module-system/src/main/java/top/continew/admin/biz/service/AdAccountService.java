/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.AdAccountBalanceTypeEnum;
import top.continew.admin.biz.excel.ProducerRecordsExcelData;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.query.AdAccountQuery;
import top.continew.admin.biz.model.query.InactiveAccountAnalyzeQuery;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.*;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 广告账号业务接口
 *
 * <AUTHOR>
 * @since 2024/12/30 17:50
 */
public interface AdAccountService extends BaseService<AdAccountResp, AdAccountDetailResp, AdAccountQuery, AdAccountReq>, IService<AdAccountDO> {

    /**
     * 客户余额变更
     *
     * @param platformAdId
     * @param amount          金额为负数时代表支出
     * @param balanceType
     * @param transactionTime
     * @param remark
     */
    void changeAmount(String platformAdId,
                      BigDecimal amount,
                      AdAccountBalanceTypeEnum balanceType,
                      LocalDateTime transactionTime,
                      String remark);

    /**
     * 导入广告账号数据
     *
     * @param cachedDataList
     */
    void saveExcelData(List<ProducerRecordsExcelData> cachedDataList);

    AdAccountDO getAdAccountByFbAccountId(String fbAccountId);

    BasePageResp<AdAccountResp> customPage(AdAccountQuery query, PageQuery pageQuery);

    AdAccountDO getByPlatformAdId(String platformAdId);

    /**
     * 广告户新增银行卡
     *
     * @param req
     */
    void addCard(AdAccountAddCardReq req);

    /**
     * 查询广告户库存
     *
     * @return
     */
    List<AdAccountInventoryResp> getAdAccountInventory();

    /**
     * 更新浏览器编号
     *
     * @param platformAdId
     * @param browser
     */
    void updateBrowserNo(String platformAdId, String browser);

    /**
     * 导入数据
     *
     * @param file
     */
    void importAdAccount(MultipartFile file);

    /**
     * 获取浏览器编号
     *
     * @param query
     */
    Set<String> getCopyBrowserSet(AdAccountQuery query);

    /**
     * 充值卡台
     *
     * @param platformAdId
     * @param amount
     * @param inactiveCheck
     * @return
     */
    AdAccountCardOpsResultResp rechargeMasterCard(String platformAdId, BigDecimal amount, boolean inactiveCheck);

    /**
     * 提现主卡
     *
     * @param platformAdId
     * @param amount
     * @return
     */
    AdAccountCardOpsResultResp withdrawMasterCard(String platformAdId, BigDecimal amount);

    /**
     * 提现所有卡
     *
     * @param platformAdId
     * @return
     */
    List<AdAccountCardOpsResultResp> withdrawAllCards(String platformAdId);

    /**
     * 修改所有卡片状态
     *
     * @param platformAdId
     * @param isActive
     */
    void updateCardStatus(String platformAdId, boolean isActive);

    /**
     * 同步总消耗
     */
    void syncAllSpend(String envId, String serialNumber, String proxy, String headers, boolean isLite);

    /**
     * 浏览器编号丢失检测
     *
     * @param req
     * @return
     */
    Set<String> browserLostCheck(AdAccountBrowserCheckReq req);

    /**
     * 更新状态
     *
     * @param req
     */
    void updateAccountStatus(AdAccountUpdateAccountStatusReq req);

    /**
     * 更新养号状态
     *
     * @param req
     */
    void updateKeepStatus(AdAccountUpdateKeepStatusReq req);

    /**
     * 更新标签
     *
     * @param req
     */
    void updateTag(AdAccountUpdateTagReq req);

    /**
     * 批量备注
     *
     * @param req
     */
    void updateRemark(AdAccountUpdateRemarkReq req);

    void updateBm1Browser(AdAccountUpdateBm1Req req);

    void updateBm1Id(AdAccountUpdateBm1Req req);

    List<Map<String, Integer>> dailyData(LocalDate date);

    PageResp<AdAccountResp> pageInactiveAccounts(InactiveAccountAnalyzeQuery query);

    Long countInactiveAccounts(InactiveAccountAnalyzeQuery query);

    /**
     * 获取总库存
     *
     * @return
     */
    Integer getTotalInventory(Integer[] saleStatuses);

    /**
     * 获取养号状态统计
     *
     * @param query 查询条件
     * @return 统计结果
     */
    List<AdAccountKeepStatusStatResp> getKeepStatusStat(AdAccountQuery query);

    /**
     * 获取清零金额
     *
     * @param platformAdId
     * @return
     */
    BigDecimal getClearAmount(String platformAdId);

    /**
     * 更新BM5
     *
     * @param req
     */
    void updateBM5(AdAccountUpdateBMReq req);

    /**
     * 获取广告户库存
     *
     * @return
     */
    List<AdAccountInventoryResp> getInventory(boolean before22hourFilter);

    /**
     * 获取外部渠道广告户库存
     *
     * @return
     */
    List<AdAccountInventoryResp> getExternalAccountInventory();

    /**
     * 获取养号成功库存
     *
     * @return
     */
    List<AdAccountInventoryResp> getKeepSuccessInventory();

    /**
     * 计算备户成本
     */
    void calcCost();

    /**
     * 更新消耗
     *
     * @param req
     */
    void updateSpent(AdAccountSpentUpdateReq req);

    /**
     * 查询库存账号
     *
     * @return
     */
    List<AdAccountResp> selectInventoryList();

    List<AdAccountDO> selectWaitSaleAdAccountList(String timeZone,
                                                  Integer useCleanBm5,
                                                  Boolean isLowLimit,
                                                  Boolean requireVo,
                                                  LocalDateTime localDateTime,
                                                  Long bmType);

    void updateSaleStatus(AdAccountUpdateSaleStatusReq req);

    List<AdAccountSpentResp> amountSpentList(String adAccountId);

    /**
     * 获取养号中的广告户
     *
     * @param minSpent
     * @return
     */
    List<AdAccountDO> listWaitKeepingAccount(BigDecimal minSpent);

    /**
     * 检测养号中的号消耗
     */
    void checkAdAccountNurturingStatus(IdsReq req);

    void updateUnusableReason(AdAccountUpdateUnusableReasonReq req);

    /**
     * 开卡
     *
     * @param req
     * @return
     */
    CardDO openCard(CardOpenReq req);

    /**
     * 广告户恢复可用状态
     *
     * @param platformAdId
     */
    void recoverUsable(String platformAdId);

    /**
     * 批量获取广告户浏览器编号
     *
     * @param platformAdIds 广告户ID列表
     * @return 广告户ID和浏览器编号的映射
     */
    Map<String, String> getBrowserNoByPlatformAdIds(List<String> platformAdIds);

    /**
     * 充值剩余应付的钱
     *
     * @param platformAdId
     */
    void rechargeLastBalance(String platformAdId);

    /**
     * 冻结主卡
     *
     * @param platformAdId
     */
    void freezeMasterCard(String platformAdId);

    /**
     * 解冻主卡
     *
     * @param platformAdId
     */
    void unfreezeMasterCard(String platformAdId);
}