package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 客户商务对接查询条件
 *
 * <AUTHOR>
 * @since 2025/08/05 15:35
 */
@Data
@Schema(description = "客户商务对接查询条件")
public class CustomerBusinessUserQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID，用于关联客户信息表
     */
    @Schema(description = "客户ID，用于关联客户信息表")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 关联的商务用户ID
     */
    @Schema(description = "关联的商务用户ID")
    @Query(type = QueryType.EQ)
    private Long userId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.EQ)
    private LocalDateTime createTime;
}