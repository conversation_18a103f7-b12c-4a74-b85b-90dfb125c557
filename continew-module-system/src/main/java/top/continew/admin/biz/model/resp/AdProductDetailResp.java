package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 投放产品详情信息
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "投放产品详情信息")
public class AdProductDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    private Long customerId;

    @ExcelProperty(value = "客户")
    private String customerName;

    /**
     * 代理线
     */
    @Schema(description = "代理线")
    @ExcelProperty(value = "代理线")
    private String agentNo;

    @ExcelProperty(value = "投放国家")
    private String country;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    @ExcelProperty(value = "产品类型")
    private Integer type;
}