package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 客户问题详情信息
 *
 * <AUTHOR>
 * @since 2025/04/30 14:35
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "客户问题详情信息")
public class CustomerQuestionDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @ExcelProperty(value = "类型")
    private Integer type;

    /**
     * 问题数量
     */
    @Schema(description = "问题数量")
    @ExcelProperty(value = "问题数量")
    private Integer num;

    /**
     * 问题账号
     */
    @Schema(description = "问题账号")
    @ExcelProperty(value = "问题账号")
    private String accounts;

    private LocalDateTime orderTime;

    private String remark;
}