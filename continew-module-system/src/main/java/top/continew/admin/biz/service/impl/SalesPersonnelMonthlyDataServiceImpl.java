package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.admin.system.enums.JobRankEnum;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.SalesPersonnelMonthlyDataMapper;
import top.continew.admin.biz.model.entity.SalesPersonnelMonthlyDataDO;
import top.continew.admin.biz.model.query.SalesPersonnelMonthlyDataQuery;
import top.continew.admin.biz.model.req.SalesPersonnelMonthlyDataReq;
import top.continew.admin.biz.model.resp.SalesPersonnelMonthlyDataDetailResp;
import top.continew.admin.biz.model.resp.SalesPersonnelMonthlyDataResp;
import top.continew.admin.biz.service.SalesPersonnelMonthlyDataService;

import java.time.Month;
import java.time.YearMonth;
import java.util.List;

/**
 * 商务人员月度绩效数据业务实现
 *
 * <AUTHOR>
 * @since 2025/08/20 10:43
 */
@Service
@RequiredArgsConstructor
public class SalesPersonnelMonthlyDataServiceImpl extends BaseServiceImpl<SalesPersonnelMonthlyDataMapper, SalesPersonnelMonthlyDataDO, SalesPersonnelMonthlyDataResp, SalesPersonnelMonthlyDataDetailResp, SalesPersonnelMonthlyDataQuery, SalesPersonnelMonthlyDataReq> implements SalesPersonnelMonthlyDataService {

    @Override
    public List<SalesPersonnelMonthlyDataResp> list(SalesPersonnelMonthlyDataQuery query, SortQuery sortQuery) {
        return super.list(query, sortQuery);
    }

    @Override
    public List<SalesPersonnelMonthlyDataDO> listByUserIds(List<Long> userIds, JobRankEnum jobRank, YearMonth yearMonth) {

        // 试岗期不需要日期
        if (jobRank.equals(JobRankEnum.PROBATION)) {
            return lambdaQuery().in(SalesPersonnelMonthlyDataDO::getBusinessUserId, userIds)
                    .eq(SalesPersonnelMonthlyDataDO::getJobRank, jobRank)
                    .list();
        } else {
            return lambdaQuery().in(SalesPersonnelMonthlyDataDO::getBusinessUserId, userIds)
                    .eq(SalesPersonnelMonthlyDataDO::getJobRank, jobRank)
                    .eq(SalesPersonnelMonthlyDataDO::getSalesDate, yearMonth.atDay(1))
                    .list();
        }
    }
}