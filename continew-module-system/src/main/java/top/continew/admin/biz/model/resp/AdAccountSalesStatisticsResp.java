package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class AdAccountSalesStatisticsResp {
    @ExcelProperty("日期")
    private LocalDate date;
    @ExcelProperty("生产数量")
    private Integer createNum;
    @ExcelProperty("出售数量")
    private Integer saleNum;
    @ExcelProperty("回收数量")
    private Integer recycleNum;
    @ExcelProperty("死号数量")
    private Integer deadNum;
    @ExcelProperty("存量")
    private Integer inventory;

    public Integer getInventory() {
        return createNum - saleNum;
    }
}
