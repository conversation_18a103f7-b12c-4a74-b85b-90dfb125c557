/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.event.AdAccountUpdateEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.admin.system.model.entity.UserDO;

@Service
public class BillingAccountInformationUtilsUpdateAccountMutationStrategyImpl implements ReportOpsStrategy {
    @Override
    public ReportType getReport() {
        return ReportType.BILLING_ACCOUNT_INFORMATION_UTILS_UPDATE_ACCOUNT_MUTATION;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
//        JSONObject data = JSONObject.from(browserReq.getData());
//        JSONObject body = data.getJSONObject("body");
//        JSONObject input = body.getJSONObject("input");
//        String platformAdId = input.getString("billable_account_payment_legacy_account_id");
//        String timezoneName = input.getString("timezone");
//        String timezone = FacebookUtils.getTimezone(timezoneName);
//        String billCountry = "";
//        if (input.containsKey("tax")) {
//            JSONObject tax = input.getJSONObject("tax");
//            JSONObject businessAddress = tax.getJSONObject("business_address");
//            if (businessAddress != null) {
//                billCountry = businessAddress.getString("country_code");
//            }
//        }
//        JSONObject result = new JSONObject();
//        result.put("platformAdId", platformAdId);
//        result.put("timezoneName", timezoneName);
//        result.put("timezone", timezone);
//        result.put("currency", input.getString("currency"));
//        if (StringUtils.isNotBlank(timezone)) {
//            AdAccountDO accountDO = new AdAccountDO.builder().platformAdId(platformAdId).timezone(timezone).billCurrency(input.getString("currency")).billCountry(billCountry).build();
//            SpringUtil.publishEvent(new AdAccountUpdateEvent(accountDO));
//        }
//        return result.toString();
        return "";
    }
}
