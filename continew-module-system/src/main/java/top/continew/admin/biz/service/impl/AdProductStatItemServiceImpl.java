package top.continew.admin.biz.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.AdProductMapper;
import top.continew.admin.biz.mapper.AdProductStatItemMapper;
import top.continew.admin.biz.mapper.AdProductStatMapper;
import top.continew.admin.biz.mapper.CustomerMapper;
import top.continew.admin.biz.model.entity.AdProductDO;
import top.continew.admin.biz.model.entity.AdProductStatDO;
import top.continew.admin.biz.model.entity.AdProductStatItemDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.AdProductStatItemQuery;
import top.continew.admin.biz.model.req.AdProductStatItemReq;
import top.continew.admin.biz.model.resp.AdProductStatDetailResp;
import top.continew.admin.biz.model.resp.AdProductStatItemDetailResp;
import top.continew.admin.biz.model.resp.AdProductStatItemResp;
import top.continew.admin.biz.service.AdProductStatItemService;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 投放日报明细业务实现
 *
 * <AUTHOR>
 * @since 2025/08/13 21:02
 */
@Service
@RequiredArgsConstructor
public class AdProductStatItemServiceImpl extends BaseServiceImpl<AdProductStatItemMapper, AdProductStatItemDO, AdProductStatItemResp, AdProductStatItemDetailResp, AdProductStatItemQuery, AdProductStatItemReq> implements AdProductStatItemService {

    private final AdProductStatMapper adProductStatMapper;

    private final AdProductMapper adProductMapper;

    private final CustomerMapper customerMapper;

    @Override
    protected void afterAdd(AdProductStatItemReq req, AdProductStatItemDO entity) {
        calcStatData(entity.getStatId());
    }

    @Override
    protected void afterUpdate(AdProductStatItemReq req, AdProductStatItemDO entity) {
        calcStatData(entity.getStatId());
    }

    @Override
    public void delete(List<Long> ids) {
        this.beforeDelete(ids);
        Set<Long> statIdSet = new HashSet<>();
        for (Long id : ids) {
            AdProductStatItemDO item = this.getById(id);
            statIdSet.add(item.getStatId());
        }
        this.baseMapper.deleteByIds(ids);
        for (Long l : statIdSet) {
            calcStatData(l);
        }
        this.afterDelete(ids);
    }

    /**
     * 重新计算统计数据
     *
     * @param statId
     */
    public void calcStatData(Long statId) {
        List<AdProductStatItemDO> list = this.list(Wrappers.<AdProductStatItemDO>lambdaQuery().eq(AdProductStatItemDO::getStatId, statId));
        BigDecimal spend = list.stream().map(AdProductStatItemDO::getSpend).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal reflow = list.stream().map(AdProductStatItemDO::getReflowSpend).reduce(BigDecimal.ZERO, BigDecimal::add);
        AdProductStatDO stat = adProductStatMapper.selectById(statId);
        CustomerDO customer = customerMapper.selectById(stat.getCustomerId());
        JSONObject totalEffectData = new JSONObject();
        // 初始化成效字段
        if (JSON.isValidArray(customer.getEffectTemplate())) {
            JSONArray effectTemplate = JSONArray.parseArray(customer.getEffectTemplate());
            for (int i = 0; i < effectTemplate.size(); i++) {
                JSONObject effectTemplateItem = effectTemplate.getJSONObject(i);
                totalEffectData.put(effectTemplateItem.getString("key"), 0);
            }
        }
        for (AdProductStatItemDO adProductStatItemDO : list) {
            if (JSON.isValidArray(adProductStatItemDO.getEffectData())) {
                JSONArray effectData = JSONArray.parseArray(adProductStatItemDO.getEffectData());
                for (int i = 0; i < effectData.size(); i++) {
                    JSONObject effectDataItem = effectData.getJSONObject(i);
                    String key = effectDataItem.getString("key");
                    if (totalEffectData.containsKey(key)) {
                        totalEffectData.put(key, totalEffectData.getIntValue(key) + effectDataItem.getIntValue("value"));
                    }
                }
            }
        }
        JSONArray effectData = new JSONArray();
        for (String s : totalEffectData.keySet()) {
            JSONObject effectDataItem = new JSONObject();
            effectDataItem.put("key", s);
            effectDataItem.put("value", totalEffectData.getIntValue(s));
            effectData.add(effectDataItem);
        }
        AdProductStatDO update = new AdProductStatDO();
        update.setId(statId);
        update.setActualSpend(spend);
        update.setReflowSpend(reflow);
        update.setEffectData(effectData.toString());
        adProductStatMapper.updateById(update);
    }
}