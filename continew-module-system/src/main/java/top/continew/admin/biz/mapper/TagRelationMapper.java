package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.TagRelationDO;

import java.util.List;

/**
 * 标签关联 Mapper
 *
 * <AUTHOR>
 * @since 2025/05/08 16:37
 */
public interface TagRelationMapper extends BaseMapper<TagRelationDO> {


    /**
     * 查询出满足条件的订单ID
     *
     * @param tagIds
     * @return
     */
    List<Long> listOrderId(@Param("tags") List<Long> tagIds);

    /**
     * 查询出满足条件的广告户ID
     *
     * @param tagIds
     * @return
     */
    List<Long> listAdAccountId(@Param("tags") List<Long> tagIds);

    String tagNamesByOrderId(@Param("orderId") Long orderId);
}