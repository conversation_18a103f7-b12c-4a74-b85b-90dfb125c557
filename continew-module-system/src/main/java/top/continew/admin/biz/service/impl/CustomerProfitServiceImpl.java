/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.CustomerMapper;
import top.continew.admin.biz.model.query.CustomerProfitStatQuery;
import top.continew.admin.biz.model.resp.CustomerProfitStatResp;
import top.continew.admin.biz.service.CustomerProfitService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.List;

/**
 * 客户业务实现
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Service
@RequiredArgsConstructor
public class CustomerProfitServiceImpl implements CustomerProfitService {

    private final CustomerMapper customerMapper;

    @Override
    public PageResp<CustomerProfitStatResp> selectCustomerProfitStatPage(CustomerProfitStatQuery query,
                                                                         PageQuery pageQuery) {
        QueryWrapper<CustomerProfitStatResp> queryWrapper = this.buildQueryWrapper(query);
        this.sort(queryWrapper, pageQuery);
        IPage<CustomerProfitStatResp> page = customerMapper.selectCustomerProfitStatPage(new Page<>((long)pageQuery.getPage(), (long)pageQuery.getSize()), queryWrapper);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<CustomerProfitStatResp> selectCustomerProfitStatList(CustomerProfitStatQuery query,
                                                                     SortQuery sortQuery) {
        QueryWrapper<CustomerProfitStatResp> queryWrapper = this.buildQueryWrapper(query);
        this.sort(queryWrapper, sortQuery);
        return customerMapper.selectCustomerProfitStatList(queryWrapper);
    }

    private QueryWrapper<CustomerProfitStatResp> buildQueryWrapper(CustomerProfitStatQuery query) {
        QueryWrapper<CustomerProfitStatResp> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("c.type", 1).eq("c.is_self_account", false);
        queryWrapper.eq(query.getCustomerId() != null, "c.id", query.getCustomerId());
        return queryWrapper;
    }

    private void sort(QueryWrapper<CustomerProfitStatResp> queryWrapper, SortQuery sortQuery) {
        if (sortQuery != null && !sortQuery.getSort().isUnsorted()) {
            Sort sort = sortQuery.getSort();
            for (Sort.Order order : sort) {
                String property = order.getProperty();
                queryWrapper.orderBy(true, order.isAscending(), CharSequenceUtil.toUnderlineCase(property));
            }

        }
    }
}