package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 结算订单详情实体
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Data
@TableName("biz_settle_order_item")
public class SettleOrderItemDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联订单
     */
    private Long orderId;

    /**
     * 广告户ID
     */
    private String platformAdId;

    /**
     * 消耗
     */
    private BigDecimal spent;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}