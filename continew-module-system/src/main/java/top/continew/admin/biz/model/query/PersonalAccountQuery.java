package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.PersonalAccountStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAppealStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutTypeEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 个号查询条件
 *
 * <AUTHOR>
 * @since 2025/03/11 17:02
 */
@Data
@Schema(description = "个号查询条件")
public class PersonalAccountQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    @Query(type = QueryType.EQ)
    private Long channelId;

    /**
     * 是否接入
     */
    @Schema(description = "是否接入")
    @Query(type = QueryType.EQ)
    private Boolean isAccess;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    @Query(type = QueryType.IN)
    private String[] browserNo;

    /**
     * 账号状态
     */
    @Schema(description = "账号状态")
    @Query(type = QueryType.EQ)
    private PersonalAccountStatusEnum accountStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN, columns = "pa.create_time")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;

    @Schema(description = "购买时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate[] purchaseTime;
    /**
     * 类型
     */
    @Schema(description = "类型")
    @Query(type = QueryType.EQ, columns = "pa.type")
    private PersonalAccoutTypeEnum type;

    /**
     * 是否售后号
     */
    @Schema(description = "是否售后号")
    @Query(type = QueryType.EQ, columns = "pa.is_after_sale")
    private Boolean isAfterSale;

    /**
     * 售后状态
     */
    @Schema(description = "售后状态")
    @Query(type = QueryType.EQ, columns = "pa.after_sale_status")
    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    /**
     * 售后原因
     */
    @Schema(description = "售后原因")
    @Query(type = QueryType.LIKE, columns = "pa.after_sale_reason")
    private String afterSaleReason;

    /**
     * 申诉状态
     */
    @Schema(description = "申诉状态")
    @Query(type = QueryType.EQ)
    private PersonalAccoutAppealStatusEnum appealStatus;

    /**
     * 是否改密码
     */
    @Schema(description = "是否改密码")
    @Query(type = QueryType.EQ)
    private Boolean isChangePwd;

    @Query(type = QueryType.EQ, columns = "pa.platform_account_id")
    private String platformAccountId;

    @Query(type = QueryType.LIKE)
    private String accessUser;

    @QueryIgnore
    private String remark;

    @Query(type = QueryType.EQ)
    private String uniqueKey;
}