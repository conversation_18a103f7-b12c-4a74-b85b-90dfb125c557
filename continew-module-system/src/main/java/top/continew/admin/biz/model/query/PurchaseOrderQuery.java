package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.AdPlatformEnum;
import top.continew.admin.biz.enums.AdProjectEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 采购订单查询条件
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@Schema(description = "采购订单查询条件")
public class PurchaseOrderQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    @Query(type = QueryType.EQ, columns = "o.channel_id")
    private Long channelId;

    /**
     * 物料类型
     */
    @Schema(description = "物料类型")
    @Query(type = QueryType.EQ, columns = "o.type")
    private Long type;

    /**
     * 是否付款
     */
    @Schema(description = "是否付款")
    @Query(type = QueryType.EQ, columns = "o.is_pay")
    private Boolean isPay;

    /**
     * 状态（1=进行中，2=已完成）
     */
    @Schema(description = "状态（1=进行中，2=已完成）")
    @Query(type = QueryType.EQ, columns = "o.status")
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN, columns = "o.create_time")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;


    @Query(type = QueryType.BETWEEN, columns = "o.purchase_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate[] purchaseTime;

    @Query(type = QueryType.BETWEEN, columns = "o.receive_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate[] receiveDate;

    /**
     * 媒体平台
     */
    @Query(type = QueryType.EQ, columns = "o.ad_platform")
    private AdPlatformEnum adPlatform;

    /**
     * 项目
     */
    @Query(type = QueryType.EQ, columns = "o.project")
    private AdProjectEnum project;
}