package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 客户账号信息实体
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@TableName("biz_customer_account")
public class CustomerAccountDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账号类型：对应着社交账号类型
     */
    private Integer accountType;

    /**
     * 账号
     */
    private String account;

    /**
     * 关联的社交账号ID
     */
    private Long socialAccountId;

    /**
     * 备注
     */
    private String remark;
}