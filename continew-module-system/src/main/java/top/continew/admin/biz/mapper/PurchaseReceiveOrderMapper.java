package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.PurchaseOrderDO;
import top.continew.admin.biz.model.resp.PurchaseOrderResp;
import top.continew.admin.biz.model.resp.PurchaseReceiveOrderResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.PurchaseReceiveOrderDO;

import java.util.List;

/**
* 采购验收单 Mapper
*
* <AUTHOR>
* @since 2025/05/21 14:38
*/
public interface PurchaseReceiveOrderMapper extends BaseMapper<PurchaseReceiveOrderDO> {

    IPage<PurchaseReceiveOrderResp> selectCustomPage(@Param("page") IPage<PurchaseReceiveOrderDO> page,
                                                     @Param(Constants.WRAPPER) QueryWrapper<PurchaseReceiveOrderDO> queryWrapper);

    List<PurchaseReceiveOrderResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<PurchaseReceiveOrderDO> queryWrapper);


    List<PurchaseReceiveOrderResp> selectPurchaseTotal(@Param(Constants.WRAPPER) QueryWrapper<PurchaseReceiveOrderDO> queryWrapper);
}