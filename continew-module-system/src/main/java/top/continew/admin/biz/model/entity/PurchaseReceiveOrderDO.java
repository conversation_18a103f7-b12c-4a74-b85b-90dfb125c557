package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购验收单实体
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@TableName("biz_purchase_receive_order")
public class PurchaseReceiveOrderDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联采购单
     */
    private Long purchaseOrderId;

    /**
     * 验收数量
     */
    private Integer receiveNum;

    /**
     * 验收金额
     */
    private BigDecimal receivePrice;

    private LocalDateTime receiveTime;

    @TableField(fill = FieldFill.INSERT)
    private Long createUser;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}