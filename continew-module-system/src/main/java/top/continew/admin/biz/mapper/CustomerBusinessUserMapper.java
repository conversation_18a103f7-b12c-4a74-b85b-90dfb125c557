package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.resp.CustomerBusinessUserResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.CustomerBusinessUserDO;

import java.util.List;

/**
* 客户商务对接 Mapper
*
* <AUTHOR>
* @since 2025/08/05 15:35
*/
public interface CustomerBusinessUserMapper extends BaseMapper<CustomerBusinessUserDO> {

    List<CustomerBusinessUserResp> getByCustomerId(@Param("customerId") Long id);
}