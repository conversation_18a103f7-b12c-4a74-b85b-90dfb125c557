package top.continew.admin.biz.service.crm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import top.continew.admin.biz.model.entity.crm.CustomerAccountRelDO;
import top.continew.admin.biz.model.req.crm.CustomerAccountAddReq;
import top.continew.admin.biz.model.resp.crm.CustomerAccountResp;

import java.util.List;

/**
 * 客户社交账号服务接口
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
public interface CustomerAccountService {
    @Getter
    @AllArgsConstructor
    enum EntityType {
        LEAD(1),
        OPPORTUNITY(2),
        CUSTOMER(3);

        private final int value;
    }

    /**
     * 添加或编辑客户的社交账号
     *
     */
    void saveCustomerAccount(List<CustomerAccountAddReq> accounts, Integer entityType, Long entityId);

    /**
     * 删除客户的社交账号
     *
     */
    void deleteCustomerAccount(Integer entityType, Long entityId);

    /**
     * 获取某线索或商机的所有社交账号数据
     *
     * @return 社交账号列表
     */
    List<CustomerAccountResp> listCustomerAccounts(Integer entityType, Long entityId);

    /**
     * 客户关联关系
     * @param rel
     */
    void addCustomerAccountRel(CustomerAccountRelDO rel);


}