/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.ClearOrderCardStatusEnum;
import top.continew.admin.biz.enums.ClearOrderStatusEnum;
import top.continew.admin.biz.enums.FbLimitCheckStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 清零订单实体
 *
 * <AUTHOR>
 * @since 2024/12/31 11:17
 */
@Data
@TableName("biz_clear_order")
public class ClearOrderDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 关联广告户
     */
    private String platformAdId;

    /**
     * 清零金额
     */
    private BigDecimal clearAmount;

    /**
     * 卡片余额
     */
    private BigDecimal cardBalance;

    /**
     * 状态
     */
    private ClearOrderStatusEnum status;

    /**
     * fb限额检测状态
     */
    private FbLimitCheckStatusEnum fbCheckStatus;

    /**
     * 卡台状态
     */
    private ClearOrderCardStatusEnum cardStatus;

    /**
     * 关联消息ID
     */
    private Integer applyMessageId;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 凭证
     */
    private String certificate;

    /**
     * 卡台清零结果
     */
    private String cardClearResult;

    /**
     * fb操作ID
     */
    private String fbOpsId;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    @TableField(fill = FieldFill.UPDATE)
    private Long updateUser;
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    private Long adAccountOrderId;
}