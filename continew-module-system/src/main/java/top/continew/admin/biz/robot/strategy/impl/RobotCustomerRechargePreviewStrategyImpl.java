/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import cn.hutool.core.util.NumberUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.RechargeFeeHandleMethodEnum;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotCustomerRechargeReq;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.core.validation.CheckUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Service
@RequiredArgsConstructor
public class RobotCustomerRechargePreviewStrategyImpl implements RobotCommandStrategy {

    private final CustomerService customerService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.CUSTOMER_RECHARGE_PREVIEW;
    }

    @Override
    public String execute(Update update) {
        if (!BotUtils.isFinancialUser(update.getMessage().getFrom().getUserName())) {
            return "";
        }
        RobotCustomerRechargeReq req = BotUtils.parseBotMsgToObject(update.getMessage()
            .getText(), "\n", ":", RobotCustomerRechargeReq.class);
        CustomerDO customer = customerService.getByName(req.getCustomerName());
        CheckUtils.throwIfNull(customer, "未找到相关客户");
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("【客户打款预览】").append("\n");
        // 计算充值手续费
        BigDecimal fee = BigDecimal.ZERO;
        if (req.getFeeHandleMethod().equals(RechargeFeeHandleMethodEnum.REDUCE_FROM_RECHARGE_AMOUNT.getValue())) {
            fee = CommonUtils.divide100(req.getAmount().multiply(customer.getFeeRatePercent()), null);
        } else if (req.getFeeHandleMethod().equals(RechargeFeeHandleMethodEnum.RECHARGE_INCLUDE_FEE.getValue())) {
            BigDecimal onePart = req.getAmount()
                .divide(customer.getFeeRatePercent().add(BigDecimal.valueOf(100)), 4, RoundingMode.HALF_UP);
            fee = onePart.multiply(customer.getFeeRatePercent()).setScale(2, RoundingMode.HALF_UP);
        }
        stringBuilder.append("客户点位：").append(customer.getFeeRatePercent()).append("%").append("\n");
        stringBuilder.append("手续费：").append(NumberUtil.toStr(fee)).append("\n");
        stringBuilder.append("实际到账：").append(NumberUtil.toStr(req.getAmount().subtract(fee))).append("\n");
        return stringBuilder.toString();
    }
}
