package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.MaterialTypeEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 物料查询条件
 *
 * <AUTHOR>
 * @since 2025/01/21 16:20
 */
@Data
@Schema(description = "物料查询条件")
public class MaterialQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] payDate;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @Query(type = QueryType.EQ)
    private MaterialTypeEnum type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @Query(type = QueryType.EQ)
    private Integer num;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    @Query(type = QueryType.EQ)
    private String channel;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    @Query(type = QueryType.EQ)
    private BigDecimal payPrice;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Query(type = QueryType.EQ)
    private String remark;

}