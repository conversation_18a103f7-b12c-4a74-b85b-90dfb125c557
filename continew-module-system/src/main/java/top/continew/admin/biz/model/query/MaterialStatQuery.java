package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.MaterialTypeEnum;

import java.time.LocalDateTime;

@Data
public class MaterialStatQuery {

    private MaterialTypeEnum type;

    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] statTimes;
}

