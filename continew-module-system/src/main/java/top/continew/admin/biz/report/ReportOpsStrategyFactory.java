/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.report;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
public class ReportOpsStrategyFactory {

    private Map<ReportType, ReportOpsStrategy> strategies;

    @Autowired
    public ReportOpsStrategyFactory(Set<ReportOpsStrategy> strategySet) {
        createStrategy(strategySet);
    }

    public ReportOpsStrategy findStrategy(String name) {
        ReportType reportType = ReportType.formName(name);
        if (reportType == null) {
            return null;
        }
        return strategies.get(reportType);
    }

    public ReportOpsStrategy findStrategy(ReportType reportType) {
        return strategies.get(reportType);
    }

    private void createStrategy(Set<ReportOpsStrategy> strategySet) {
        strategies = new HashMap<>();
        strategySet.forEach(strategy -> strategies.put(strategy.getReport(), strategy));
    }

}
