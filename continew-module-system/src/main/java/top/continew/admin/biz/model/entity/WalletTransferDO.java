package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 钱包流水实体
 *
 * <AUTHOR>
 * @since 2025/07/22 15:59
 */
@Data
@TableName("biz_wallet_transfer")
public class WalletTransferDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易号
     */
    private String transactionId;

    /**
     * 是否确认
     */
    private Boolean confirmed;

    /**
     * 交易时间
     */
    private LocalDateTime transTime;

    /**
     * contractRet
     */
    private String contractRet;

    /**
     * 合约地址
     */
    private String contractAddress;

    /**
     * 合约类型
     */
    private String contractType;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * finalResult
     */
    private String finalResult;

    /**
     * 来源地址
     */
    private String fromAddress;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * to_address
     */
    private String toAddress;

    private String tokenSymbol;
}