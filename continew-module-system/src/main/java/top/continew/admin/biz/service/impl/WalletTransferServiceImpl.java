package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.WalletTransferMapper;
import top.continew.admin.biz.model.entity.CustomerDailyStatDO;
import top.continew.admin.biz.model.entity.RefundOrderDO;
import top.continew.admin.biz.model.entity.WalletTransferDO;
import top.continew.admin.biz.model.query.RefundOrderQuery;
import top.continew.admin.biz.model.query.WalletTransferQuery;
import top.continew.admin.biz.model.req.WalletTransferReq;
import top.continew.admin.biz.model.resp.CustomerDailyStatDetailResp;
import top.continew.admin.biz.model.resp.RefundOrderResp;
import top.continew.admin.biz.model.resp.WalletTransferDetailResp;
import top.continew.admin.biz.model.resp.WalletTransferResp;
import top.continew.admin.biz.service.WalletTransferService;
import top.continew.admin.biz.utils.TronscanApi;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 钱包流水业务实现
 *
 * <AUTHOR>
 * @since 2025/07/22 15:59
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WalletTransferServiceImpl extends BaseServiceImpl<WalletTransferMapper, WalletTransferDO, WalletTransferResp, WalletTransferDetailResp, WalletTransferQuery, WalletTransferReq> implements WalletTransferService {
    @Override
    public void syncData(Long startTimestamp, Long endTimestamp) {
        int start = 0;
        int limit = 50;
        int total = 20;
        List<WalletTransferDO> list = new ArrayList<>();
        String toAddress = "TBipLtwBVkfcP7ZGPp8k5oU3NkFEq2tNtJ";
        while (start < total) {
            log.info("【交易记录同步】开始同步第{}条数据...", start);
            JSONObject result = TronscanApi.getTransferList(toAddress, startTimestamp, endTimestamp, start, limit);
            log.info("【交易记录同步】同步成功：{}", result);
            total = result.getIntValue("total");
            JSONArray data = result.getJSONArray("token_transfers");
            for (int i = 0; i < data.size(); i++) {
                JSONObject item = data.getJSONObject(i);
                WalletTransferDO walletTransfer = new WalletTransferDO();
                walletTransfer.setTransactionId(item.getString("transaction_id"));
                walletTransfer.setConfirmed(item.getBoolean("confirmed"));
                walletTransfer.setTransTime(LocalDateTimeUtil.of(item.getLong("block_ts")));
                walletTransfer.setContractRet(item.getString("contractRet"));
                walletTransfer.setContractAddress(item.getString("contract_address"));
                walletTransfer.setContractType(item.getString("contract_type"));
                walletTransfer.setEventType(item.getString("event_type"));
                walletTransfer.setFinalResult(item.getString("finalResult"));
                walletTransfer.setFromAddress(item.getString("from_address"));
                BigDecimal decimal = item.getBigDecimal("quant");
                JSONObject tokenInfo = item.getJSONObject("tokenInfo");
                int tokenDecimal = tokenInfo.getIntValue("tokenDecimal");
                walletTransfer.setTokenSymbol(tokenInfo.getString("tokenAbbr"));
                BigDecimal amount = decimal.divide(BigDecimal.valueOf(Math.pow(10, tokenDecimal)), 6, RoundingMode.HALF_UP);
                if (walletTransfer.getFromAddress().equals(toAddress)) {
                    walletTransfer.setAmount(amount.negate());
                } else {
                    walletTransfer.setAmount(amount);
                }
                walletTransfer.setToAddress(item.getString("to_address"));
                boolean exist = list.stream()
                    .anyMatch(v -> v.getTransactionId().equals(walletTransfer.getTransactionId()));
                if (!exist) {
                    list.add(walletTransfer);
                }
            }
            start = start + limit;
            ThreadUtil.sleep(1000);
        }
        for (WalletTransferDO walletTransferDO : list) {
            if (!this.exists(Wrappers.<WalletTransferDO>lambdaQuery()
                .eq(WalletTransferDO::getTransactionId, walletTransferDO.getTransactionId()))) {
                this.saveOrUpdate(walletTransferDO);
            }
        }
    }

    @Override
    protected QueryWrapper<WalletTransferDO> buildQueryWrapper(WalletTransferQuery query) {
        QueryWrapper<WalletTransferDO> queryWrapper = super.buildQueryWrapper(query);
        queryWrapper.eq("token_symbol", "USDT");
        return queryWrapper;
    }

    @Override
    public PageResp<WalletTransferResp> page(WalletTransferQuery query, PageQuery pageQuery) {
        QueryWrapper<WalletTransferDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, pageQuery);
        IPage<WalletTransferResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        return PageResp.build(page);
    }

    @Override
    protected <E> List<E> list(WalletTransferQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<WalletTransferDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        List<WalletTransferDetailResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    public BigDecimal getCurrentBalance() {
        return this.baseMapper.getCurrentBalance();
    }
}