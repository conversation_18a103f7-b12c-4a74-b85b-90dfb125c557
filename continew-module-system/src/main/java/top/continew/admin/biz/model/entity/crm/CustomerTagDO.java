package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 客户标签实体
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@TableName("biz_customer_tag")
public class CustomerTagDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 标签类型编码，对应着字典类型编码
     */
    private String code;
}