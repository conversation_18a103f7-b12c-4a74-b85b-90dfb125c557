package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 商务日报实体
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
@Data
@TableName("biz_sales_daily_summary")
public class SalesDailySummaryDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日报记录的日期
     */
    private LocalDate recordDate;

    /**
     * 日报具体内容，支持长文本
     */
    private String content;


    private Long createUser;


    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}