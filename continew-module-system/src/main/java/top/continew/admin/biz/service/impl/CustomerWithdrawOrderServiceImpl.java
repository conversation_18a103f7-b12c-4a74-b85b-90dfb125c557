package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.enums.CustomerStatusEnum;
import top.continew.admin.biz.enums.CustomerWithdrawOrderStatusEnum;
import top.continew.admin.biz.event.CustomerBalanceChangeEvent;
import top.continew.admin.biz.event.CustomerBalanceChangeModel;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.resp.CustomerDetailResp;
import top.continew.admin.biz.service.CustomerBalanceRecordService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.HolidayUtils;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.CustomerWithdrawOrderMapper;
import top.continew.admin.biz.model.entity.CustomerWithdrawOrderDO;
import top.continew.admin.biz.model.query.CustomerWithdrawOrderQuery;
import top.continew.admin.biz.model.req.CustomerWithdrawOrderReq;
import top.continew.admin.biz.model.req.CustomerWithdrawOrderAuditReq;
import top.continew.admin.biz.model.req.CustomerWithdrawOrderRefundReq;
import top.continew.admin.biz.model.resp.CustomerWithdrawOrderDetailResp;
import top.continew.admin.biz.model.resp.CustomerWithdrawOrderResp;
import top.continew.admin.biz.service.CustomerWithdrawOrderService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户余额提现订单业务实现
 *
 * <AUTHOR>
 * @since 2025/01/22 14:23
 */
@Service
@RequiredArgsConstructor
public class CustomerWithdrawOrderServiceImpl extends BaseServiceImpl<CustomerWithdrawOrderMapper, CustomerWithdrawOrderDO, CustomerWithdrawOrderResp, CustomerWithdrawOrderDetailResp, CustomerWithdrawOrderQuery, CustomerWithdrawOrderReq> implements CustomerWithdrawOrderService {
    private final CustomerService customerService;
    private final HolidayUtils holidayUtils;
    private final CustomerBalanceRecordService customerBalanceRecordService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(CustomerWithdrawOrderReq req) {
        CustomerDetailResp customer = customerService.get(req.getCustomerId());
        if (customer == null) {
            throw new BusinessException("客户不存在");
        }

        Long existCount = baseMapper.selectCount(new LambdaQueryWrapper<CustomerWithdrawOrderDO>()
                .in(CustomerWithdrawOrderDO::getStatus, CustomerWithdrawOrderStatusEnum.WAIT.getValue(), CustomerWithdrawOrderStatusEnum.PASS.getValue()).eq(CustomerWithdrawOrderDO::getCustomerId, req.getCustomerId()));
        if (existCount > 0L) {
            throw new BusinessException("客户存在未退款订单");
        }

        CustomerWithdrawOrderDO entity = new CustomerWithdrawOrderDO();
        BeanUtil.copyProperties(req, entity);
        entity.setBusinessUserId(customer.getBusinessUserId());
        entity.setOrderNo(CommonUtils.randomOrderNo("TX"));
        entity.setCreateTime(LocalDateTime.now());
        // 预计退款时间 提交时间+7个工作日
        entity.setExpectedRefundTime(holidayUtils.calculateWorkingDayDate(entity.getCreateTime(), 7));
        entity.setStatus(CustomerWithdrawOrderStatusEnum.WAIT);
        baseMapper.insert(entity);
        return entity.getId();
    }

    @Override
    public PageResp<CustomerWithdrawOrderResp> page(CustomerWithdrawOrderQuery query, PageQuery pageQuery) {
        IPage<CustomerWithdrawOrderResp> page = baseMapper.selectCustomPage(new Page((long) pageQuery.getPage(), (long) pageQuery.getSize()), query);
        page.getRecords().forEach(this::fill);
        return PageResp.build(page, this.getListClass());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> ids) {
        for (Long id : ids) {
            CustomerWithdrawOrderDO entity = baseMapper.selectById(id);
            if (entity == null) {
                throw new BusinessException("订单不存在");
            }
            if (!CustomerWithdrawOrderStatusEnum.WAIT.equals(entity.getStatus())) {
                throw new BusinessException("订单状态不正确");
            }
        }

        super.delete(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(CustomerWithdrawOrderAuditReq req) {
        CustomerWithdrawOrderDO entity = baseMapper.selectById(req.getId());
        if (entity == null) {
            throw new BusinessException("订单不存在");
        }
        if (!CustomerWithdrawOrderStatusEnum.WAIT.equals(entity.getStatus())) {
            throw new BusinessException("订单状态不正确");
        }

        entity.setStatus(req.getStatus());
        entity.setAuditRemark(req.getRemark());

        entity.setAuditor(UserContextHolder.getUserId() == null ? 1 : UserContextHolder.getUserId());
        entity.setAuditTime(LocalDateTime.now());
        baseMapper.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refund(CustomerWithdrawOrderRefundReq req) {
        CustomerWithdrawOrderDO entity = baseMapper.selectById(req.getId());
        if (entity == null) {
            throw new BusinessException("订单不存在");
        }
        if (!CustomerWithdrawOrderStatusEnum.PASS.equals(entity.getStatus())) {
            throw new BusinessException("订单状态不正确");
        }


        CustomerDO customer = customerService.getById(entity.getCustomerId());
        if (customer == null) {
            throw new BusinessException("客户不存在");
        }

        BigDecimal balance = baseMapper.getBalance(customer.getId());

        if (req.getActualRefundAmount().compareTo(balance) > 0) {
            throw new BusinessException("退款金额不能大于客户余额，请刷新重试");
        }

        CustomerWithdrawOrderDO updateEntity = new CustomerWithdrawOrderDO();
        updateEntity.setId(entity.getId());
        updateEntity.setStatus(CustomerWithdrawOrderStatusEnum.FINISH);
        updateEntity.setActualRefundTime(req.getActualRefundTime());
        updateEntity.setActualRefundAmount(req.getActualRefundAmount());
        baseMapper.updateById(updateEntity);

        // 退款金额等于客户余额，将客户状态改为终止
        if (req.getActualRefundAmount().compareTo(balance) == 0) {
            customerService.batchUpdateStatus(List.of(entity.getCustomerId()), CustomerStatusEnum.TERMINATED.getValue(), req.getActualRefundTime());
        }

        if (req.getActualRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 清空客户余额，并增加一条扣款信息
            BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(customer.getId());
            customerService.changeAmount(entity.getCustomerId(), req.getActualRefundAmount().negate(), CustomerBalanceTypeEnum.DEDUCE, req.getActualRefundTime(), "财务审核客户退款订单");
            CustomerBalanceChangeModel changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, req.getActualRefundAmount().negate(), customer.getBalance(), req.getActualRefundAmount().negate());
            SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
        }

    }
}