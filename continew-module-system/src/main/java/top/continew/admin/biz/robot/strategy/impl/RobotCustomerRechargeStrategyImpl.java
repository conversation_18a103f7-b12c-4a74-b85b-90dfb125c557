/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.RechargeFeeHandleMethodEnum;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotCustomerRechargeReq;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.core.validation.CheckUtils;

@Service
@RequiredArgsConstructor
public class RobotCustomerRechargeStrategyImpl implements RobotCommandStrategy {

    private final CustomerService customerService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.CUSTOMER_RECHARGE_CONFIRM;
    }

    @Override
    public String execute(Update update) {
        if (!BotUtils.isFinancialUser(update.getMessage().getFrom().getUserName())) {
            return "";
        }
        if (!update.getMessage().isReply()) {
            return "";
        }
        String message = update.getMessage().getReplyToMessage().getText();
        RobotCustomerRechargeReq req = BotUtils.parseBotMsgToObject(message, "\n", ":", RobotCustomerRechargeReq.class);
        CustomerDO customer = customerService.getByName(req.getCustomerName());
        CheckUtils.throwIfNull(customer, "未找到相关客户");
        long createUser;
        switch (update.getMessage().getFrom().getUserName()) {
            case "Reallyee" -> createUser = 664896988164182082L;
            case "agony9445" -> createUser = 665914661983608847L;
            default -> createUser = 1L;
        }
        Long id = customerService.rechargeFromRobot(customer.getId(), req.getAmount(), RechargeFeeHandleMethodEnum.getByValue(req.getFeeHandleMethod()), createUser, req.getRemark());
        return "打款成功，订单编号： " + id;
    }

}
