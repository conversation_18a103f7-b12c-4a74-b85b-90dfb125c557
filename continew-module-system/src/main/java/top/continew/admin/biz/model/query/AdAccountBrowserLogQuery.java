/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 账号浏览器操作记录查询条件
 *
 * <AUTHOR>
 * @since 2024/12/31 14:27
 */
@Data
@Schema(description = "账号浏览器操作记录查询条件")
public class AdAccountBrowserLogQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @Query(type = QueryType.EQ)
    private String name;

    /**
     * 标签
     */
    @Schema(description = "标签")
    @Query(type = QueryType.EQ)
    private String label;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    @Query(type = QueryType.EQ)
    private Long opsTime;

    /**
     * 平台账号ID
     */
    @Schema(description = "平台账号ID")
    @Query(type = QueryType.EQ)
    private String platformAccountId;

    /**
     * 激活码
     */
    @Schema(description = "激活码")
    @Query(type = QueryType.EQ)
    private String activeCode;

    /**
     * 操作环境
     */
    @Schema(description = "操作环境")
    @Query(type = QueryType.EQ)
    private String env;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.EQ)
    private LocalDateTime createTime;
}