package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 白名单邮箱查询条件
 *
 * <AUTHOR>
 * @since 2025/07/01 14:36
 */
@Data
@Schema(description = "白名单邮箱查询条件")
public class WhiteEmailQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    @Query(type = QueryType.EQ)
    private String email;
}