/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.AppealOrderCardStatusEnum;
import top.continew.admin.biz.enums.AppealOrderStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 申诉订单实体
 *
 * <AUTHOR>
 * @since 2025/01/16 11:24
 */
@Data
@TableName("biz_appeal_order")
public class AppealOrderDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 客户
     */
    private Long customerId;

    /**
     * 广告户ID
     */
    private String platformAdId;

    /**
     * 申诉状态
     */
    private AppealOrderStatusEnum status;

    /**
     * 卡台状态
     */
    private AppealOrderCardStatusEnum cardStatus;

    /**
     * 关联消息id
     */
    private Integer applyMessageId;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 卡片余额
     */
    private BigDecimal cardBalance;

    /**
     * 卡台提现结果
     */
    private String cardClearResult;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}