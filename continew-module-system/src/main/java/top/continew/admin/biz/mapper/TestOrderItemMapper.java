package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.resp.AdAccountTestOrderItemResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.TestOrderItemDO;

import java.util.List;

/**
* 测试任务详情 Mapper
*
* <AUTHOR>
* @since 2025/05/13 11:43
*/
public interface TestOrderItemMapper extends BaseMapper<TestOrderItemDO> {

    List<AdAccountTestOrderItemResp> listByAdAccountId(@Param("adAccountId") String adAccountId);
}