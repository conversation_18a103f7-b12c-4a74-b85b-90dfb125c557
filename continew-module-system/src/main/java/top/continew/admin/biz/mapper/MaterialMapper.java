package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.MaterialDO;
import top.continew.admin.biz.model.entity.PurchaseOrderDO;
import top.continew.admin.biz.model.query.MaterialStatQuery;
import top.continew.admin.biz.model.resp.MaterialResp;
import top.continew.admin.biz.model.resp.MaterialStatByDateResp;
import top.continew.admin.biz.model.resp.MaterialStatByTypeResp;
import top.continew.admin.biz.model.resp.PurchaseOrderResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.starter.extension.crud.model.query.SortQuery;

import java.util.List;

/**
 * 物料 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/21 16:20
 */
public interface MaterialMapper extends BaseMapper<MaterialDO> {

    IPage<MaterialStatByDateResp> selectStatByDatePage(IPage<MaterialDO> page, @Param("query") MaterialStatQuery query);

    List<MaterialStatByDateResp> selectStatByDateList(@Param("query") MaterialStatQuery query);

    List<MaterialStatByTypeResp> selectStatByTypeList(@Param("query") MaterialStatQuery query, @Param("sort") String[] sort);

    IPage<MaterialResp> selectCustomPage(@Param("page") IPage<MaterialDO> page,
                                         @Param(Constants.WRAPPER) QueryWrapper<MaterialDO> queryWrapper);

    List<MaterialResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<MaterialDO> queryWrapper);
}