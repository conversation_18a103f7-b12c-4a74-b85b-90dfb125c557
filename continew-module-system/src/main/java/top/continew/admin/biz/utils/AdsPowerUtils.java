package top.continew.admin.biz.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import top.continew.katai.utils.Common;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class AdsPowerUtils {

    public static List<Ads> getAdsList() {
        String url = "http://127.0.0.1:50325/api/v1/user/list";
        int page = 1;
        boolean hasMore = true;
        List<Ads> list = new ArrayList<>();
        while (hasMore) {
            try {
                log.info("开始获取第{}页数据...", page);
                Map<String, Object> params = new HashMap<>();
                params.put("page", page);
                params.put("page_size", 100);
                String result = HttpUtil.get(url, params);
                JSONObject resultJson = JSONObject.parseObject(result);
                JSONObject data = resultJson.getJSONObject("data");
                JSONArray items = data.getJSONArray("list");
                log.info("第{}页数据获取完成：{}", page, result);
                if (items.isEmpty()) {
                    hasMore = false;
                } else {
                    for (int i = 0; i < items.size(); i++) {
                        JSONObject item = items.getJSONObject(i);
                        Ads ad = new Ads();
                        ad.setCreateTime(item.getLong("created_time"));
                        ad.setUser_id(item.getString("user_id"));
                        ad.setSerial_number(item.getString("serial_number"));
                        ad.setRemark(item.getString("remark"));
                        ad.setLast_open_time(item.getLong("last_open_time"));
                        if (item.getLong("last_open_time") != null) {
                            ad.setLastOpen(LocalDateTimeUtil.of(item.getLong("last_open_time") * 1000));
                        }
                        list.add(ad);
                    }
                }
                page++;
            } catch (Exception e) {
                log.info("第{}页数据获取失败：{}", page, e.getMessage());
            }
            Common.sleep(1000);
        }
        return list;
    }

    public static void deleteAds(List<String> ids) {
        int batch = ids.size() % 100 == 0 ? ids.size() / 100 : (ids.size() / 100 + 1);
        for (int i = 0; i < batch; i++) {
            String url = "http://127.0.0.1:50325/api/v1/user/delete";
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("user_ids", ids.subList(i * 100, Math.min((i + 1) * 100, ids.size())));
            String result = HttpRequest.post(url).body(jsonObject.toString()).execute().body();
            System.out.println(result);
            Common.sleep(1000);
        }
    }

    @Data
    public static class Ads {
        @ExcelProperty("user_id")
        private String user_id;
        private String serial_number;
        private String remark;
        @ExcelProperty("last_open_time")
        private Long last_open_time;
        @ExcelProperty("created_time")
        private Long createTime;
        @ExcelProperty("最后打开时间")
        private LocalDateTime lastOpen;
    }
}

