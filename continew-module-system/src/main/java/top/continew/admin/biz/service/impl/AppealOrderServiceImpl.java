/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.AppealOrderCancelEvent;
import top.continew.admin.biz.event.AppealOrderCreateEvent;
import top.continew.admin.biz.event.AppealOrderSuccessEvent;
import top.continew.admin.biz.mapper.AppealOrderMapper;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.AppealOrderDO;
import top.continew.admin.biz.model.entity.ClearOrderDO;
import top.continew.admin.biz.model.query.AppealOrderQuery;
import top.continew.admin.biz.model.req.AppealOrderFinishReq;
import top.continew.admin.biz.model.req.AppealOrderReq;
import top.continew.admin.biz.model.req.AppealOrderUpdateRemarkReq;
import top.continew.admin.biz.model.resp.AppealOrderDetailResp;
import top.continew.admin.biz.model.resp.AppealOrderResp;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.AppealOrderService;
import top.continew.admin.biz.service.ClearOrderService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 申诉订单业务实现
 *
 * <AUTHOR>
 * @since 2025/01/16 11:24
 */
@Service
@RequiredArgsConstructor
public class AppealOrderServiceImpl extends BaseServiceImpl<AppealOrderMapper, AppealOrderDO, AppealOrderResp, AppealOrderDetailResp, AppealOrderQuery, AppealOrderReq> implements AppealOrderService {

    private final CustomerService customerService;

    private final ClearOrderService clearOrderService;

    private final AdAccountService adAccountService;

    @Override
    protected void beforeAdd(AppealOrderReq req) {
        AdAccountDO adAccountDO = adAccountService.getByPlatformAdId(req.getPlatformAdId());
        CheckUtils.throwIfNull(adAccountDO, "广告户不存在");
        CheckUtils.throwIf(!adAccountDO.getClearStatus()
            .equals(AdAccountClearStatusEnum.WAIT), "广告户已清零，无法提交申诉");
        AppealOrderDO existOrder = this.getOne(Wrappers.<AppealOrderDO>lambdaQuery()
            .eq(AppealOrderDO::getPlatformAdId, req.getPlatformAdId())
            .in(AppealOrderDO::getStatus, List.of(AppealOrderStatusEnum.WAIT, AppealOrderStatusEnum.HANDLING)));
        CheckUtils.throwIfNotNull(existOrder, "请勿重新申诉");
    }

    @Override
    public Long add(AppealOrderReq req) {
        this.beforeAdd(req);
        AppealOrderDO order = new AppealOrderDO();
        order.setOrderNo(CommonUtils.randomOrderNo("SS"));
        order.setPlatformAdId(req.getPlatformAdId());
        order.setStatus(AppealOrderStatusEnum.WAIT);
        this.save(order);
        this.afterAdd(req, order);
        return order.getId();
    }

    @Override
    protected void afterAdd(AppealOrderReq req, AppealOrderDO entity) {
        AppealOrderCreateEvent createEvent = new AppealOrderCreateEvent(entity.getId());
        SpringUtil.publishEvent(createEvent);
    }

    @Override
    public PageResp<AppealOrderResp> page(AppealOrderQuery query, PageQuery pageQuery) {
        QueryWrapper<AppealOrderDO> queryWrapper = this.buildQueryWrapper(query);
        if (StrUtil.isNotBlank(query.getRemark())) {
            if ("空".equals(query.getRemark())) {
                queryWrapper.eq("o.remark", StrUtil.EMPTY);
            } else {
                queryWrapper.like("o.remark", query.getRemark());
            }
        }
        if (StrUtil.isNotBlank(query.getPlatformAdId())) {
            queryWrapper.in("o.platform_ad_id", query.getPlatformAdId().split(","));
        }
        super.sort(queryWrapper, pageQuery);
        IPage<AppealOrderResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        return PageResp.build(page);
    }

    @Override
    public List<AppealOrderResp> list(AppealOrderQuery query, SortQuery sortQuery) {
        QueryWrapper<AppealOrderDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, sortQuery);
        return baseMapper.selectCustomList(queryWrapper);
    }

    protected <E> List<E> list(AppealOrderQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<AppealOrderDO> queryWrapper = this.buildQueryWrapper(query);
        this.sort(queryWrapper, sortQuery);
        List<AppealOrderResp> entityList = baseMapper.selectCustomList(queryWrapper);
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    public synchronized void handleOrder(Long id) {
        AppealOrderDO order = this.getById(id);
        CheckUtils.throwIf(!order.getStatus().equals(AppealOrderStatusEnum.WAIT), "订单已被其他人处理");
        this.update(Wrappers.<AppealOrderDO>lambdaUpdate()
            .set(AppealOrderDO::getHandleUser, UserContextHolder.getUserId())
            .set(AppealOrderDO::getStatus, ClearOrderStatusEnum.HANDLING)
            .set(AppealOrderDO::getHandleTime, LocalDateTime.now())
            .eq(AppealOrderDO::getId, id));
        //        if (order.getApplyMessageId() != null && order.getCustomerId() != null) {
        //            CustomerDO customer = customerService.getById(order.getCustomerId());
        //            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
        //                .chatId(customer.getTelegramChatId())
        //                .replyToMessageId(order.getApplyMessageId())
        //                .text(order.getPlatformAdId() + " 正在申诉中")
        //                .build()));
        //        }
    }

    @Override
    public synchronized void finishOrder(Long id, AppealOrderFinishReq req) {
        AppealOrderDO order = this.getById(id);
        CheckUtils.throwIf(!order.getStatus().equals(AppealOrderStatusEnum.HANDLING), "订单未在处理中");
        this.update(Wrappers.<AppealOrderDO>lambdaUpdate()
            .set(AppealOrderDO::getStatus, req.getStatus())
            .set(StringUtils.isNotBlank(req.getRemark()), AppealOrderDO::getRemark, req.getRemark())
            .set(AppealOrderDO::getFinishTime, LocalDateTime.now())
            .eq(AppealOrderDO::getId, id));
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getAppealStatus, req.getStatus().equals(AppealOrderStatusEnum.SUCCESS)
                ? AdAccountAppealStatusEnum.SUCCESS
                : AdAccountAppealStatusEnum.FAIL)
            .eq(AdAccountDO::getPlatformAdId, order.getPlatformAdId()));
        if (req.getStatus().equals(AppealOrderStatusEnum.SUCCESS)) {
            SpringUtil.publishEvent(new AppealOrderSuccessEvent(id));
        }
        //        if (order.getApplyMessageId() != null && order.getCustomerId() != null) {
        //            CustomerDO customer = customerService.getById(order.getCustomerId());
        //            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
        //                    .chatId(customer.getTelegramChatId())
        //                    .text(order.getPlatformAdId() + " " + req.getStatus().getDescription())
        //                    .build()));
        //        }
    }

    @Override
    public synchronized void cancelOrder(Long id) {
        AppealOrderDO order = this.getById(id);
        CheckUtils.throwIf(order.getStatus().equals(AppealOrderStatusEnum.SUCCESS) || order.getStatus()
            .equals(AppealOrderStatusEnum.FAIL), "订单已完成，无法取消");
        this.update(Wrappers.<AppealOrderDO>lambdaUpdate()
            .set(AppealOrderDO::getStatus, AppealOrderStatusEnum.CANCEL)
            .eq(AppealOrderDO::getId, id));
        SpringUtil.publishEvent(new AppealOrderCancelEvent(id));
    }

    @Override
    public void applyClear(Long id) {
        AppealOrderDO order = this.getById(id);
        CheckUtils.throwIf(!order.getStatus().equals(AppealOrderStatusEnum.FAIL), "仅限申诉失败订单提交清零");
        AdAccountDO adAccountDO = adAccountService.getByPlatformAdId(order.getPlatformAdId());
        CheckUtils.throwIf(!adAccountDO.getClearStatus()
            .equals(AdAccountClearStatusEnum.WAIT), "广告户已清零，请勿重复提交");
        ClearOrderDO clearOrder = new ClearOrderDO();
        clearOrder.setOrderNo(CommonUtils.randomOrderNo("QL"));
        clearOrder.setCustomerId(order.getCustomerId());
        clearOrder.setPlatformAdId(order.getPlatformAdId());
        clearOrder.setCardBalance(order.getCardBalance());
        clearOrder.setCardStatus(ClearOrderCardStatusEnum.of(order.getCardStatus().getValue()));
        clearOrder.setApplyMessageId(order.getApplyMessageId());
        clearOrder.setCardClearResult(order.getCardClearResult());
        clearOrderService.save(clearOrder);
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getClearStatus, AdAccountClearStatusEnum.PROCESS)
            .eq(AdAccountDO::getPlatformAdId, order.getPlatformAdId()));
        this.update(Wrappers.<AppealOrderDO>lambdaUpdate()
            .set(AppealOrderDO::getStatus, AppealOrderStatusEnum.APPLY_CLEAR)
            .eq(AppealOrderDO::getId, id));
    }

    @Override
    public void updateRemark(AppealOrderUpdateRemarkReq req) {

        if (req.getRemark() == null) {
            req.setRemark(StrUtil.EMPTY);
        }

        this.update(Wrappers.<AppealOrderDO>lambdaUpdate()
            .set(AppealOrderDO::getRemark, req.getRemark())
            .in(AppealOrderDO::getId, req.getIds()));
    }
}