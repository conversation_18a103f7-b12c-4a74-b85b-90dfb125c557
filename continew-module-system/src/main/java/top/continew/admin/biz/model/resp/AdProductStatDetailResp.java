package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.AdProductTypeEnum;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;
import top.continew.starter.web.autoconfigure.mvc.BaseEnumConverter;

/**
 * 产品日报详情信息
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "产品日报详情信息")
public class AdProductStatDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户
     */
    @Schema(description = "客户")
    private Long customerId;

    @ExcelProperty(value = "客户")
    private String customerName;

    /**
     * 产品
     */
    @Schema(description = "产品")
    private Long productId;

    @ExcelProperty(value = "代理线")
    private String productName;

    @ExcelProperty(value = "产品", converter = ExcelBaseEnumConverter.class)
    private AdProductTypeEnum productType;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @ExcelProperty(value = "统计日期")
    private LocalDate statDate;

    /**
     * 实际花费
     */
    @Schema(description = "实际花费")
    @ExcelProperty(value = "实际花费")
    private BigDecimal actualSpend;

    /**
     * 花费
     */
    @Schema(description = "花费")
    @ExcelProperty(value = "花费")
    private BigDecimal spend;

    @ExcelProperty(value = "回流")
    private BigDecimal reflowSpend;

    @ExcelProperty(value = "费率")
    private BigDecimal feeRate;
    /**
     * 服务费
     */
    @Schema(description = "服务费")
    @ExcelProperty(value = "服务费")
    private BigDecimal fee;

    /**
     * 额外数据
     */
    private String extraData;

    private String effectData;

    private BigDecimal bearCost;
}