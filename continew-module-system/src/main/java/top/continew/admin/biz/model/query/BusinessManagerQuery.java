/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * BM5账号查询条件
 *
 * <AUTHOR>
 * @since 2024/12/30 17:48
 */
@Data
@Schema(description = "BM5账号查询条件")
public class BusinessManagerQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联渠道
     */
    @Schema(description = "关联渠道")
    @Query(type = QueryType.EQ)
    private Long channelId;

    /**
     * BM5 ID
     */
    @Schema(description = "BM5 ID")
    @Query(type = QueryType.IN, columns = {"platform_id"})
    private List<String> platformIds;

    @Query(type = QueryType.EQ)
    private BusinessManagerStatusEnum status;

    @Query(type = QueryType.EQ)
    private BusinessManagerBannedReasonEnum bannedReason;

    @Query(type = QueryType.EQ)
    private Boolean isUse;

    @Query(type = QueryType.BETWEEN, columns = "bm.create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] createTime;

    @Schema(description = "浏览器编号")
    @Query(type = QueryType.LIKE)
    private String browserNo;

    @Schema(description = "使用者")
    @Query(type = QueryType.EQ)
    private String user;

    @Schema(description = "备注")
    @QueryIgnore
    private String remark;

    @Schema(description = "创建人")
    @Query(type = QueryType.EQ)
    private Long createUser;

    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] useTime;

    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] banTime;

    @QueryIgnore
    private String opsBrowser;
    @QueryIgnore
    private String reserveBrowser;
    @QueryIgnore
    private String observeBrowser;

    @Query(type = QueryType.EQ)
    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    @Query(type = QueryType.LIKE)
    private String afterSaleReason;

    @Query(type = QueryType.EQ)
    private Long type;

    @Query(type = QueryType.EQ)
    private Boolean isExternal;

    @Query(type = QueryType.EQ)
    private Boolean isEnterpriseAuth;

    @Query(type = QueryType.EQ)
    private Boolean isRemoveAdmin;

    @QueryIgnore
    private String reserveBrowserBak;

    @Query(type = QueryType.EQ)
    private Boolean isBu;

    @QueryIgnore
    private Boolean drop;
}