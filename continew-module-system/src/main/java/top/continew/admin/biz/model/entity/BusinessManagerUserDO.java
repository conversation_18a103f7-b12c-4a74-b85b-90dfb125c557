package top.continew.admin.biz.model.entity;

import java.io.Serial;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * bm管理员实体
 *
 * <AUTHOR>
 * @since 2025/04/22 16:45
 */
@Data
@TableName("biz_business_manager_user")
public class BusinessManagerUserDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * BM ID
     */
    private String bmId;

    /**
     * USER ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String userEmail;

    /**
     * 用户权限
     */
    private String userRole;

    /**
     * 是否移除
     */
    private Boolean isRemove;
}