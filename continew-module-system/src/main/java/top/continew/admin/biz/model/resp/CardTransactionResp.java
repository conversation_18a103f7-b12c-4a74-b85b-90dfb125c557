/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.AdPlatformEnum;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardTransactionStatusEnum;
import top.continew.admin.biz.enums.CardTransactionTypeEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 卡片交易流水信息
 *
 * <AUTHOR>
 * @since 2024/12/31 21:57
 */
@Data
@Schema(description = "卡片交易流水信息")
public class CardTransactionResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 平台
     */
    @Schema(description = "平台")
    private CardPlatformEnum platform;

    /**
     * 卡号
     */
    @Schema(description = "卡号")
    private String cardNumber;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    private CardTransactionTypeEnum transType;

    /**
     * 交易状态
     */
    @Schema(description = "交易状态")
    private CardTransactionStatusEnum transStatus;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    private BigDecimal transAmount;

    /**
     * 清算金额
     */
    private BigDecimal clearAmount;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    private LocalDateTime transTime;

    private LocalDateTime chinaTime;

    private LocalDateTime adAccountTime;

    /**
     * 交易详情
     */
    @Schema(description = "交易详情")
    private String transDetail;

    /**
     * 交易编号
     */
    private String transactionId;

    private String remark;

    private String adAccountId;

    private Long customerId;

    private AdPlatformEnum adPlatform;
}