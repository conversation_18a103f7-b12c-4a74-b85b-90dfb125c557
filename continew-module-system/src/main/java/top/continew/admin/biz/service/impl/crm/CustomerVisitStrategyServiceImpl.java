package top.continew.admin.biz.service.impl.crm;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import top.continew.admin.biz.mapper.crm.CustomerVisitStrategyMapper;
import top.continew.admin.biz.model.resp.CustomerConditionResp;
import top.continew.admin.biz.model.entity.crm.CustomerVisitStrategyDO;
import top.continew.admin.biz.model.query.crm.CustomerVisitStrategyQuery;
import top.continew.admin.biz.model.req.crm.CustomerVisitStragegyBasicConditionReq;
import top.continew.admin.biz.model.req.crm.CustomerVisitStrategyReq;
import top.continew.admin.biz.model.resp.crm.CustomerExecuteStrategyResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitStrategyDetailResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitStrategyResp;
import top.continew.admin.biz.service.crm.CustomerVisitStrategyService;
import top.continew.admin.biz.service.crm.CustomerVisitTaskService;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.core.exception.BusinessException;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 客户回访策略业务实现
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerVisitStrategyServiceImpl extends BaseServiceImpl<CustomerVisitStrategyMapper, CustomerVisitStrategyDO, CustomerVisitStrategyResp, CustomerVisitStrategyDetailResp, CustomerVisitStrategyQuery, CustomerVisitStrategyReq> implements CustomerVisitStrategyService {
    private final Integer NORMAL_STATUS = 1;
    private final CustomerVisitTaskService customerVisitTaskService;


    /**
     * 修改主执行方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerExecuteStrategyResp executeStrategy(Long strategyId) {
        log.info("开始执行客户回访策略，策略ID: {}", strategyId);

        try {
            // 1. 获取策略详情
            CustomerVisitStrategyDO strategy = baseMapper.selectById(strategyId);
            if (strategy == null) {
                throw new BusinessException("策略不存在");
            }

            if (!NORMAL_STATUS.equals(strategy.getStrategyStatus())) {
                throw new BusinessException("策略未启用，无法执行");
            }

            // 2. 解析策略条件
            List<Map<String, Object>> conditions = parseStrategyConditions(strategy.getStrategyConditions());

            // 3. 根据条件查找符合的客户（带详细信息）
            List<CustomerConditionResp> matchedCustomersWithDetails = findMatchedCustomersWithDetails(conditions, strategy.getCustomerType());

            // 4. 为每个客户创建回访任务（传入详细信息）
            int taskCount = createVisitTasksWithDetails(matchedCustomersWithDetails, strategy);

            // 5. 更新策略的扫描信息
            updateStrategyLastScan(strategyId, taskCount);

            log.info("策略执行完成，策略ID: {}，创建任务数: {}", strategyId, taskCount);

            return new CustomerExecuteStrategyResp(taskCount, "策略执行成功");

        } catch (Exception e) {
            log.error("执行策略失败，策略ID: {}", strategyId, e);
            throw new BusinessException("执行策略失败: " + e.getMessage());
        }
    }

    /**
     * 解析策略条件
     */
    private List<Map<String, Object>> parseStrategyConditions(String strategyConditions) {
        try {
            // 使用FastJSON解析JSON字符串
            Map<String, Object> conditionsConfig = JSON.parseObject(strategyConditions);
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> conditions = (List<Map<String, Object>>) conditionsConfig.get("conditions");

            // 过滤出启用的条件
            List<Map<String, Object>> enabledConditions = new ArrayList<>();
            for (Map<String, Object> condition : conditions) {
                Boolean enabled = (Boolean) condition.get("enabled");
                if (Boolean.TRUE.equals(enabled)) {
                    enabledConditions.add(condition);
                }
            }

            return enabledConditions;
        } catch (Exception e) {
            log.error("解析策略条件失败: {}", strategyConditions, e);
            throw new BusinessException("策略条件格式错误");
        }
    }

    /**
     * 根据条件查找匹配的客户 - 优化版本（返回详细信息）
     */
    private List<CustomerConditionResp> findMatchedCustomersWithDetails(List<Map<String, Object>> conditions, Integer customerType) {
        if (conditions.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用枚举分离基础条件和高级条件
        List<Map<String, Object>> basicConditions = new ArrayList<>();
        List<Map<String, Object>> advancedConditions = new ArrayList<>();

        for (Map<String, Object> condition : conditions) {
            String type = (String) condition.get("type");
            CustomerVisitStrategyConditionType conditionType = CustomerVisitStrategyConditionType.getByFrontendCode(type);

            if (conditionType != null) {
                if (conditionType.isBasicCondition()) {
                    basicConditions.add(condition);
                } else {
                    advancedConditions.add(condition);
                }
            } else {
                log.warn("未知的条件类型: {}", type);
            }
        }

        // 情况1：只有基础条件，返回基础客户信息
        if (!basicConditions.isEmpty() && advancedConditions.isEmpty()) {
            log.info("只有基础条件，执行单条SQL查询，条件数量: {}", basicConditions.size());
            List<Long> customerIds = executeBasicConditionsQuery(basicConditions, customerType);
            return customerIds.stream()
                    .map(id -> new CustomerConditionResp(id, "basicConditions", null, "基础条件匹配"))
                    .collect(Collectors.toList());
        }

        // 情况2：存在高级条件，逐步过滤并记录详细信息
        if (!advancedConditions.isEmpty()) {
            log.info("存在高级条件，基础条件将合并到第一个高级条件中执行");

            List<CustomerConditionResp> currentResults = null;

            // 第一个高级条件：合并基础条件一起执行
            Map<String, Object> firstAdvancedCondition = advancedConditions.get(0);
            String type = (String) firstAdvancedCondition.get("type");
            @SuppressWarnings("unchecked")
            Map<String, Object> config = (Map<String, Object>) firstAdvancedCondition.get("config");

            currentResults = executeConditionQueryWithDetailsAndBasicConditions(type, config, customerType, basicConditions, null);

            if (currentResults.isEmpty()) {
                log.info("第一个高级条件 {} 查询结果为空，停止执行后续条件", type);
                return new ArrayList<>();
            }

            log.info("第一个高级条件 {} 执行完成，匹配客户数: {}", type, currentResults.size());

            // 执行剩余的高级条件（多条件组合逻辑）
            if (advancedConditions.size() > 1) {
                log.info("开始执行剩余 {} 个高级条件的组合逻辑", advancedConditions.size() - 1);

                // 从第二个高级条件开始，逐步过滤
                for (int i = 1; i < advancedConditions.size(); i++) {
                    Map<String, Object> condition = advancedConditions.get(i);
                    String conditionType = (String) condition.get("type");
                    @SuppressWarnings("unchecked")
                    Map<String, Object> conditionConfig = (Map<String, Object>) condition.get("config");

                    // 提取当前结果中的客户ID列表作为过滤条件
                    List<Long> filterCustomerIds = currentResults.stream()
                            .map(CustomerConditionResp::getCustomerId)
                            .collect(Collectors.toList());

                    if (filterCustomerIds.isEmpty()) {
                        log.info("第 {} 个条件执行前客户列表为空，停止后续条件执行", i + 1);
                        return new ArrayList<>();
                    }

                    log.info("执行第 {} 个高级条件: {}，当前客户数: {}", i + 1, conditionType, filterCustomerIds.size());

                    // 执行当前条件查询（不包含基础条件，因为已经在第一个条件中处理过）
                    List<CustomerConditionResp> conditionResults = executeConditionQueryWithDetailsAndBasicConditions(
                            conditionType, conditionConfig, customerType, new ArrayList<>(), filterCustomerIds);

                    if (conditionResults.isEmpty()) {
                        log.info("第 {} 个高级条件 {} 查询结果为空，停止执行后续条件", i + 1, conditionType);
                        return new ArrayList<>();
                    }

                    // 更新当前结果为交集
                    currentResults = mergeConditionResults(currentResults, conditionResults);

                    log.info("第 {} 个高级条件 {} 执行完成，剩余匹配客户数: {}", i + 1, conditionType, currentResults.size());

                    // 如果没有匹配的客户，提前结束
                    if (currentResults.isEmpty()) {
                        log.info("多条件组合后无匹配客户，停止执行");
                        return new ArrayList<>();
                    }
                }

                log.info("所有高级条件执行完成，最终匹配客户数: {}", currentResults.size());
            }

            return currentResults;
        }

        return new ArrayList<>();
    }

    /**
     * 执行条件查询（支持基础条件合并，返回详细信息）
     */
    private List<CustomerConditionResp> executeConditionQueryWithDetailsAndBasicConditions(
            String type, Map<String, Object> config,
            Integer customerType,
            List<Map<String, Object>> basicConditions,
            List<Long> filterCustomerIds) {

        // 构建基础条件DTO
        CustomerVisitStragegyBasicConditionReq basicCondition = buildBasicConditionDTO(basicConditions, customerType);

        // 使用枚举处理高级条件类型
        CustomerVisitStrategyConditionType conditionType = CustomerVisitStrategyConditionType.getByFrontendCode(type);

        if (conditionType == null) {
            log.warn("未知的条件类型: {}", type);
            return new ArrayList<>();
        }

        // 根据高级条件类型调用相应的详细查询方法
        switch (conditionType) {
            case AD_ACCOUNT_COUNT:
                Integer minCount = (Integer) config.get("min");
                Integer maxCount = (Integer) config.get("max");
                return baseMapper.findCustomersByAdAccountCountWithBasicConditions(
                        minCount, maxCount, basicCondition, filterCustomerIds);

            case NORMAL_ACCOUNT_COUNT:
                minCount = (Integer) config.get("min");
                maxCount = (Integer) config.get("max");
                return baseMapper.findCustomersByNormalAccountCountWithBasicConditions(
                        minCount, maxCount, basicCondition, filterCustomerIds);

            case EMPTY_ACCOUNT_COUNT:
                minCount = (Integer) config.get("min");
                maxCount = (Integer) config.get("max");
                return baseMapper.findCustomerIdsByEmptyAccountCountWithBasicConditions(
                        minCount, maxCount, basicCondition, filterCustomerIds);

            case CONSUMPTION_AMOUNT:
                Double minAmount = MapUtil.getDouble(config, "min");
                Double maxAmount = MapUtil.getDouble(config, "max");
                String consumptionStartDate = (String) config.get("startDate");
                String consumptionEndDate = (String) config.get("endDate");
                return baseMapper.findCustomersByConsumptionAmountWithBasicConditions(
                        minAmount, maxAmount, consumptionStartDate, consumptionEndDate,
                        basicCondition, filterCustomerIds);

            case CONSUMPTION_TREND:
                //按天对比，按周对比，按月对比
                //值是day、week、month
                String periodType = (String) config.get("periodType");

                //阈值
                //值是百分比，值是正数或负数
                Double threshold = MapUtil.getDouble(config, "changeThreshold");

                return analyzeConsumptionTrend(periodType, threshold, basicCondition, filterCustomerIds);

            case SINGLE_ACCOUNT_CONSUMPTION_TREND:
                //按天对比，按周对比，按月对比
                //值是day、week、month
                //String periodType = (String) config.get("periodType");

                //阈值
                //值是百分比，值是正数或负数
                //Double threshold = (Double) config.get("threshold");

                //如果是按天对比，则获取昨天的消耗额和产生消耗的广告户数，前天的消耗额和产生消耗的广告户数，计算出单户消耗成本，进行对比
                //如果是按周对比，则获取上一周的消耗额和客户的所有广告户数，上上一周的消耗额和下户数，计算出单户消耗成本，进行对比
                //如果是按月对比，则获取上一月的消耗额和下户数，上上一月的消耗额和下户数，计算出单户消耗成本，进行对比
                //如果超过了阈值，则要为这个客户生成回访记录
                return null;

            default:
                log.warn("未实现的高级条件类型: {}", conditionType);
                return new ArrayList<>();
        }
    }


    /**
     * 创建回访任务（带条件详情和去重逻辑）
     */
    private int createVisitTasksWithDetails(List<CustomerConditionResp> customerResults, CustomerVisitStrategyDO strategy) {
        if (customerResults.isEmpty()) {
            log.info("没有匹配的客户，跳过创建回访任务");
            return 0;
        }

        log.info("开始创建回访任务，匹配客户数: {}", customerResults.size());

        // 调用批量创建方法
        int createdCount = customerVisitTaskService.batchCreateTasksWithTriggerInfo(
                customerResults,
                strategy.getId(),
                strategy.getStrategyName(),
                DateUtil.date().offset(DateField.DAY_OF_MONTH, 15).toLocalDateTime()
        );

        log.info("回访任务创建完成，实际创建任务数: {}", createdCount);
        return createdCount;
    }

    /**
     * 执行基础条件查询 - 使用baseMapper
     */
    private List<Long> executeBasicConditionsQuery(List<Map<String, Object>> basicConditions, Integer customerType) {
        if (basicConditions.isEmpty()) {
            return new ArrayList<>();
        }

        // 构建基础条件DTO
        CustomerVisitStragegyBasicConditionReq basicCondition = buildBasicConditionDTO(basicConditions, customerType);

        return baseMapper.findCustomerIdsByBasicConditions(basicCondition);
    }

    /**
     * 构建基础条件DTO
     */
    private CustomerVisitStragegyBasicConditionReq buildBasicConditionDTO(List<Map<String, Object>> basicConditions, Integer customerType) {
        CustomerVisitStragegyBasicConditionReq dto = new CustomerVisitStragegyBasicConditionReq();
        dto.setCustomerType(customerType);

        // 使用枚举处理基础条件
        for (Map<String, Object> condition : basicConditions) {
            String type = (String) condition.get("type");
            CustomerVisitStrategyConditionType conditionType = CustomerVisitStrategyConditionType.getByFrontendCode(type);
            @SuppressWarnings("unchecked")
            Map<String, Object> config = (Map<String, Object>) condition.get("config");

            if (conditionType != null) {
                switch (conditionType) {
                    case CREATE_TIME_RANGE:
                        dto.setStartDate((String) config.get("startDate"));
                        dto.setEndDate((String) config.get("endDate"));
                        break;
                    case SALES_TAGS:
                        @SuppressWarnings("unchecked")
                        List<String> selectedValues = (List<String>) config.get("selectedValues");
                        dto.setSalesTagIds(selectedValues);
                        break;
                    case REFUND_CUSTOMER:
                        Boolean enabled = (Boolean) config.get("enabled");
                        dto.setIsRefund(Boolean.TRUE.equals(enabled) ? true : null);
                        break;
                    default:
                        log.warn("未处理的基础条件类型: {}", conditionType);
                        break;
                }
            }
        }

        return dto;
    }


    /**
     * 更新策略的最后扫描信息
     */
    private void updateStrategyLastScan(Long strategyId, int matchCount) {
        CustomerVisitStrategyDO updateEntity = new CustomerVisitStrategyDO();
        updateEntity.setId(strategyId);
        updateEntity.setLastScanTime(LocalDateTime.now());
        updateEntity.setLastScanMatchCount(matchCount);

        baseMapper.updateById(updateEntity);
    }

    /**
     * 合并多个条件的查询结果
     * 保留同时满足多个条件的客户，并合并条件描述
     */
    private List<CustomerConditionResp> mergeConditionResults(
            List<CustomerConditionResp> previousResults,
            List<CustomerConditionResp> currentResults) {

        // 将当前结果转换为Map，便于快速查找
        Map<Long, CustomerConditionResp> currentResultMap = currentResults.stream()
                .collect(Collectors.toMap(
                        CustomerConditionResp::getCustomerId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        // 找出同时满足两个条件的客户
        List<CustomerConditionResp> mergedResults = new ArrayList<>();

        for (CustomerConditionResp previousResult : previousResults) {
            Long customerId = previousResult.getCustomerId();
            CustomerConditionResp currentResult = currentResultMap.get(customerId);

            if (currentResult != null) {
                // 合并条件描述
                String mergedDescription = previousResult.getConditionDescription() +
                        " & " + currentResult.getConditionDescription();

                // 创建合并后的结果
                CustomerConditionResp mergedResult = new CustomerConditionResp(
                        customerId,
                        "multipleConditions", // 多条件组合类型
                        null, // 条件值设为null，因为是多条件组合
                        mergedDescription
                );

                mergedResults.add(mergedResult);
            }
        }

        return mergedResults;
    }



    /**
     * 分析消耗趋势
     */
    private List<CustomerConditionResp> analyzeConsumptionTrend(String periodType, Double threshold,
                                                                CustomerVisitStragegyBasicConditionReq basicCondition,
                                                                List<Long> filterCustomerIds) {
        LocalDate today = LocalDate.now();
        String[] periods = calculatePeriods(periodType, today);
        String currentPeriodStart = periods[0];
        String currentPeriodEnd = periods[1];
        String previousPeriodStart = periods[2];
        String previousPeriodEnd = periods[3];

        // 获取当前周期的消耗数据
        List<CustomerConditionResp> currentPeriodData = baseMapper.findCustomersConsumptionByPeriod(
                currentPeriodStart, currentPeriodEnd, basicCondition, filterCustomerIds);

        // 获取上一周期的消耗数据
        List<CustomerConditionResp> previousPeriodData = baseMapper.findCustomersConsumptionByPeriod(
                previousPeriodStart, previousPeriodEnd, basicCondition, filterCustomerIds);

        // 转换为Map便于查找
        Map<Long, Double> currentConsumptionMap = currentPeriodData.stream()
                .collect(Collectors.toMap(
                        CustomerConditionResp::getCustomerId,
                        resp -> Double.parseDouble((String) resp.getConditionValue()),
                        (existing, replacement) -> existing
                ));

        Map<Long, Double> previousConsumptionMap = previousPeriodData.stream()
                .collect(Collectors.toMap(
                        CustomerConditionResp::getCustomerId,
                        resp -> Double.parseDouble((String) resp.getConditionValue()),
                        (existing, replacement) -> existing
                ));

        List<CustomerConditionResp> result = new ArrayList<>();

        // 分析趋势变化
        for (CustomerConditionResp currentResp : currentPeriodData) {
            Long customerId = currentResp.getCustomerId();
            Double currentConsumption = currentConsumptionMap.get(customerId);
            Double previousConsumption = previousConsumptionMap.get(customerId);

            if (previousConsumption != null && previousConsumption > 0) {
                // 计算变化百分比
                double changePercent = ((currentConsumption - previousConsumption) / previousConsumption) * 100;

                // 判断是否符合阈值条件
                boolean meetsCriteria = false;
                if (threshold > 0) {
                    // 正阈值：增长超过阈值
                    meetsCriteria = changePercent >= threshold;
                } else {
                    // 负阈值：下降超过阈值
                    meetsCriteria = changePercent <= threshold;
                }

                if (meetsCriteria) {
                    CustomerConditionResp trendResp = new CustomerConditionResp();
                    trendResp.setCustomerId(customerId);
                    trendResp.setConditionType("consumptionTrend");
                    trendResp.setConditionValue(String.format("%.2f", changePercent));
                    trendResp.setConditionDescription(String.format(
                            "消耗趋势变化: %.2f%% (上期: %.2f, 上上期: %.2f)",
                            changePercent, currentConsumption, previousConsumption));
                    result.add(trendResp);
                }
            }
        }

        log.info("消耗趋势分析完成，周期类型: {}, 阈值: {}%, 符合条件客户数: {}",
                periodType, threshold, result.size());

        return result;
    }

    /**
     * 计算时间周期
     * @param periodType 周期类型：day、week、month
     * @param today 今天日期
     * @return [当前周期开始, 当前周期结束, 上一周期开始, 上一周期结束]
     */
    private String[] calculatePeriods(String periodType, LocalDate today) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        switch (periodType.toLowerCase()) {
            case "day":
                // 昨天 vs 前天
                LocalDate yesterday = today.minusDays(1);
                LocalDate dayBeforeYesterday = today.minusDays(2);
                return new String[]{
                        yesterday.format(formatter),
                        today.format(formatter),
                        dayBeforeYesterday.format(formatter),
                        yesterday.format(formatter)
                };

            case "week":
                // 上周 vs 上上周
                LocalDate lastWeekStart = today.minusWeeks(1).with(DayOfWeek.MONDAY);
                LocalDate lastWeekEnd = lastWeekStart.plusDays(7);
                LocalDate weekBeforeLastStart = today.minusWeeks(2).with(DayOfWeek.MONDAY);
                LocalDate weekBeforeLastEnd = weekBeforeLastStart.plusDays(7);
                return new String[]{
                        lastWeekStart.format(formatter),
                        lastWeekEnd.format(formatter),
                        weekBeforeLastStart.format(formatter),
                        weekBeforeLastEnd.format(formatter)
                };

            case "month":
                // 上月 vs 上上月
                LocalDate lastMonthStart = today.minusMonths(1).withDayOfMonth(1);
                LocalDate lastMonthEnd = today.withDayOfMonth(1);
                LocalDate monthBeforeLastStart = today.minusMonths(2).withDayOfMonth(1);
                LocalDate monthBeforeLastEnd = lastMonthStart;
                return new String[]{
                        lastMonthStart.format(formatter),
                        lastMonthEnd.format(formatter),
                        monthBeforeLastStart.format(formatter),
                        monthBeforeLastEnd.format(formatter)
                };

            default:
                throw new IllegalArgumentException("不支持的周期类型: " + periodType);
        }
    }
}



