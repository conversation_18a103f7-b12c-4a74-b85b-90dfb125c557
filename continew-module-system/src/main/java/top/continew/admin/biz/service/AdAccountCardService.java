/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AdAccountCardDO;
import top.continew.admin.biz.model.req.AdAccountOpenCardReq;
import top.continew.admin.biz.model.resp.AdAccountCardResp;
import top.continew.starter.data.mp.service.IService;

import java.util.List;

/**
 * 广告户关联卡业务接口
 *
 * <AUTHOR>
 * @since 2025/01/02 11:24
 */
public interface AdAccountCardService extends IService<AdAccountCardDO> {
    /**
     * 设置默认卡
     *
     * @param platformAdId
     * @param cardId
     */
    void setDefault(String platformAdId, Long cardId);

    /**
     * 同步卡号
     */
    void syncCardNo();

    /**
     * 查询广告户关联卡片
     *
     * @param platformAdId
     * @return
     */
    List<AdAccountCardDO> listValidByPlatformAdId(String platformAdId);

    /**
     * 获取广告户主卡
     *
     * @param platformAdId
     * @return
     */
    AdAccountCardDO getDefaultCard(String platformAdId);

    /**
     * 查询广告户卡片列表
     *
     * @param platformAdId
     * @return
     */
    List<AdAccountCardDO> listByPlatformAdId(String platformAdId);

    /**
     * 开卡
     *
     * @param req
     */
    void openCard(AdAccountOpenCardReq req);

    List<AdAccountCardResp> listCardByPlatformAdId(String platformAdId);

}