package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 成本分析信息
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
@Data
@Schema(description = "成本分析信息")
public class BusinessManagerStatisticsSummaryResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 接入坑位
     */
    private Integer receiveBMItem;

    /**
     * 接入坑位成本
     */
    private BigDecimal receiveCost;

    /**
     * 已使用正常
     */
    private Integer usedNormalBmItem;

    /**
     * 存活率（已使用正常 / 接入坑位）
     */
    private BigDecimal normalRate;

    /**
     * 采购成本
     */
    private BigDecimal purchaseCost;

    /**
     * 平均每日采购成本
     */
    private BigDecimal avgPurchaseCostForDay;

    /**
     * 接入坑位成本 / 已使用正常
     */
    private BigDecimal avgPrepareCost;
}