package top.continew.admin.biz.model.entity.crm;

import java.io.Serial;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 客户账号信息关联实体
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@TableName("biz_customer_account_rel")
public class CustomerAccountRelDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    private Long customerAccountId;

    /**
     * 实体类型：1-线索、2-商机、3-客户
     */
    private Integer entityType;

    /**
     * 实体ID
     */
    private Long entityId;

    /**
     * 是否主要联系方式：0-否、1-是
     */
    private Integer isPrimary;
}